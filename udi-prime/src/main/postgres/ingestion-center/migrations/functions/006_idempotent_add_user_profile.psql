-- Create the function with exception handling and logging
CREATE OR REPLACE FUNCTION drh_stateless_research_study.create_organization(
    p_name character varying, 
    p_identifier_system_value jsonb, 
    p_alias character varying, 
    p_type_code varchar, 
    p_type_display varchar, 
    p_city varchar, 
    p_state varchar, 
    p_country varchar, 
    p_website_url text, 
    p_createdby text DEFAULT NULL::text, 
    p_latitude text DEFAULT NULL::text, 
    p_longitude text DEFAULT NULL::text
) 
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result jsonb;
    latitude_float float8;
    longitude_float float8;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_research_study.create_organization'; 
    current_query text := pg_catalog.current_query(); 
    organization_id text;
    org_party_id text;
    location_id text;
    location_insert_id text; -- Variable to store the ID of the newly inserted location, used for verifying the insertion was successful
    org_type_id text;
    cleaned_type_ids TEXT[];  -- Renamed variable for clarity and to avoid conflict    
    existing_types TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    -- Initialize result to failure by default in case of an error
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during organization creation');

    parameters_lst := jsonb_build_object(
        'p_name', p_name,
        'p_identifier_system_value', p_identifier_system_value,
        'p_alias', p_alias,
        'p_type_code', p_type_code,
        'p_type_display', p_type_display,
        'p_city', p_city,
        'p_state', p_state,
        'p_country', p_country,
        'p_website_url', p_website_url,
        'p_createdby', p_createdby,
        'p_latitude', p_latitude,
        'p_longitude', p_longitude
    );
    
    -- Convert latitude and longitude if provided
    IF p_latitude IS NOT NULL AND p_longitude IS NOT NULL THEN
        BEGIN
            latitude_float := CAST(p_latitude AS float8);
            longitude_float := CAST(p_longitude AS float8);
        EXCEPTION WHEN invalid_text_representation THEN
            result := jsonb_build_object(
                'status', 'failure',
                'message', 'Invalid latitude or longitude format'
            );
            RETURN result;
        END;
    END IF;

    -- Check if the organization already exists by name
    IF EXISTS (
        SELECT 1
        FROM drh_stateless_research_study.organization_party_view
        WHERE organization_name = p_name
    ) THEN
        -- Return failure if the organization already exists
        result := jsonb_build_object('status', 'failure', 'message', 'Organization already exists');
        RETURN result;
    ELSE
        BEGIN
            -- Insert into the party table
            INSERT INTO drh_stateful_party.party
            (
                party_id,
                party_type_id,
                party_name,
                elaboration,
                created_at,
                created_by,
                updated_at,
                updated_by,
                deleted_at,
                deleted_by,
                activity_log
            )
            VALUES (
                drh_stateless_util.get_unique_id(),
                (SELECT party_type_id FROM drh_stateful_party.party_type WHERE code = 'ORGANIZATION'),
                p_name,
                NULL,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                p_createdby,
                NULL,
                NULL,
                NULL,
                NULL,
                NULL
            ) returning party_id into org_party_id;
            
            -- Insert the new organization into the organization table
            INSERT INTO drh_stateful_research_study.organization (
                id,
                party_id,
                identifier_system_value,
                "name",
                alias,
                type_code,
                type_display,
                address_text,
                address_line,
                city,
                state,
                postal_code,
                country,
                phone,
                email,
                website_url,
                parent_organization_id,
                rec_status_id,
                created_at,
                created_by
            ) 
            VALUES (
                drh_stateless_util.get_unique_id(),
                org_party_id,
                p_identifier_system_value,
                p_name,
                p_alias,
                NULL,
                p_type_display,
                NULL,
                NULL,
                p_city,
                p_state,
                NULL,
                p_country,
                NULL,
                NULL,
                p_website_url,
                NULL,
                (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                p_createdby
            ) RETURNING id INTO organization_id;

           -- Clean and extract organization type codes (remove quotes, trim, and ensure uniqueness)
           IF p_type_display IS NOT NULL AND p_type_display != '' THEN
               cleaned_type_ids := ARRAY(
                   SELECT TRIM(lower(REPLACE(unnest_type_id, '"', '')))  -- Renaming type_record to unnest_type_id
                   FROM unnest(string_to_array(p_type_display, ',')) AS unnest_type_id
                   WHERE unnest_type_id IS NOT NULL AND unnest_type_id != ''
               );
               
               -- Insert the unique organization types (if they do not already exist)
               INSERT INTO drh_stateful_master.organization_type 
               (organization_type_id, code, system_uri, display, description, rec_status_id, created_at, created_by)
               SELECT 
                   drh_stateless_util.get_unique_id(),
                   unnest_type_id,
                   NULL, -- Specify the URI if necessary
                   unnest_type_id,
                   NULL, -- Description if necessary
                   (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
                   CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                   p_createdby
               FROM unnest(cleaned_type_ids) AS unnest_type_id
               WHERE NOT EXISTS (
                   SELECT 1 FROM drh_stateful_master.organization_type ot
                   WHERE ot.code = unnest_type_id
               );
               
               FOR i IN 1..array_length(cleaned_type_ids, 1) LOOP
			        DECLARE
			            org_type_id text;
			        BEGIN
			            -- Get the ULID for each cleaned type code from the organization_type table
			            SELECT organization_type_id
			            INTO org_type_id
			            FROM drh_stateful_master.organization_type
			            WHERE code = cleaned_type_ids[i];
			
			            -- If the ULID was found, update the organization table's type_code with the new ULID			            
				        IF org_type_id IS NOT NULL THEN
				            -- Only append a comma if type_code is not empty
				            UPDATE drh_stateful_research_study.organization
				            SET type_code = 
				                CASE
				                    WHEN type_code IS NULL OR type_code = '' THEN org_type_id
				                    ELSE type_code || ',' || org_type_id
				                END
				            WHERE id = organization_id;
				        END IF;
			        END;
			    END LOOP;
           END IF;

           -- Insert into the location table if latitude and longitude are provided
           IF latitude_float IS NOT NULL AND longitude_float IS NOT NULL THEN
               location_id := drh_stateless_util.generate_unique_id();
               INSERT INTO drh_stateful_research_study.location (
                   id,
                   "name",
                   alias,
                   description,
                   type_code,
                   type_display,
                   address_line,
                   city,
                   state,
                   postal_code,
                   country,
                   longitude,
                   latitude,
                   managing_org_id,
                   part_of_id,
                   characteristic_code,
                   characteristic_display,
                   rec_status_id,
                   created_at,
                   created_by,
                   updated_at,
                   updated_by,
                   deleted_at,
                   deleted_by
               )
               VALUES (
                   location_id,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   p_city,
                   p_state,
                   NULL,
                   p_country,
                   longitude_float,
                   latitude_float,
                   organization_id,
                   NULL,
                   NULL,
                   NULL,
                   (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
                   CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                   p_createdby,
                   NULL,
                   NULL,
                   NULL,
                   NULL
               ) RETURNING id INTO location_insert_id;
              
              --check data inserted correctly in location table
              IF location_insert_id IS null THEN
              	result := jsonb_build_object('status', 'failure', 'message', 'Failed to insert location details');
              	RETURN result;
              END IF;
           END IF;

           -- Set the result to success only after all insertions are completed
           result := jsonb_build_object(
               'status', 'success',
               'message', 'Organization created successfully',
               'organization_id', organization_id,
               'organization_party_id', org_party_id
              
           );
        END;

    END IF;

    -- Return success result
    RETURN result;
EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during organization creation', 'error_details', error_details_json);
    RETURN result;
END;
$function$;



--------------------------------------------------------------------------------------------------------------------------------------------
-- Ensuring that the view is created in an idempotent manner by first checking if it already exists and dropping it.
-- This will avoid errors if the view is created more than once.

-- Drop the view if it already exists
DROP VIEW IF EXISTS drh_stateless_research_study.organization_party_view CASCADE;

-- Create the view
CREATE VIEW drh_stateless_research_study.organization_party_view 
WITH(security_invoker=true) AS
SELECT
    o.id AS organization_id,                 -- The unique identifier for the organization.
    p.party_id AS organization_party_id,     -- The party ID related to the organization, based on matching the organization name with the party name.
    o."name" AS organization_name,            -- The official name of the organization.
    o.alias AS organization_alias,           -- Any alternative name(s) or alias(es) the organization may use.
    o.type_code AS organization_type_code,   -- The type code of the organization (e.g., research lab, healthcare provider).
    o.type_display AS organization_type_display, -- A human-readable display name for the type of organization (e.g., "Research Lab", "Healthcare Provider").
    o.city AS organization_city,             -- The city where the organization is located.
    o.state AS organization_state,           -- The state or province where the organization is located.
    o.country AS organization_country,       -- The country where the organization is located.
    o.website_url  as organization_website_url
    
FROM
    drh_stateful_research_study.organization o   -- The table storing the organization details.

JOIN
    drh_stateful_party.party p                    -- The table storing the party (organization-related entity) details.
    ON p.party_name = o."name"                    -- Joining on the party name matching the organization name.

WHERE
    o.rec_status_id = 1;                          -- Filter to only include organizations that are currently active (based on rec_status_id being 1).

----------------------------------------------------------------------------------------------------------------

-- Drop the view if it already exists
DROP VIEW IF EXISTS drh_stateless_research_study.user_profile_view;
DROP VIEW IF EXISTS drh_stateless_authentication.user_profile_view;

CREATE OR REPLACE VIEW drh_stateless_authentication.user_profile_view 
WITH (security_invoker=true) AS
SELECT 
    p.id AS practitioner_id,
    p.practitioner_party_id,
    p.tenant_id,
    (SELECT opv.organization_party_id
     FROM drh_stateless_research_study.organization_party_view opv
     WHERE opv.organization_id = p.tenant_id
     LIMIT 1) AS organization_party_id,
    ua.email AS user_account_primary_email,
    ua.username,
    ua.first_name,
    ua.profile_status AS profile_status_id,
    (SELECT pstv.code
     FROM drh_stateful_master.profile_status_type pstv
     WHERE pstv.profile_status_type_id = ua.profile_status
     LIMIT 1) AS profile_status,
    em.auth_provider,
    em.provider_user_id,
    array_agg(
        CASE
            WHEN t.contact_point_system_id = cps.id AND t.contact_point_use_type_id = cpu.id THEN t.telecom_value
            ELSE NULL::character varying
        END
    ) AS work_emails,
    ua.metadata AS user_metadata,
    p.created_at
FROM 
    drh_stateful_research_study.practitioner p
JOIN 
    drh_stateful_party.party pt ON pt.party_id = p.practitioner_party_id  
JOIN 
    drh_stateful_authentication.user_account ua ON ua.party_id = pt.party_id  AND ua.deleted_at IS NULL
LEFT JOIN 
    drh_stateful_research_study.telecom t ON t.party_id = pt.party_id
LEFT JOIN 
    drh_stateful_authentication.auth_mappings em ON em.user_id = ua.user_id
LEFT JOIN 
    drh_stateful_master.contact_point_system cps ON cps.code::text = 'email'::text
LEFT JOIN 
    drh_stateful_master.contact_point_use cpu ON cpu.code::text = 'work'::text
WHERE 
    pt.party_type_id = (SELECT party_type.party_type_id
                         FROM drh_stateful_party.party_type
                         WHERE party_type.code = 'PERSON'::text)
GROUP BY 
    p.id, pt.party_id, p.tenant_id, ua.email, ua.username, ua.first_name, ua.profile_status, ua.metadata, em.auth_provider, em.provider_user_id;


---------------------------------------------------------------------------------------
-----------------------USERS LIST WITH ROLES-------------------------------------------
---------------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_research_study.user_list_view CASCADE;
DROP VIEW IF EXISTS drh_stateless_authentication.user_list_view CASCADE;

CREATE OR REPLACE VIEW drh_stateless_authentication.user_list_view
WITH (security_invoker = true)
AS SELECT 
    ua.user_id,
    ua.party_id,
    p.org_party_id as organization_party_id,
    p.tenant_id AS tenant_id,
    o.name as organization_name,
    ua.email AS user_account_primary_email,
    ua.username,
    ua.first_name,
    ua.last_name,
    ua.profile_status AS profile_status_id,
    (SELECT pstv.code
     FROM drh_stateful_master.profile_status_type pstv
     WHERE pstv.profile_status_type_id = ua.profile_status
     LIMIT 1) AS profile_status,
    em.auth_provider,
    em.provider_user_id,
    array_agg(
        CASE
            WHEN t.contact_point_system_id = cps.id AND t.contact_point_use_type_id = cpu.id THEN t.telecom_value
            ELSE NULL::character varying
        END) AS work_emails,
    jsonb_agg(DISTINCT jsonb_build_object('role_id', r.role_id, 'role_name', r.role_name)) AS user_roles,
    ua.metadata AS user_metadata,
    ua.last_login_at
FROM drh_stateful_authentication.user_account ua
    JOIN drh_stateful_party.party pt ON pt.party_id = ua.party_id
    LEFT JOIN drh_stateful_research_study.practitioner p on p.practitioner_party_id = ua.party_id
    LEFT JOIN drh_stateful_research_study.organization o ON o.party_id = p.org_party_id
    LEFT JOIN drh_stateful_research_study.telecom t ON t.party_id = pt.party_id
    LEFT JOIN drh_stateful_authentication.auth_mappings em ON em.user_id = ua.user_id
    LEFT JOIN drh_stateful_master.contact_point_system cps ON cps.code::text = 'email'::text
    LEFT JOIN drh_stateful_master.contact_point_use cpu ON cpu.code::text = 'work'::text
    LEFT JOIN drh_stateful_authentication.user_role ur ON ur.user_id = ua.user_id AND ur.deleted_at IS NULL
    LEFT JOIN drh_stateful_master.role r ON r.role_id = ur.role_id
WHERE pt.party_type_id = (
    SELECT party_type.party_type_id
    FROM drh_stateful_party.party_type
    WHERE party_type.code = 'PERSON'::text
)
    AND em.auth_provider !='vanna'
    AND r.role_name !='Super Admin'
GROUP BY 
    ua.user_id,
    ua.party_id,
    ua.email,
    ua.username,
    ua.first_name,
    ua.last_name,
    ua.profile_status,
    ua.metadata,
    ua.last_login_at,
    em.auth_provider,
    em.provider_user_id,
    p.org_party_id,
    p.tenant_id,
    o.name;

------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_research_study.create_practitioner_profile(text, text, text[], text, text, text);
DROP FUNCTION IF EXISTS drh_stateless_research_study.create_practitioner_profile(text, text, text[], text, text, text, text);
DROP FUNCTION IF EXISTS drh_stateless_research_study.create_practitioner_profile(text, text, text[], text, text, text, text, jsonb);
CREATE OR REPLACE FUNCTION drh_stateless_research_study.create_practitioner_profile(p_fullname text, p_source text, p_email text[], p_organization_party_id text, p_orcid text DEFAULT NULL::text, p_github text DEFAULT NULL::text, p_user_name text DEFAULT NULL::text, p_activity_json JSONB DEFAULT NULL::JSONB)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
    DECLARE
        result JSONB;
        practitioner_id TEXT;
        v_practitioner_party_id TEXT;  
        p_createdby TEXT;
        org_id TEXT;  
        user_account_id TEXT;
        user_exists BOOLEAN;    
        user_auth_id TEXT;
        v_email TEXT;
       	v_username TEXT;
        existing_record RECORD;
        err_context TEXT;
        err_state TEXT;
        err_message TEXT;
        err_detail TEXT;
        err_hint TEXT;
        error_details_json JSONB;
        function_name TEXT := 'drh_stateless_research_study.create_practitioner_profile';
        current_query TEXT := pg_catalog.current_query();
        exception_log_json JSONB;
        parameters_lst JSONB;
       
       	--Activity log variables
       	v_activity_log_json JSONB;
	    v_activity_level_id TEXT;
	    v_activity_type_id TEXT;
    BEGIN
        parameters_lst := jsonb_build_object(
        'p_fullname', p_fullname,
        'p_source', p_source,
        'p_email', p_email,
        'p_organization_party_id', p_organization_party_id,
        'p_orcid', p_orcid,
        'p_github', p_github,
        'p_user_name', p_user_name
        );

        
	   v_username := CASE 
            WHEN p_user_name IS NOT NULL THEN p_user_name
            WHEN p_email IS NOT NULL AND array_length(p_email, 1) > 0 THEN p_email[1]
            ELSE p_fullname
        END;
       
       -- Check if any email already exists in user_account table
        IF EXISTS (
            SELECT 1 
            FROM drh_stateful_authentication.user_account 
            WHERE email = ANY(p_email)
        ) THEN
            RETURN jsonb_build_object(
                'status', 'error',
                'message', 'One or more email addresses are already registered.'
            );
        END IF;
       
	    -- Validate p_source
        IF p_source NOT IN ('ORCiD', 'GitHub') THEN
            RETURN jsonb_build_object(
                'status', 'error',
                'message', 'Invalid source. Supported sources are ORCiD and GitHub.'
            );
        END IF;

        -- Fetch organization ID
        SELECT organization_id INTO org_id
        FROM drh_stateless_research_study.organization_party_view
        WHERE organization_party_id = p_organization_party_id;

        IF org_id IS NULL THEN
            RETURN jsonb_build_object(
                'status', 'error',
                'message', 'Organization not found.'
            );
        END IF;

        -- Check if user exists
        SELECT EXISTS (
            SELECT 1
            FROM drh_stateless_authentication.user_profile_view
            WHERE auth_provider = p_source
            AND provider_user_id = CASE
                                        WHEN p_source = 'ORCiD' THEN p_orcid
                                        WHEN p_source = 'GitHub' THEN p_github
                                    END
        ) INTO user_exists;

        IF user_exists THEN
            RETURN jsonb_build_object(
                'status', 'error',
                'message', 'User already exists.'
            );
        END IF;

        

        -- Begin transaction for atomicity
        BEGIN
            -- Insert practitioner party
            INSERT INTO drh_stateful_party.party (
                party_id, party_type_id, party_name, elaboration, created_at, created_by, 
                updated_at, updated_by, deleted_at, deleted_by, activity_log
            )
            VALUES (
                drh_stateless_util.get_unique_id(),
                (SELECT party_type_id FROM drh_stateful_party.party_type WHERE code = 'PERSON'),
                p_fullname,
                NULL, 
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                NULL,
                NULL, 
                NULL,
                NULL, 
                NULL,
                NULL
            )
            RETURNING party_id INTO v_practitioner_party_id;
        
            p_createdby := v_practitioner_party_id;

            -- Insert practitioner
            INSERT INTO drh_stateful_research_study.practitioner 
            (id, system_identifier, "name", gender_type_id, birth_date, photo_url, org_party_id, tenant_id, practitioner_party_id, rec_status_id,  created_at, created_by, updated_at, updated_by, deleted_at, deleted_by) 
                VALUES 
                (
                    drh_stateless_util.get_unique_id(),
                    CASE
                        WHEN p_source = 'ORCiD' THEN p_orcid
                        WHEN p_source = 'GitHub' THEN p_github
                    END,
                    p_fullname, 
                    (SELECT gender_type_id FROM drh_stateless_master.gender_type_view WHERE code = 'UNKNOWN' LIMIT 1),  
                    NULL,  
                    NULL,  
                    p_organization_party_id,
                    org_id, 
                    v_practitioner_party_id,
                    (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),  
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  
                    p_createdby,  
                    NULL,  
                    NULL,  
                    NULL,
                    NULL
                )
                RETURNING id INTO practitioner_id;

            -- Insert user account
            INSERT INTO drh_stateful_authentication.user_account (
                    user_id, username, email, first_name, last_name, last_login_at, 
                    profile_status, metadata, rec_status_id, created_at, created_by, 
                    updated_at, updated_by, deleted_at, deleted_by,party_id ,org_party_id
                )
                VALUES (
                    drh_stateless_util.get_unique_id(), 
                    v_username , 
                    ' ', 
                    p_fullname, 
                    NULL, 
                    NULL,
                    (SELECT profile_status_type_id FROM drh_stateless_master.profile_status_type_view WHERE code = 'COMPLETE' LIMIT 1),
                    NULL,
                    (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 
                    p_createdby, 
                    NULL, 
                    NULL, 
                    NULL, 
                    NULL,
                    v_practitioner_party_id,
                    p_organization_party_id
                    
                )
                RETURNING user_id INTO user_account_id;

            -- Insert into external_auth_mappings table
            INSERT INTO drh_stateful_authentication.auth_mappings (
                    id, user_id, auth_provider, provider_user_id, access_token, refresh_token, 
                    status, rec_status_id, created_at, updated_at
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),  
                    user_account_id,  
                    p_source,
                    CASE
                        WHEN p_source = 'ORCiD' THEN p_orcid
                        WHEN p_source = 'GitHub' THEN p_github
                    END,
                    '',  
                    '',  
                    'ACTIVE',  
                    (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),  
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                )
                RETURNING id INTO user_auth_id;

            -- Store ORCiD in metadata if using GitHub
            IF p_source = 'GitHub' AND p_orcid IS NOT NULL THEN            
                UPDATE drh_stateful_authentication.user_account
                SET 
                    metadata = jsonb_build_object(
                        'identifier', 'ORCiD',  -- Set identifier key to ORCiD
                        'value', p_orcid         -- Set value key to the provided ORCiD value (parameter p_orcid)
                    )
                WHERE user_id = user_account_id;  -- Provide the actual user ID here        

            END IF;

        
            -- Update email in user_account table if provided
                IF p_email IS NOT NULL AND array_length(p_email, 1) > 0 THEN
                    UPDATE drh_stateful_authentication.user_account
                    SET email = p_email[1]
                    WHERE user_id = user_account_id;
                END IF;

                -- Insert emails into the telecom table
                IF p_email IS NOT NULL THEN
                    FOREACH v_email IN ARRAY p_email LOOP
                        INSERT INTO drh_stateful_research_study.telecom 
                        (id, party_id, telecom_type, telecom_value, telecom_use, contact_point_system_id, contact_point_use_type_id, tenant_id, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by)
                        VALUES (
                            drh_stateless_util.get_unique_id(), 
                            v_practitioner_party_id, 
                            'email',
                            v_email,
                            NULL,
                            (SELECT id FROM drh_stateless_master.contact_point_system_view WHERE code = 'email' LIMIT 1), 
                            (SELECT id FROM drh_stateless_master.contact_point_use_view WHERE code = 'work' LIMIT 1),
                            org_id, 
                            (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),  
                            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  
                            p_createdby,
                            NULL, 
                            NULL, 
                            NULL, 
                            NULL
                        );
                    END LOOP;
                END IF;

                -- Insert user role into user_role table
                INSERT INTO drh_stateful_authentication.user_role (
                    user_role_id, user_id, role_id, rec_status_id, created_at, created_by, updated_at, updated_by, deleted_at, deleted_by
                )
                VALUES (
                    drh_stateless_util.get_unique_id(),
                    user_account_id,
                    (SELECT role_id FROM drh_stateful_master.role where role_name = 'Researcher' LIMIT 1),
                    (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                    p_createdby,
                    NULL,
                    NULL,
                    NULL,
                    NULL
                );

            IF p_activity_json ? 'session_id' THEN 
                --Fetch level and type of activity
                SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
                SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='CREATE_PRACTITIONER_PROFILE' AND deleted_at IS NULL;
        
                -- Create new activity log JSON with the required fields
                v_activity_log_json := p_activity_json || jsonb_build_object(
                    'activity_type_id', v_activity_type_id,
                    'activity_level_id', v_activity_level_id,
                    'activity_name', 'Create Practitioner Profile',
                    'activity_description', 'Practitioner profile created successfully.'
                );
        
                --Add activity log
                PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_createdby,v_activity_log_json);   
            END IF;    
            -- Return success response
            RETURN jsonb_build_object(
                'status', 'success',
                'message', 'User successfully created.',
                'practitioner_id', practitioner_id,
                'practitioner_party_id', v_practitioner_party_id,
                'user_account_id', user_account_id,
                'organization_party_id', p_organization_party_id
            );
        END;
    EXCEPTION WHEN OTHERS THEN
        -- Capture error details
        GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                                err_state = RETURNED_SQLSTATE,
                                err_message = MESSAGE_TEXT,
                                err_detail = PG_EXCEPTION_DETAIL,
                                err_hint = PG_EXCEPTION_HINT;

        -- Log the error details       


        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst
        );

        -- Log exception (assuming your logging function)
        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Prepare error JSON
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        -- Return failure with the error details
        result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during practitioner creation', 'error_details', error_details_json);
        RETURN result;
    END;
    $function$
;



----------------------------------------------------------------------------------------------
---------------------------CREATE USER PROFILE------------------------------------------------
----------------------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_research_study.create_user_profile;
DROP FUNCTION IF EXISTS drh_stateless_authentication.create_user_profile;
DROP FUNCTION IF EXISTS drh_stateless_authentication.create_user_profile(
    text, text, text[], text, text, text, text, text
);
DROP FUNCTION IF EXISTS drh_stateless_authentication.create_user_profile(
    text, text, text[], text, text, text, text, text, jsonb
);


CREATE OR REPLACE FUNCTION drh_stateless_authentication.create_user_profile(
    p_fullname text, p_source text, p_email text[], 
    p_organization_party_id text,
    p_role_id text,
    p_orcid text DEFAULT NULL::text, 
    p_github text DEFAULT NULL::text,
    user_name text DEFAULT NULL::text,
    p_activity_json JSONB DEFAULT NULL::JSONB
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    practitioner_id TEXT;
    patient_id TEXT;
    p_createdby TEXT;
    org_id TEXT;  
    user_account_id TEXT;
    user_exists BOOLEAN;    
    user_auth_id TEXT;
    v_email TEXT;
    v_username TEXT;
    existing_record RECORD;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_authentication.create_user_profile';
    current_query TEXT := pg_catalog.current_query();
    v_role_name TEXT;
    v_active_status_id INT;
    v_practitioner_party_id TEXT :=drh_stateless_util.get_unique_id();
    exception_log_json JSONB;
    parameters_lst JSONB;

    -- Activity log variables
    v_activity_log_json JSONB;
    v_activity_level_id TEXT;
    v_activity_type_id TEXT;
BEGIN
    parameters_lst := jsonb_build_object(
        'p_fullname', p_fullname,        
        'p_organization_party_id', p_organization_party_id,
        'p_role_id', p_role_id,
        'p_orcid', p_orcid,
        'p_github', p_github,
        'user_name', user_name
    );

    -- Get role name
    SELECT role_name INTO v_role_name 
    FROM drh_stateful_master.role 
    WHERE role_id = p_role_id;
    --validate role
    IF v_role_name IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Invalid role ID provided.'
        );
    END IF;

    -- Validate p_source
    IF p_source NOT IN ('ORCiD', 'GitHub') THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Invalid source. Supported sources are ORCiD and GitHub.'
        );
    END IF;

    -- Validate email array
    IF p_email IS NULL OR array_length(p_email, 1) = 0 THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'At least one email address is required.'
        );
    END IF;

    -- Check if email already exists
    SELECT EXISTS (
        SELECT 1 FROM drh_stateful_authentication.user_account 
        WHERE email = ANY(p_email)
    ) INTO user_exists;

    IF user_exists THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Email already exists.'
        );
    END IF;

    -- Fetch organization ID
    SELECT organization_id INTO org_id
    FROM drh_stateless_research_study.organization_party_view
    WHERE organization_party_id = p_organization_party_id;

    IF org_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Organization not found.'
        );
    END IF;

    -- Set username
    v_username := COALESCE(user_name, p_email[1]);
    SELECT rs.value INTO v_active_status_id FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE';

    

    -- Begin transaction
    BEGIN
        -- Insert party record
        INSERT INTO drh_stateful_party.party (
            party_id, party_type_id, party_name, 
            created_at
        )
        VALUES (
            v_practitioner_party_id,
            (SELECT party_type_id FROM drh_stateful_party.party_type WHERE code = 'PERSON'),
            p_fullname,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );
    
        p_createdby := v_practitioner_party_id;

        -- Role-specific insertions
        IF v_role_name = 'Researcher' THEN
            -- Insert practitioner record
            INSERT INTO drh_stateful_research_study.practitioner 
            (id, system_identifier, "name", gender_type_id, org_party_id, tenant_id, practitioner_party_id, rec_status_id, created_at, created_by) 
            VALUES (
                drh_stateless_util.get_unique_id(),
                CASE
                    WHEN p_source = 'ORCiD' THEN p_orcid
                    WHEN p_source = 'GitHub' THEN p_github
                END,
                p_fullname, 
                (SELECT gender_type_id FROM drh_stateless_master.gender_type_view WHERE code = 'UNKNOWN' LIMIT 1),  
                p_organization_party_id, org_id, 
                v_practitioner_party_id,
                v_active_status_id,  
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC', p_createdby
            )
            RETURNING id INTO practitioner_id;

        ELSIF v_role_name = 'Patient' THEN
            -- Insert patient record
            INSERT INTO drh_stateful_research_study.patient
            (id, gender_type_id,org_party_id, tenant_id, rec_status_id,
             created_at, created_by)
            VALUES (
                drh_stateless_util.get_unique_id(),
                (SELECT gender_type_id FROM drh_stateless_master.gender_type_view WHERE code = 'UNKNOWN' LIMIT 1),
                p_organization_party_id, 
                org_id,
                v_active_status_id,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC', p_createdby
            )
            RETURNING id INTO patient_id;
            
        END IF;

        -- Insert user account for all roles
        INSERT INTO drh_stateful_authentication.user_account (
            user_id, username, email, first_name, 
            profile_status, rec_status_id, created_at, created_by,
            party_id
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            v_username,
            p_email[1],
            p_fullname,
            (SELECT profile_status_type_id FROM drh_stateless_master.profile_status_type_view WHERE code = 'COMPLETE' LIMIT 1),
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_createdby,
            v_practitioner_party_id
        )
        RETURNING user_id INTO user_account_id;        

        -- Insert external auth mappings for all roles
        INSERT INTO drh_stateful_authentication.auth_mappings (
            id, user_id, auth_provider, provider_user_id, access_token,
            refresh_token, status, rec_status_id, created_at, updated_at
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            p_source,
            CASE
                WHEN p_source = 'ORCiD' THEN p_orcid
                WHEN p_source = 'GitHub' THEN p_github
            END,   
            '','',
            'ACTIVE',
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        )
        RETURNING id INTO user_auth_id;

        -- Store ORCiD in metadata if using GitHub
            IF p_source = 'GitHub' AND p_orcid IS NOT NULL THEN            
                UPDATE drh_stateful_authentication.user_account
                SET 
                    metadata = jsonb_build_object(
                        'identifier', 'ORCiD',  -- Set identifier key to ORCiD
                        'value', p_orcid         -- Set value key to the provided ORCiD value (parameter p_orcid)
                    )
                WHERE user_id = user_account_id;  -- Provide the actual user ID here        

            END IF;

        -- Insert emails into the telecom table
        IF p_email IS NOT NULL THEN
            FOREACH v_email IN ARRAY p_email LOOP
                INSERT INTO drh_stateful_research_study.telecom 
                (id, party_id, telecom_type, telecom_value, contact_point_system_id, contact_point_use_type_id, tenant_id, rec_status_id, created_at, created_by)
                VALUES (
                    drh_stateless_util.get_unique_id(), 
                    v_practitioner_party_id,
                    'email', 
                    v_email,
                    (SELECT id FROM drh_stateless_master.contact_point_system_view WHERE code = 'email' LIMIT 1), 
                    (SELECT id FROM drh_stateless_master.contact_point_use_view WHERE code = 'work' LIMIT 1),
                    org_id, 
                    v_active_status_id,  
                    CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  
                    p_createdby
                );
            END LOOP;
        END IF;

        -- Insert user role
        INSERT INTO drh_stateful_authentication.user_role (
            user_role_id, user_id, role_id, rec_status_id,
            created_at, created_by, updated_at, updated_by, deleted_at, deleted_by
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            p_role_id,
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_createdby,
            NULL, NULL, NULL, NULL
        );
		end;

        -- Activity log feature
        IF p_activity_json IS NOT NULL THEN
            -- Fetch level and type of activity
            SELECT id INTO v_activity_level_id FROM drh_stateful_master.activity_level WHERE title ='DB_LEVEL_LOG' AND deleted_at IS NULL;
            SELECT id INTO v_activity_type_id FROM drh_stateful_master.activity_type WHERE code ='CREATE_USER_PROFILE' AND deleted_at IS NULL;

            -- Create new activity log JSON with the required fields
            v_activity_log_json := p_activity_json || jsonb_build_object(
                'activity_type_id', v_activity_type_id,
                'activity_level_id', v_activity_level_id,
                'activity_name', 'Create User Profile',
                'activity_description', format('User profile created for %s', v_username)
            );

            -- Add activity log
            PERFORM drh_stateless_activity_audit.insert_activity_log_by_session(p_createdby, v_activity_log_json);
        END IF;
        
        -- Return success response
        RETURN jsonb_build_object(
            'status', 'success',
            'message', 'User successfully created.',
            'party_id', v_practitioner_party_id,
            'user_account_id', user_account_id,
            'organization_party_id', p_organization_party_id
        );

    EXCEPTION WHEN OTHERS THEN
        -- Error handling
        GET STACKED DIAGNOSTICS 
            err_context = PG_EXCEPTION_CONTEXT,
            err_state = RETURNED_SQLSTATE,
            err_message = MESSAGE_TEXT,
            err_detail = PG_EXCEPTION_DETAIL,
            err_hint = PG_EXCEPTION_HINT;

        -- Log error
        exception_log_json := jsonb_build_object(
            'function_name', function_name,
            'error_code', err_state,
            'error_message', err_message,
            'error_detail', err_detail,
            'error_hint', err_hint,
            'error_context', err_context,
            'query', current_query,
            'parameters', parameters_lst  
        );

        PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

        -- Return error response
        error_details_json := jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        );

        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'Error occurred during user creation',
            'error_details', error_details_json
        );
    END;
$function$;



 -------------------------------------------------------------------------------------

 CREATE OR REPLACE FUNCTION drh_stateless_research_study.revert_practitioner_profile_for_author_inv(p_study_display_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    p_user_account_id TEXT;
    p_practitioner_party_id TEXT;
    p_practitioner_id TEXT;
    inv_name TEXT;
    inv_role text;
    deleted_usernames TEXT[] := ARRAY[]::TEXT[];
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_research_study.revert_practitioner_profile_for_author_inv';
    current_query TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    RAISE NOTICE 'Starting revert process for study_display_id: %', p_study_display_id;

    parameters_lst := jsonb_build_object(
        'p_study_display_id', p_study_display_id        
    );

    -- Loop over distinct usernames from both investigators and authors
FOR inv_name, inv_role IN
    WITH combined_users AS (
        SELECT rsap.party_name AS username, rspr2.code AS user_role
        FROM drh_stateful_research_study.research_study_associated_party rsap        
        JOIN drh_stateful_research_study.research_study rs ON rs.study_id = rsap.research_study_id
        JOIN drh_stateful_master.research_study_party_role rspr2 
            ON rsap.party_role_type_id = rspr2.study_party_role_id
        WHERE rs.study_display_id = p_study_display_id
          AND rsap.party_role_type_id != (
              SELECT rspr.study_party_role_id
              FROM drh_stateful_master.research_study_party_role rspr
              WHERE rspr.code = 'sponsor' LIMIT 1
          )

        UNION

        SELECT rsap.first_name AS username, 'co-author' AS user_role
        FROM drh_stateful_research_study.citation_author rsap
        LEFT JOIN drh_stateful_research_study.citation c ON c.id = rsap.citation_id        
        JOIN drh_stateful_research_study.research_study rs ON rs.study_id = c.study_id
        WHERE rs.study_display_id = p_study_display_id
          AND rsap.role_id = (
              SELECT rspr.study_party_role_id
              FROM drh_stateful_master.research_study_party_role rspr
              WHERE rspr.code = 'co-author' LIMIT 1
          )
    )
    SELECT username, user_role FROM combined_users
LOOP
    RAISE NOTICE 'Processing username: %, role: %', inv_name, inv_role;

        -- Check if user account exists
        IF EXISTS (
            SELECT 1
            FROM drh_stateful_authentication.user_account ua2
            WHERE ua2.username = inv_name
        ) THEN
            -- Find user account ID and party ID
            SELECT ua.user_id, ua.party_id 
            INTO p_user_account_id, p_practitioner_party_id
            FROM drh_stateful_authentication.user_account ua
            WHERE ua.username = inv_name;

            IF p_user_account_id IS NULL THEN
                RAISE NOTICE 'User not found for username: %, skipping.', inv_name;
                CONTINUE;
            END IF;

            RAISE NOTICE 'Found user_id: %, party_id: %', p_user_account_id, p_practitioner_party_id;

            -- Get practitioner_id
            SELECT id INTO p_practitioner_id
            FROM drh_stateful_research_study.practitioner
            WHERE practitioner_party_id = p_practitioner_party_id;

            RAISE NOTICE 'Found practitioner_id: %', p_practitioner_id;
           
            delete from drh_stateful_authentication.auth_mappings where user_id =p_user_account_id;
           RAISE NOTICE 'Deleted from external_auth_mappings for user_id: %', p_user_account_id;

            -- Delete from user_role
            DELETE FROM drh_stateful_authentication.user_role
            WHERE user_id = p_user_account_id;
            RAISE NOTICE 'Deleted from user_role for user_id: %', p_user_account_id;

            -- Delete from telecom
            DELETE FROM drh_stateful_research_study.telecom
            WHERE party_id = p_practitioner_party_id;
            RAISE NOTICE 'Deleted from telecom for party_id: %', p_practitioner_party_id;

            -- Delete from user_account
            DELETE FROM drh_stateful_authentication.user_account
            WHERE user_id = p_user_account_id;
            RAISE NOTICE 'Deleted from user_account for user_id: %', p_user_account_id;

            -- Delete from practitioner
            DELETE FROM drh_stateful_research_study.practitioner
            WHERE id = p_practitioner_id;
            RAISE NOTICE 'Deleted from practitioner for id: %', p_practitioner_id;

            -- Delete from party
            DELETE FROM drh_stateful_party.party
            WHERE party_id = p_practitioner_party_id;
            RAISE NOTICE 'Deleted from party for party_id: %', p_practitioner_party_id;

            -- Track deleted username
            deleted_usernames := array_append(deleted_usernames, inv_name);
        ELSE
            RAISE NOTICE 'No user_account found for username: %, skipping.', inv_name;
        END IF;
    END LOOP;

    RAISE NOTICE 'Revert process completed. Deleted users: %', deleted_usernames;

    RETURN jsonb_build_object(
        'status', 'success',
        'message', 'Practitioner profile(s) reverted successfully.',
        'deleted_users', to_jsonb(deleted_usernames)
    );

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Return failure with error details
    error_details_json := jsonb_build_object(
        'error', err_state,
        'message', err_message,
        'detail', err_detail,
        'hint', err_hint
    );
    RAISE NOTICE 'Function failed: %', error_details_json;
END;
$function$
;

----------------------------------------------------------------------------------

CREATE OR REPLACE FUNCTION drh_stateless_authentication.remove_users_by_name(user_list TEXT[])
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    p_username TEXT;
    p_user_account_id TEXT;
    p_practitioner_party_id TEXT;
    p_practitioner_id TEXT;

    deleted_usernames TEXT[] := ARRAY[]::TEXT[];

    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_authentication.remove_users_by_name';
    current_query TEXT;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    parameters_lst := jsonb_build_object(
    'user_list', user_list
    );

    FOREACH p_username IN ARRAY user_list LOOP
        current_query := 'Processing user: ' || p_username;

        IF EXISTS (
            SELECT 1
            FROM drh_stateful_authentication.user_account ua2
            WHERE ua2.username = p_username
        ) THEN
            -- Find user account ID and party ID
            SELECT ua.user_id, ua.party_id 
            INTO p_user_account_id, p_practitioner_party_id
            FROM drh_stateful_authentication.user_account ua
            WHERE ua.username = p_username;

            IF p_user_account_id IS NULL THEN
                RAISE NOTICE 'User not found for username: %, skipping.', p_username;
                CONTINUE;
            END IF;

            RAISE NOTICE 'Found user_id: %, party_id: %', p_user_account_id, p_practitioner_party_id;

            -- Get practitioner_id (may not exist)
            SELECT id INTO p_practitioner_id
            FROM drh_stateful_research_study.practitioner
            WHERE practitioner_party_id = p_practitioner_party_id;

            RAISE NOTICE 'Found practitioner_id: %', p_practitioner_id;

            -- Delete from external_auth_mappings
            DELETE FROM drh_stateful_authentication.auth_mappings WHERE user_id = p_user_account_id;
            RAISE NOTICE 'Deleted from external_auth_mappings for user_id: %', p_user_account_id;

            -- Delete from user_role
            DELETE FROM drh_stateful_authentication.user_role WHERE user_id = p_user_account_id;
            RAISE NOTICE 'Deleted from user_role for user_id: %', p_user_account_id;

            -- Delete from telecom
            DELETE FROM drh_stateful_research_study.telecom WHERE party_id = p_practitioner_party_id;
            RAISE NOTICE 'Deleted from telecom for party_id: %', p_practitioner_party_id;

            -- Delete from user_account
            DELETE FROM drh_stateful_authentication.user_account WHERE user_id = p_user_account_id;
            RAISE NOTICE 'Deleted from user_account for user_id: %', p_user_account_id;

            -- Delete from practitioner
            IF p_practitioner_id IS NOT NULL THEN
                DELETE FROM drh_stateful_research_study.practitioner WHERE id = p_practitioner_id;
                RAISE NOTICE 'Deleted from practitioner for id: %', p_practitioner_id;
            END IF;

            -- Delete from party
            DELETE FROM drh_stateful_party.party WHERE party_id = p_practitioner_party_id;
            RAISE NOTICE 'Deleted from party for party_id: %', p_practitioner_party_id;

            -- Track deleted user
            deleted_usernames := array_append(deleted_usernames, p_username);
        END IF;
    END LOOP;

    RETURN jsonb_build_object(
        'status', 'success',
        'message', 'User(s) deleted successfully.',
        'deleted_users', to_jsonb(deleted_usernames)
    );

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    error_details_json := jsonb_build_object(
        'error', err_state,
        'message', err_message,
        'detail', err_detail,
        'hint', err_hint
    );

    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    RAISE NOTICE 'Function failed: %', error_details_json;
    RETURN jsonb_build_object('status', 'error', 'details', error_details_json);
END;
$function$;



/**
 * Function: drh_stateless_research_study.create_super_admin_account
 * 
 * Description:
 * This function creates a Super Admin account in the system. It performs various operations such as 
 * validating input parameters, checking for duplicate email addresses, creating associated records 
 * in multiple tables, and assigning the Super Admin role to the user. The function ensures that all 
 * operations are performed within a transaction and includes error handling to log and return 
 * detailed error information in case of failure.
 * 
 * Parameters:
 * - p_fullname (TEXT): The full name of the Super Admin user.
 * - p_email (TEXT): The email address of the Super Admin user. This is required and must be unique.
 * - p_organization_party_id (TEXT): The ID of the organization to which the Super Admin belongs.
 * - p_password (TEXT): The password for the Super Admin account. Can be plain text or encrypted.
 * - p_is_pass_encrypted (BOOLEAN): Indicates whether the provided password is already encrypted.
 * 
 * Returns:
 * - JSONB: A JSON object containing the status of the operation. On success, it includes details 
 *   such as the party ID, user account ID, and organization party ID. On failure, it includes 
 *   error details such as the error message, context, and SQL state.
 * 
 * Behavior:
 * 1. Validates the email address to ensure it is not null or empty.
 * 2. Checks if the email address already exists in the `user_account` table.
 * 3. Fetches the organization ID based on the provided organization party ID.
 * 4. Retrieves the active status ID from the `record_status` table.
 * 5. Inserts records into the following tables:
 *    - `party`: Creates a new party record for the Super Admin.
 *    - `user_account`: Creates a user account for the Super Admin.
 *    - `user_credentials`: Stores the user's credentials, encrypting the password if necessary.
 *    - `auth_mappings`: Adds external authentication mappings for the user.
 *    - `telecom`: Adds the user's email address to the telecom table.
 *    - `user_role`: Assigns the Super Admin role to the user.
 * 6. Returns a success response with relevant details if all operations succeed.
 * 7. Handles exceptions by logging the error details in the `exception_log` table and returning 
 *    a failure response with detailed error information.
 * 
 * Notes:
 * - The function uses the `drh_stateless_util.get_unique_id()` utility to generate unique IDs for 
 *   various records.
 * - The function is defined with `SECURITY DEFINER` to allow execution with the privileges of the 
 *   function owner.
 * - The function ensures transactional integrity by rolling back changes in case of any errors.
 */

--drop old function
DROP FUNCTION IF EXISTS drh_stateless_research_study.create_super_admin_account;
DROP FUNCTION IF EXISTS drh_stateless_authentication.create_super_admin_account;

CREATE OR REPLACE FUNCTION drh_stateless_authentication.create_super_admin_account(
    p_fullname TEXT,
    p_email TEXT,
    p_organization_party_id TEXT,
    p_password TEXT,
    p_is_pass_encrypted BOOLEAN
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    user_account_id TEXT;
    v_practitioner_party_id TEXT := drh_stateless_util.get_unique_id();
    p_createdby TEXT;
    org_id TEXT;
    v_active_status_id INT;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_authentication.create_super_admin_account';
    current_query TEXT := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    
    parameters_lst := jsonb_build_object(
        'p_fullname', p_fullname,
        'p_email', p_email,
        'p_organization_party_id', p_organization_party_id,
        'p_password', p_password,
        'p_is_pass_encrypted', p_is_pass_encrypted
    );

    -- Validate email
    IF p_email IS NULL OR p_email = '' THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Email address is required.'
        );
    END IF;

    -- Check if email already exists
    IF EXISTS (
        SELECT 1 FROM drh_stateful_authentication.user_account 
        WHERE email = p_email
    ) THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Email already exists.'
        );
    END IF;

    -- Fetch organization ID
    SELECT organization_id INTO org_id
    FROM drh_stateless_research_study.organization_party_view
    WHERE organization_party_id = p_organization_party_id;

    IF org_id IS NULL THEN
        RETURN jsonb_build_object(
            'status', 'error',
            'message', 'Organization not found.'
        );
    END IF;

    -- Get active status ID
    SELECT rs.value INTO v_active_status_id 
    FROM drh_stateful_party.record_status rs 
    WHERE rs.code = 'ACTIVE';

    
    -- Begin transaction
    BEGIN
        -- Insert party record
        INSERT INTO drh_stateful_party.party (
            party_id, party_type_id, party_name, 
            created_at
        )
        VALUES (
            v_practitioner_party_id,
            (SELECT party_type_id FROM drh_stateful_party.party_type WHERE code = 'PERSON'),
            p_fullname,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );

        p_createdby := v_practitioner_party_id;

        -- Insert user account
        INSERT INTO drh_stateful_authentication.user_account (
            user_id, username, email, first_name, 
            profile_status, rec_status_id, created_at, created_by,
            party_id
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            p_email,
            p_email,
            p_fullname,
            (SELECT profile_status_type_id FROM drh_stateless_master.profile_status_type_view WHERE code = 'COMPLETE' LIMIT 1),
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_createdby,
            v_practitioner_party_id
        )
        RETURNING user_id INTO user_account_id;

        -- Insert into user_credentials table
        INSERT INTO drh_stateful_authentication.user_credentials (
            id, user_id, password_hash, password_salt, password_updated_at
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            CASE
            WHEN p_is_pass_encrypted THEN p_password
            ELSE drh_stateless_util.crypt(p_password, drh_stateless_util.gen_salt('bf'))
            END,
            'bcrypt',
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );

        -- Insert external auth mappings
        INSERT INTO drh_stateful_authentication.auth_mappings (
            id, user_id, auth_provider, provider_user_id, access_token,
            refresh_token, status, rec_status_id, created_at, updated_at
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            'Local',
            p_email,
            '', '', 'ACTIVE',
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        );

        -- Insert email into the telecom table
        INSERT INTO drh_stateful_research_study.telecom (
            id, party_id, telecom_type, telecom_value, contact_point_system_id, contact_point_use_type_id, tenant_id, rec_status_id, created_at, created_by
        )
        VALUES (
            drh_stateless_util.get_unique_id(), 
            v_practitioner_party_id,
            'email', 
            p_email,
            (SELECT id FROM drh_stateless_master.contact_point_system_view WHERE code = 'email' LIMIT 1), 
            (SELECT id FROM drh_stateless_master.contact_point_use_view WHERE code = 'work' LIMIT 1),
            org_id, 
            v_active_status_id,  
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  
            p_createdby
        );

        -- Assign Super Admin role
        INSERT INTO drh_stateful_authentication.user_role (
            user_role_id, user_id, role_id, rec_status_id,
            created_at, created_by, updated_at, updated_by, deleted_at, deleted_by
        )
        VALUES (
            drh_stateless_util.get_unique_id(),
            user_account_id,
            (SELECT role_id FROM drh_stateful_master.role WHERE role_name = 'Super Admin' LIMIT 1),
            v_active_status_id,
            CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
            p_createdby,
            NULL, NULL, NULL, NULL
        );

        -- Return success response
        RETURN jsonb_build_object(
            'status', 'success',
            'message', 'Super Admin profile successfully created.',
            'party_id', v_practitioner_party_id,
            'user_account_id', user_account_id,
            'organization_party_id', p_organization_party_id
        );
    END;

EXCEPTION WHEN OTHERS THEN
    -- Error handling
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log error
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Return error response
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during Super Admin profile creation',
        'error_details', error_details_json
    );
END;
$function$;

-----------------------------------------------------------------------------------------------------------------------
------------------------------------------------CHECK EMAIL EXISTS-----------------------------------------------------
-----------------------------------------------------------------------------------------------------------------------
CREATE OR REPLACE FUNCTION drh_stateless_authentication.check_email_unique(p_email text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_authentication.check_email_unique';
    current_query TEXT := pg_catalog.current_query();
    email_exists BOOLEAN;
    exception_log_json JSONB;
    parameters_lst JSONB;

BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Email is unique','is_unique', true);

    parameters_lst := jsonb_build_object(
    'p_email', p_email    
    );

    -- Check if email exists in user_account table
    SELECT EXISTS (
        SELECT 1 
        FROM drh_stateful_authentication.user_account 
        WHERE email = p_email
    ) INTO email_exists;

    IF email_exists THEN
        result := jsonb_build_object(
            'status', 'success',
            'message', 'Email already exists',
            'email', p_email,
            'is_unique', false
        );
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    result := jsonb_build_object(
        'status', 'failure', 
        'message', 'Error occurred while checking email uniqueness', 
        'error_details', error_details_json
    );
    
    RETURN result;
END;
$function$;
