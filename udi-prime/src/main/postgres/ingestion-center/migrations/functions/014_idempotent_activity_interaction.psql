-- Create an idempotent view for activity_log
DROP VIEW IF EXISTS drh_stateless_activity_audit.vw_activity_log;
CREATE OR REPLACE VIEW drh_stateless_activity_audit.vw_activity_log 
WITH (security_invoker = true) AS
SELECT 
    al.activity_id,
    al.activity_name,    
    al.activity_description,
    al.root_id,
    al.parent_id,
    al.activity_hierarchy,
    al.hierarchy_path,
    al.request_url,
    al.organization_party_id,
    al.platform,
    al.environment,
    al.created_by,
    al.user_name,
    al.created_at,
    al.app_version,
    al.test_case,
    al.linkage_id,
    al.ip_address,
    al.location_latitude,
    al.location_longitude,
    al.activity_data,
    al.session_unique_id,
    alvl.level as activity_log_level,
    at.title as activity_type,
    al.activity_type_id,
    al.activity_level_id 
FROM drh_stateful_activity_audit.activity_log al
LEFT JOIN drh_stateful_master.activity_type at 
    ON al.activity_type_id = at.id
LEFT JOIN drh_stateful_master.activity_level alvl 
    ON al.activity_level_id = alvl.id;


CREATE OR REPLACE VIEW drh_stateless_activity_audit.v_hub_study_interaction 
WITH (security_invoker = true) AS
SELECT 
    hi.hub_interaction_id,
    hi.study_id AS hub_study_id,
    hi.organization_party_id AS hub_organization_party_id,
    hi.created_by AS hub_created_by,
    hi.updated_by AS hub_updated_by,
    hi.created_at AS hub_created_at,
    hi.updated_at AS hub_updated_at,
    si.study_interaction_id,
    si.hub_interaction_id AS study_hub_interaction_id,
    si.study_id AS study_study_id,
    si.organization_party_id AS study_organization_party_id,
    si.uri,
    si.interaction_type,
    si.description,
    si.request,
    si.response,
    si.from_state,
    si.to_state,
    si.status,
    si.response_code,
    si.error_response,
    si.created_by AS study_created_by,
    si.updated_by AS study_updated_by,
    si.created_at AS study_created_at,
    si.updated_at AS study_updated_at
FROM drh_stateful_activity_audit.hub_interaction hi
LEFT JOIN drh_stateful_activity_audit.study_interaction si
ON hi.hub_interaction_id = si.hub_interaction_id;


-- Drop view if it exists to ensure idempotency
DROP VIEW IF EXISTS drh_stateless_activity_audit.hub_interaction_view;

-- Create the view
CREATE OR REPLACE VIEW drh_stateless_activity_audit.hub_interaction_view 
WITH (security_invoker = true) AS 
SELECT 
    hub_interaction_id,
    study_id,
    organization_party_id,
    created_by,
    updated_by,
    created_at,
    updated_at
FROM drh_stateful_activity_audit.hub_interaction;


-- Drop the view if it already exists
DROP VIEW IF EXISTS drh_stateless_activity_audit.file_interaction_view;

-- Create the view with proper joins and column selection
CREATE OR REPLACE VIEW drh_stateless_activity_audit.file_interaction_view 
WITH (security_invoker = true) AS
SELECT 
    fi.file_interaction_id,
    fi.hub_interaction_id,
    fi.study_id,
    fi.organization_party_id,
    fi.participant_id,
    fi.uri,
    fi.description,
    fi.request::text,
    fi.response::text,
    fi.db_file_id,
    fi.file_location,
    fi.file_name,
    fi.file_content_type,
    fi.file_content_json::text,
    fi.file_category,
    fi.file_upload_status,
    fi.file_processing_initiated_at,
    fi.file_processing_completed_at,
    fi.interaction_action_type_id,
    fi.interaction_status_id,
    fi.response_code,
    fi.error_response,
    rs.title AS study_title,
    rs.study_display_id,
    rs.created_by as rs_created_by,
    org.name AS organization_name,
    p.party_name AS created_by_name,
    fi.created_at,
    fi.updated_at,
    fi.created_by,
    fi.interaction_hierarchy::text as interaction_hierarchy, 
    iat.title AS interaction_action_type,
    ist.title AS interaction_status,
    rsub.participant_identifier as participant_display_id
FROM 
    drh_stateful_activity_audit.file_interaction fi
LEFT JOIN 
    drh_stateful_research_study.research_study rs ON fi.study_id = rs.study_id
LEFT JOIN 
    drh_stateful_research_study.organization org ON fi.organization_party_id = org.party_id
LEFT JOIN 
    drh_stateful_party.party p ON fi.created_by = p.party_id
LEFT JOIN 
    drh_stateful_master.interaction_action_type iat ON iat.id = fi.interaction_action_type_id
LEFT JOIN 
    drh_stateful_master.interaction_status ist ON ist.id = fi.interaction_status_id
LEFT JOIN 
    drh_stateful_research_study.research_subject rsub ON rsub.rsubject_id =fi.participant_id    
ORDER BY fi.created_at, fi.interaction_action_type_id ASC;

------------------------------------------------------------------------------------------
-------------------------STUDY INTERACTION TABLE VIEW-------------------------------------
------------------------------------------------------------------------------------------
-- Drop the view if it already exists
DROP VIEW IF EXISTS drh_stateless_activity_audit.study_interaction_view;

CREATE OR REPLACE VIEW drh_stateless_activity_audit.study_interaction_view
WITH (security_invoker=true) AS
SELECT
    si.study_interaction_id,
    si.hub_interaction_id,
    si.study_id,
    si.organization_party_id,
    si.uri,
    si.interaction_type,
    si.description,
    si.request,
    si.response,
    si.from_state,
    si.to_state,
    si.status,
    si.response_code,
    si.error_response,
    si.created_by,
    si.updated_by,
    si.created_at,
    si.updated_at,
    si.interaction_hierarchy,
    iat.title as action_type,
    ist.title as interaction_status,
    rs.study_display_id,
    rs.title as study_title,
    rs.created_by rs_created_by,
    org.name as organization_name
FROM
    drh_stateful_activity_audit.study_interaction si
LEFT JOIN
    drh_stateful_master.interaction_action_type iat ON si.interaction_action_type_id = iat.id
LEFT JOIN
    drh_stateful_master.interaction_status ist ON si.interaction_status_id = ist.id
LEFT JOIN
    drh_stateful_research_study.research_study rs ON si.study_id = rs.study_id
LEFT JOIN
    drh_stateful_research_study.organization org ON si.organization_party_id = org.party_id
ORDER BY si.created_at, si.interaction_action_type_id ASC;


------------------------------------------------------------------------------------------
-------------------------STUDY PARTICIPANT INTERACTION TABLE VIEW-------------------------
------------------------------------------------------------------------------------------
-- Drop the view if it already exists
DROP VIEW IF EXISTS drh_stateless_activity_audit.study_participant_interaction_view;
CREATE OR REPLACE VIEW drh_stateless_activity_audit.study_participant_interaction_view
WITH (security_invoker = true) AS
SELECT 
    spi.participant_interaction_id,
    spi.hub_interaction_id,
    spi.study_id,
    spi.organization_party_id,
    spi.participant_id,
    spi.uri,
    spi.interaction_type,
    spi.description,
    spi.request::text,
    spi.response::text,
    spi.from_state,
    spi.to_state,
    spi.status,
    spi.response_code,
    spi.error_response,
    spi.created_by,
    spi.updated_by,
    spi.created_at,
    spi.updated_at,
    spi.interaction_hierarchy::text as interaction_hierarchy,
    iat.title AS interaction_action_type,
    ist.title AS interaction_status,
    rs.study_display_id,
    rs.title AS study_title,
    rs.created_by as rs_created_by,
    org.name AS organization_name,
    p.party_name AS created_by_name,
    rsub.participant_identifier as participant_display_id
FROM 
    drh_stateful_activity_audit.study_participant_interaction spi
LEFT JOIN 
    drh_stateful_master.interaction_action_type iat ON iat.id = spi.interaction_action_type_id
LEFT JOIN 
    drh_stateful_master.interaction_status ist ON ist.id = spi.interaction_status_id
LEFT JOIN 
    drh_stateful_research_study.research_study rs ON spi.study_id = rs.study_id
LEFT JOIN 
    drh_stateful_research_study.organization org ON spi.organization_party_id = org.party_id
LEFT JOIN 
    drh_stateful_party.party p ON spi.created_by = p.party_id
LEFT JOIN
    drh_stateful_research_study.research_subject rsub ON rsub.rsubject_id = spi.participant_id
ORDER BY spi.created_at, spi.interaction_action_type_id ASC;

------------------------------------------------------------------------------------------    
CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.insert_activity_log(p_input_json JSONB)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_activity_id TEXT;
    v_created_at TIMESTAMPTZ;
    v_session_mapping_id TEXT;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_activity_audit.insert_activity_log';
    current_query text := pg_catalog.current_query();
    result JSONB;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    parameters_lst := jsonb_build_object(
        'p_input_json', p_input_json        
    );

    -- First, check if session_unique_id exists and get its ID if it does
    WITH session_check AS (
        SELECT id 
        FROM drh_stateful_activity_audit.session_unique_id_mapping 
        WHERE session_unique_id = NULLIF(p_input_json->>'session_unique_id', '')
    ),
    session_insert AS (
        INSERT INTO drh_stateful_activity_audit.session_unique_id_mapping (
            id, 
            session_unique_id,
            session_id
        )
        SELECT 
            drh_stateless_util.get_unique_id(),
            NULLIF(p_input_json->>'session_unique_id', ''),
            NULLIF(p_input_json->>'session_id', '')
        WHERE NOT EXISTS (
            SELECT 1 FROM session_check
        )
        RETURNING id
    )
    SELECT COALESCE(
        (SELECT id FROM session_check),
        (SELECT id FROM session_insert)
    ) INTO v_session_mapping_id;

    -- Insert into the activity_log table
    INSERT INTO drh_stateful_activity_audit.activity_log (
        activity_id, activity_name, activity_type, activity_description,
        root_id, parent_id, activity_hierarchy, hierarchy_path,
        request_url, organization_party_id, platform, environment,
        created_by, user_name, created_at, app_version,
        test_case, session_id, linkage_id, ip_address,
        location_latitude, location_longitude, activity_data,
        activity_level_id, session_unique_id,activity_type_id
    ) VALUES (
        drh_stateless_util.get_unique_id(),
        NULLIF(p_input_json->>'activity_name', ''), 
        NULLIF(p_input_json->>'activity_type', ''), 
        NULLIF(p_input_json->>'activity_description', ''), 
        NULLIF((p_input_json->>'root_id')::INT, 0), 
        NULLIF((p_input_json->>'parent_id')::INT, 0), 
        NULLIF(p_input_json->>'activity_hierarchy', ''), 
        NULLIF(p_input_json->>'hierarchy_path', ''), 
        NULLIF(p_input_json->>'request_url', ''), 
        NULLIF(p_input_json->>'organization_party_id', ''), 
        NULLIF(p_input_json->>'platform', ''), 
        NULLIF(p_input_json->>'environment', ''), 
        NULLIF(p_input_json->>'created_by', ''), 
        NULLIF(p_input_json->>'user_name', ''), 
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',  -- Using computed UTC timestamp
        NULLIF(p_input_json->>'app_version', ''), 
        NULLIF(p_input_json->>'test_case', ''), 
        NULLIF(p_input_json->>'session_id', ''),
        NULLIF(p_input_json->>'linkage_id', ''), 
        NULLIF(p_input_json->>'ip_address', ''), 
        NULLIF(p_input_json->>'location_latitude', ''), 
        NULLIF(p_input_json->>'location_longitude', ''), 
        NULLIF(p_input_json->>'activity_data', ''), 
        NULLIF(p_input_json->>'activity_level_id', NULL), 
        v_session_mapping_id,
        NULLIF(p_input_json->>'activity_type_id', NULL)
    )RETURNING activity_id into v_activity_id;

    -- Return success message
    RETURN jsonb_build_object('status', 'success', 'message', 'Activity log inserted successfully','activity_id',v_activity_id);    

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during activity_log insertion', 'error_details', error_details_json);
    RETURN result;
END;
$function$;



CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.insert_hub_interaction(p_input_json JSONB)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_hub_interaction_id TEXT;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_activity_audit.insert_hub_interaction';
    current_query text := pg_catalog.current_query();
    result JSONB;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_input_json', p_input_json        
    );
        
    -- Insert into hub_interaction table
    INSERT INTO drh_stateful_activity_audit.hub_interaction (
        hub_interaction_id, study_id, organization_party_id, created_by, updated_by, created_at, updated_at
    ) VALUES (
        drh_stateless_util.get_unique_id(),
        NULLIF(p_input_json->>'study_id', ''),
        NULLIF(p_input_json->>'organization_party_id', ''),
        NULLIF(p_input_json->>'created_by', ''),
        NULLIF(p_input_json->>'updated_by', ''),
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
    ) returning hub_interaction_id into v_hub_interaction_id;
    
    -- Return success message with hub_interaction_id
    RETURN jsonb_build_object('status', 'success', 'message', 'Hub interaction inserted successfully', 'hub_interaction_id', v_hub_interaction_id);

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;
    
    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    
    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );
    
    -- Return failure with the error details
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during hub_interaction insertion', 'error_details', error_details_json);
    RETURN result;
END;
$function$;


CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.insert_file_interaction(p_input_json JSONB)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_file_interaction_id TEXT;
    v_created_at TIMESTAMPTZ;
    v_updated_at TIMESTAMPTZ;
    result JSONB;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_activity_audit.insert_file_interaction';
    current_query text := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN


    parameters_lst := jsonb_build_object(
        'p_input_json', p_input_json        
    );    

    -- Insert into file_interaction table
    INSERT INTO drh_stateful_activity_audit.file_interaction (
        file_interaction_id, hub_interaction_id, study_id, organization_party_id,
        participant_id, uri, description, request, response, db_file_id,
        file_location, file_name, file_content_type, file_content_json, file_category,
        file_upload_status, file_processing_initiated_at, file_processing_completed_at,
        response_code, error_response, created_by, updated_by,
        created_at, updated_at,interaction_hierarchy, interaction_action_type_id,interaction_status_id
    ) VALUES (
        drh_stateless_util.get_unique_id(),
        NULLIF(p_input_json->>'hub_interaction_id', ''),
        NULLIF(p_input_json->>'study_id', ''),
        NULLIF(p_input_json->>'organization_party_id', ''),
        NULLIF(p_input_json->>'participant_id', ''),
        NULLIF(p_input_json->>'uri', ''),
        NULLIF(p_input_json->>'description', ''),
        p_input_json->'request',
        p_input_json->'response',
        NULLIF(p_input_json->>'db_file_id', ''),
        NULLIF(p_input_json->>'file_location', ''),
        NULLIF(p_input_json->>'file_name', ''),
        NULLIF(p_input_json->>'file_content_type', ''),
        p_input_json->'file_content_json',
        NULLIF(p_input_json->>'file_category', ''),
        NULLIF(p_input_json->>'file_upload_status', ''),
        NULLIF((p_input_json->>'file_processing_initiated_at')::TIMESTAMPTZ, NULL),
        NULLIF((p_input_json->>'file_processing_completed_at')::TIMESTAMPTZ, NULL),
        NULLIF((p_input_json->>'response_code')::INT, NULL),
        NULLIF(p_input_json->>'error_response', ''),
        NULLIF(p_input_json->>'created_by', ''),
        NULLIF(p_input_json->>'updated_by', ''),
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        NULL,
        COALESCE(p_input_json->'interaction_hierarchy', '[]'::jsonb),
        NULLIF((p_input_json->>'interaction_action_type_id')::INT, NULL),
        NULLIF((p_input_json->>'interaction_status_id')::INT, NULL)
    )RETURNING file_interaction_id into v_file_interaction_id;

    -- Return success message
    RETURN jsonb_build_object('status', 'success', 'message', 'File interaction inserted successfully','file_interaction_id',v_file_interaction_id);

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;
    
    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    
    -- Prepare error JSON response
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during file_interaction insertion',
        'error_details', jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        )
    );
    RETURN result;
END;
$function$;

CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.update_file_interaction_hierarchy(
    p_file_interaction_id TEXT,
    p_interaction_hierarchy JSONB
) 
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_updated_at TIMESTAMPTZ;
    result JSONB;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_activity_audit.update_file_interaction_hierarchy';
    current_query text := pg_catalog.current_query();
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    parameters_lst := jsonb_build_object(
        'p_file_interaction_id', p_file_interaction_id,
        'p_interaction_hierarchy', p_interaction_hierarchy);

    -- Update interaction_hierarchy in file_interaction table
    UPDATE drh_stateful_activity_audit.file_interaction 
    SET 
        interaction_hierarchy = p_interaction_hierarchy,
        updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
    WHERE file_interaction_id = p_file_interaction_id
    RETURNING updated_at INTO v_updated_at;

    -- Check if any row was updated
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'status', 'failure',
            'message', 'No file interaction found with the given ID',
            'file_interaction_id', p_file_interaction_id
        );
    END IF;

    -- Return success message
    RETURN jsonb_build_object(
        'status', 'success', 
        'message', 'File interaction hierarchy updated successfully',
        'file_interaction_id', p_file_interaction_id,
        'updated_at', v_updated_at
    );

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;
    
    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);
        
    -- Prepare error JSON response
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during file interaction hierarchy update',
        'error_details', jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        )
    );
    RETURN result;
END;
$function$;

CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.insert_study_interaction(p_input_json JSONB)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    error_details_json JSONB;
    result JSONB;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;    
    function_name text := 'drh_stateless_activity_audit.insert_study_interaction';
    current_query text := pg_catalog.current_query();
    v_study_interaction_id text;
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN
    
    parameters_lst := jsonb_build_object(
        'p_input_json', p_input_json        
    );

    -- Insert into the study_interaction table
    INSERT INTO drh_stateful_activity_audit.study_interaction (
        study_interaction_id, hub_interaction_id, study_id, organization_party_id,
        uri, interaction_type, description, request, response,
        from_state, to_state, status, response_code, error_response,
        created_by, updated_by, created_at, updated_at,interaction_hierarchy, interaction_action_type_id,interaction_status_id
    ) VALUES (
        drh_stateless_util.get_unique_id(),
        NULLIF(p_input_json->>'hub_interaction_id', ''),
        NULLIF(p_input_json->>'study_id', ''),
        NULLIF(p_input_json->>'organization_party_id', ''),
        NULLIF(p_input_json->>'uri', ''),
        NULLIF(p_input_json->>'interaction_type', ''),
        NULLIF(p_input_json->>'description', ''),
        NULLIF(p_input_json->'request', 'null'::jsonb),
        NULLIF(p_input_json->'response', 'null'::jsonb),
        NULLIF(p_input_json->>'from_state', ''),
        NULLIF(p_input_json->>'to_state', ''),
        NULLIF(p_input_json->>'status', ''),
        NULLIF((p_input_json->>'response_code')::INT, 0),
        NULLIF(p_input_json->>'error_response', ''),
        NULLIF(p_input_json->>'created_by', ''),
        NULLIF(p_input_json->>'updated_by', ''),
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        NULL,
        COALESCE(p_input_json->'interaction_hierarchy', '[]'::jsonb),
        NULLIF((p_input_json->>'interaction_action_type_id')::INT, NULL),
        NULLIF((p_input_json->>'interaction_status_id')::INT, NULL)
    )RETURNING study_interaction_id into v_study_interaction_id;

    -- Return success message
    RETURN jsonb_build_object('status', 'success', 'message', 'Study interaction inserted successfully','study_interaction_id',v_study_interaction_id);
EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with the error details
    result := jsonb_build_object('status', 'failure', 'message', 'Error occurred during study_interaction insertion', 'error_details', error_details_json);
    RETURN result;
END;
$function$;


CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.insert_study_participant_interaction(p_input_json JSONB)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_participant_interaction_id TEXT;
    v_created_at TIMESTAMPTZ;
    v_result JSONB;
    err_context text;
    err_state text;
    err_message text;
    err_detail text;
    err_hint text;
    error_details_json jsonb;
    function_name text := 'drh_stateless_activity_audit.insert_study_participant_interaction';
    current_query text := pg_catalog.current_query();    
    exception_log_json JSONB;
    parameters_lst JSONB;
BEGIN

    parameters_lst := jsonb_build_object(
        'p_input_json', p_input_json);
    
    -- Insert into the study_participant_interaction table
    INSERT INTO drh_stateful_activity_audit.study_participant_interaction (
        participant_interaction_id, hub_interaction_id, study_id, organization_party_id,
        participant_id, uri, interaction_type, description, request, response,
        from_state, to_state, status, response_code, error_response,
        created_by, updated_by, created_at, updated_at,interaction_hierarchy, interaction_action_type_id,interaction_status_id
    ) VALUES (
        drh_stateless_util.get_unique_id(),
        NULLIF(p_input_json->>'hub_interaction_id', ''),
        NULLIF(p_input_json->>'study_id', ''),
        NULLIF(p_input_json->>'organization_party_id', ''),
        NULLIF(p_input_json->>'participant_id', ''),
        NULLIF(p_input_json->>'uri', ''),
        NULLIF(p_input_json->>'interaction_type', ''),
        NULLIF(p_input_json->>'description', ''),
        p_input_json->'request',
        p_input_json->'response',
        NULLIF(p_input_json->>'from_state', ''),
        NULLIF(p_input_json->>'to_state', ''),
        NULLIF(p_input_json->>'status', ''),
        NULLIF((p_input_json->>'response_code')::INT, 0),
        NULLIF(p_input_json->>'error_response', ''),
        NULLIF(p_input_json->>'created_by', ''),
        NULLIF(p_input_json->>'updated_by', ''),
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        NULL,
        COALESCE(p_input_json->'interaction_hierarchy', '[]'::jsonb),
        NULLIF((p_input_json->>'interaction_action_type_id')::INT, NULL),
        NULLIF((p_input_json->>'interaction_status_id')::INT, NULL)
    )RETURNING participant_interaction_id into v_participant_interaction_id;

    -- Return success JSON
    RETURN jsonb_build_object('status', 'success', 'message', 'Study participant interaction inserted successfully','participant_interaction_id',v_participant_interaction_id);

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS err_context = PG_EXCEPTION_CONTEXT,
                             err_state = RETURNED_SQLSTATE,
                             err_message = MESSAGE_TEXT,
                             err_detail = PG_EXCEPTION_DETAIL,
                             err_hint = PG_EXCEPTION_HINT;

    -- Log error into exception_log table
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);


    -- Prepare error JSON response
    v_result := jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during study participant interaction insertion',
        'error_details', jsonb_build_object(
            'error', err_message,
            'detail', err_detail,
            'hint', err_hint,
            'context', err_context,
            'state', err_state
        )
    );

    RETURN v_result;
END;
$function$;


---------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_activity_audit.log_exception(exeption_info jsonb);

CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.log_exception(
    exception_info JSONB
)
RETURNS void AS
$$
BEGIN
    INSERT INTO drh_stateful_activity_audit.exception_log (
        function_name,
        error_code,
        error_message,
        error_detail,
        error_hint,
        error_context,
        query,
        parameters,
        occurred_at,
        resolved,
        resolved_at,
        resolver_comments
    )
    VALUES (
        exception_info->>'function_name',
        exception_info->>'error_code',
        exception_info->>'error_message',
        exception_info->>'error_detail',
        exception_info->>'error_hint',
        exception_info->>'error_context',
        exception_info->>'query',
        exception_info->'parameters',
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        'No',
        NULL,
        NULL
    );
END;
$$ LANGUAGE plpgsql;

----------------------------------------------------------------------------
-------------------------SESSION DURATION VIEW------------------------------
----------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_activity_audit.session_duration_view;
CREATE OR REPLACE VIEW drh_stateless_activity_audit.session_duration_view
WITH (security_invoker = true) AS 
SELECT
    sal.session_id::text AS session_id,
    sal.start_time AT TIME ZONE 'UTC' AS start_time,
    sal.end_time AT TIME ZONE 'UTC' AS end_time,
    sal.user_party_id::varchar AS user_party_id,
    ua.username::varchar AS username,
    org.name::varchar AS organization_name,
    p.org_party_id::text AS org_party_id,
    CASE
        WHEN sal.end_time IS NOT NULL AND sal.start_time IS NOT NULL
            THEN EXTRACT(EPOCH FROM sal.end_time - sal.start_time)::bigint
        ELSE NULL::bigint
    END AS duration_seconds
FROM drh_stateful_activity_audit.session_audit_log sal
LEFT JOIN drh_stateful_authentication.user_account ua
    ON ua.party_id = sal.user_party_id
LEFT JOIN drh_stateful_research_study.practitioner p
    ON p.practitioner_party_id = ua.party_id
LEFT JOIN drh_stateful_research_study.organization org
    ON org.party_id = p.org_party_id;

-----------------------------------------------------------------------------
-------------------------SAVE SESSION AUDIT LOG------------------------------
-----------------------------------------------------------------------------

DROP FUNCTION IF EXISTS drh_stateless_activity_audit.save_session_audit_log(text, text, text);
CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.save_session_audit_log(
    p_session_id text,
    p_username text,
    p_user_pary_id text
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'save_session_audit_log';
    current_query TEXT := pg_catalog.current_query();
BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Session audit log created successfully');

    -- Insert into session_audit_log
    INSERT INTO drh_stateful_activity_audit.session_audit_log (
        session_id,
        start_time,
        user_party_id,
        username,
        rec_status_id,
        created_at,
        created_by
    ) VALUES (
        p_session_id,
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        p_user_pary_id,
        p_username,
        1,
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        p_user_pary_id
    );

    -- Return success with session ID
    result := jsonb_build_object(
        'status', 'success',
        'message', 'Session audit log created successfully',
        'session_id', p_session_id
    );

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    INSERT INTO drh_stateful_activity_audit.exception_log (
        function_name,
        error_code,
        error_message,
        error_detail,
        error_hint,
        error_context,
        query,
        parameters,
        occurred_at,
        resolved,
        resolved_at,
        resolver_comments
    ) VALUES (
        function_name,
        err_state,
        err_message,
        err_detail,
        err_hint,
        err_context,
        current_query,
        NULL,
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        'No',
        NULL,
        NULL
    );

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with error details
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred while creating session audit log',
        'error_details', error_details_json
    );

    RETURN result;
END;
$function$;

-------------------------------------------------------------------------------
-------------------------UPDATE SESSION AUDIT LOG------------------------------
-------------------------------------------------------------------------------
DROP FUNCTION IF EXISTS drh_stateless_activity_audit.update_session_audit_log(text, text, text, text);
DROP FUNCTION IF EXISTS drh_stateless_activity_audit.update_session_audit_log(text, text, text);
CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.update_session_audit_log(
    p_session_id text,
    p_session_inactive_source text DEFAULT NULL,
    p_user_pary_id text DEFAULT NULL
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'update_session_audit_log';
    current_query TEXT := pg_catalog.current_query();
BEGIN
    -- Initialize result to success by default
    result := jsonb_build_object('status', 'success', 'message', 'Session audit log updated successfully');

    -- Update session_audit_log
    UPDATE drh_stateful_activity_audit.session_audit_log
    SET 
        end_time = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        session_inactive_source = NULLIF(p_session_inactive_source, ''),
        updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        updated_by = p_user_pary_id
    WHERE session_id = p_session_id;

    -- Check if the update was successful
    IF FOUND THEN
        result := jsonb_build_object(
            'status', 'success',
            'message', 'Session audit log updated successfully',
            'session_id', p_session_id
        );
    ELSE
        result := jsonb_build_object(
            'status', 'failure',
            'message', 'Session not found',
            'session_id', p_session_id
        );
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Capture error details
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    INSERT INTO drh_stateful_activity_audit.exception_log (
        function_name,
        error_code,
        error_message,
        error_detail,
        error_hint,
        error_context,
        query,
        parameters,
        occurred_at,
        resolved,
        resolved_at,
        resolver_comments
    ) VALUES (
        function_name,
        err_state,
        err_message,
        err_detail,
        err_hint,
        err_context,
        current_query,
        NULL,
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        'No',
        NULL,
        NULL
    );

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    -- Return failure with error details
    result := jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred while updating session audit log',
        'error_details', error_details_json
    );

    RETURN result;
END;
$function$;

-------------------------------------------------------------------------------
-------------------------SESSION UNIQUE ID MAPPING-----------------------------
-------------------------------------------------------------------------------
DROP VIEW IF EXISTS drh_stateless_activity_audit.session_unique_id_mapping_view;

CREATE OR REPLACE VIEW drh_stateless_activity_audit.session_unique_id_mapping_view
WITH (security_invoker = true) AS
SELECT
    id,
    session_unique_id,
    session_id
FROM
    drh_stateful_activity_audit.session_unique_id_mapping;


/*
 * Session Analytics Function
 * 
 * This function provides session analytics data including:
 * 1. Currently active users count (unique user_party_id with end_time IS NULL)
 * 2. Registered user count (unique user_party_id per organization)
 * 3. Total visit count (total sessions per organization)
 * 
 * Data is filtered by date range based on created_at timestamp
 */

-- DROP FUNCTION IF EXISTS drh_stateless_activity_audit.get_organization_session_stats(TIMESTAMPTZ,TIMESTAMPTZ, TEXT);

CREATE OR REPLACE FUNCTION drh_stateless_activity_audit.get_organization_session_stats(p_start_date timestamp with time zone, p_end_date timestamp with time zone, p_org_party_id text DEFAULT NULL::text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'drh_stateless_activity_audit.get_organization_session_stats';
    current_query TEXT := pg_catalog.current_query();
    parameters_lst JSONB := jsonb_build_object(
        'p_start_date', p_start_date,
        'p_end_date', p_end_date,
        'p_org_party_id', p_org_party_id
    );
    data_array jsonb;   
    exception_log_json JSONB; 
BEGIN
    IF p_org_party_id IS NULL THEN
        WITH session_data AS (
            SELECT 
                sdv.org_party_id,
                org.id AS organization_id,
                sdv.organization_name,
                COUNT(DISTINCT CASE WHEN sdv.end_time IS NULL THEN sdv.user_party_id END) AS currently_active_users_count,
                COUNT(*) AS total_visit_count
            FROM drh_stateless_activity_audit.session_duration_view sdv
            JOIN drh_stateful_research_study.organization org ON org.party_id = sdv.org_party_id
            JOIN drh_stateful_activity_audit.session_audit_log sal ON sal.session_id = sdv.session_id
            WHERE sal.created_at::DATE BETWEEN p_start_date AND p_end_date
              AND sal.deleted_at IS NULL
              AND sdv.org_party_id IS NOT NULL
            GROUP BY sdv.org_party_id, sdv.organization_name, org.id
        ),
        registered_users_data AS (
            SELECT 
                ua.org_party_id,
                org.id AS organization_id,
                org.name AS organization_name,
                COUNT(DISTINCT ua.party_id) AS registered_user_count
            FROM drh_stateful_authentication.user_account ua
            JOIN drh_stateful_research_study.organization org ON org.party_id = ua.org_party_id
            WHERE ua.deleted_at IS NULL
              AND ua.created_at::DATE BETWEEN p_start_date AND p_end_date
              AND NOT EXISTS (
                  SELECT 1 
                  FROM drh_stateful_authentication.auth_mappings am
                  WHERE am.user_id = ua.user_id
                    AND am.auth_provider = 'vanna' 
              )
              AND NOT EXISTS (
			    SELECT 1
			    FROM drh_stateful_authentication.user_role ur
			    JOIN drh_stateful_master.role r ON r.role_id = ur.role_id
			    WHERE ur.user_id = ua.user_id
			      AND ur.deleted_at IS NULL
			      AND r.role_name = 'Super Admin'
			)
			GROUP BY ua.org_party_id, org.id, org.name
        ),
        merged_data AS (
            SELECT 
                ru.org_party_id,
                ru.organization_id,
                ru.organization_name,
                COALESCE(sd.total_visit_count, 0) AS total_visit_count,
                ru.registered_user_count,
                COALESCE(sd.currently_active_users_count, 0) AS currently_active_users_count
            FROM registered_users_data ru
            LEFT JOIN session_data sd ON sd.org_party_id = ru.org_party_id
        )
        SELECT jsonb_agg(row_to_json(m)) INTO data_array
        FROM merged_data m;

        result := jsonb_build_object(
            'status', 'success',
            'message', 'Organization session stats fetched successfully',
            'data', COALESCE(data_array, '[]'::jsonb),
            'total', (
                SELECT jsonb_build_object(
                    'total_visit_count', COALESCE(SUM((item->>'total_visit_count')::int), 0),
                    'registered_user_count', COALESCE(SUM((item->>'registered_user_count')::int), 0),
                    'currently_active_users_count', COALESCE(SUM((item->>'currently_active_users_count')::int), 0)
                )
                FROM jsonb_array_elements(COALESCE(data_array, '[]'::jsonb)) AS item
            )
        );

    ELSE
        WITH session_data AS (
            SELECT 
                sdv.org_party_id,
                org.id AS organization_id,
                sdv.organization_name,
                COUNT(DISTINCT CASE WHEN sdv.end_time IS NULL THEN sdv.user_party_id END) AS currently_active_users_count,
                COUNT(*) AS total_visit_count
            FROM drh_stateless_activity_audit.session_duration_view sdv
            JOIN drh_stateful_research_study.organization org ON org.party_id = sdv.org_party_id
            JOIN drh_stateful_activity_audit.session_audit_log sal ON sal.session_id = sdv.session_id
            WHERE sal.created_at::DATE BETWEEN p_start_date AND p_end_date
              AND sal.deleted_at IS NULL
              AND sdv.org_party_id = p_org_party_id
            GROUP BY sdv.org_party_id, sdv.organization_name, org.id
        ),
        registered_users_data AS (
            SELECT 
                ua.org_party_id,
                org.id AS organization_id,
                org.name AS organization_name,
                COUNT(DISTINCT ua.party_id) AS registered_user_count
            FROM drh_stateful_authentication.user_account ua
            JOIN drh_stateful_research_study.organization org ON org.party_id = ua.org_party_id
            WHERE ua.deleted_at IS NULL
              AND ua.created_at::DATE BETWEEN p_start_date AND p_end_date
              AND ua.org_party_id = p_org_party_id
              AND NOT EXISTS (
                  SELECT 1 
                  FROM drh_stateful_authentication.auth_mappings am
                  WHERE am.user_id = ua.user_id
                    AND am.auth_provider = 'vanna' 
              
              )
              AND NOT EXISTS (
			    SELECT 1
			    FROM drh_stateful_authentication.user_role ur
			    JOIN drh_stateful_master.role r ON r.role_id = ur.role_id
			    WHERE ur.user_id = ua.user_id
			      AND ur.deleted_at IS NULL
			      AND r.role_name = 'Super Admin'
			)
            GROUP BY ua.org_party_id, org.id, org.name
        ),
        merged_data AS (
            SELECT 
                ru.org_party_id,
                ru.organization_id,
                ru.organization_name,
                COALESCE(sd.total_visit_count, 0) AS total_visit_count,
                ru.registered_user_count,
                COALESCE(sd.currently_active_users_count, 0) AS currently_active_users_count
            FROM registered_users_data ru
            LEFT JOIN session_data sd ON sd.org_party_id = ru.org_party_id
        )
        SELECT jsonb_agg(row_to_json(m)) INTO data_array
        FROM merged_data m;

        result := jsonb_build_object(
            'status', 'success',
            'message', 'Organization session stats fetched successfully',
            'data', COALESCE(data_array, '[]'::jsonb),
            'total', (
                SELECT jsonb_build_object(
                    'total_visit_count', COALESCE(SUM((item->>'total_visit_count')::int), 0),
                    'registered_user_count', COALESCE(SUM((item->>'registered_user_count')::int), 0),
                    'currently_active_users_count', COALESCE(SUM((item->>'currently_active_users_count')::int), 0)
                )
                FROM jsonb_array_elements(COALESCE(data_array, '[]'::jsonb)) AS item
            )
        );
    END IF;

    RETURN result;    

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS 
        err_context = PG_EXCEPTION_CONTEXT,
        err_state = RETURNED_SQLSTATE,
        err_message = MESSAGE_TEXT,
        err_detail = PG_EXCEPTION_DETAIL,
        err_hint = PG_EXCEPTION_HINT;

    -- Log the error details
    exception_log_json := jsonb_build_object(
        'function_name', function_name,
        'error_code', err_state,
        'error_message', err_message,
        'error_detail', err_detail,
        'error_hint', err_hint,
        'error_context', err_context,
        'query', current_query,
        'parameters', parameters_lst  
    );

    PERFORM drh_stateless_activity_audit.log_exception(exception_log_json);

    -- Prepare error JSON
    error_details_json := jsonb_build_object(
        'error', err_message,
        'detail', err_detail,
        'hint', err_hint,
        'context', err_context,
        'state', err_state
    );

    RETURN jsonb_build_object(
        'status', 'failure',
        'message', 'Error occurred during organization session stats fetch',
        'error_details', error_details_json
    );
END;
$function$
;

