-- Debug script to check created_by value
BEGIN;

-- Create test data
INSERT INTO drh_stateful_party.party (
    party_id, party_type_id, party_name, 
    created_at, created_by
) VALUES (
    drh_stateless_util.get_unique_id(), '01H8ZQKH3FZPY9X8W6CJ7BMQ0N', 
    'Debug Created By Test Org', 
    CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'DEBUG_USER'
) RETURNING party_id AS org_party_id \gset

INSERT INTO drh_stateful_research_study.organization (
    id, party_id, "name", rec_status_id, 
    created_at, created_by
) VALUES (
    drh_stateless_util.get_unique_id(), :'org_party_id', 
    'Debug Created By Test Org', 1,
    CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'DEBUG_USER'
) RETURNING id AS org_id \gset

-- Test the function with NULL created_by
SELECT drh_stateless_research_study.save_research_study(
    p_study_display_id => 'DBG001',
    p_org_party_id     => :'org_party_id',
    p_title            => 'Debug Created By Test',
    p_description      => 'Testing created_by default value',
    p_created_by       => NULL,  -- This should default to 'UNKNOWN'
    p_visibility       => 1,
    p_activity_json    => NULL
) AS result \gset

\echo 'Function result:'
\echo :result

-- Check what was actually stored
SELECT 
    study_id,
    study_display_id,
    title,
    created_by,
    CASE 
        WHEN created_by = 'UNKNOWN' THEN 'CORRECT: Default value applied'
        ELSE 'INCORRECT: Expected UNKNOWN, got: ' || COALESCE(created_by, 'NULL')
    END as created_by_check
FROM drh_stateful_research_study.research_study 
WHERE study_display_id = 'DBG001';

ROLLBACK;
