# pgTAP Test Suite for `drh_stateless_research_study.save_research_study` Function

## Overview

This comprehensive test suite validates the `drh_stateless_research_study.save_research_study` function using pgTAP (PostgreSQL Testing Framework). The tests ensure the function behaves correctly under various scenarios including normal operations, edge cases, and error conditions.

## Test Files

### 1. `004-idempotent-save-research-study-unit-test.psql`
Main test file containing three comprehensive test functions:

- `test_save_research_study_comprehensive()` - Core functionality tests
- `test_save_research_study_error_scenarios()` - Error handling tests  
- `test_save_research_study_performance()` - Performance and stress tests

### 2. `run-save-research-study-tests.psql`
Standalone test runner that can be executed independently to run only the save_research_study tests.

### 3. `README-save-research-study-tests.md`
This documentation file.

## Function Under Test

```sql
drh_stateless_research_study.save_research_study(
    p_study_display_id character varying DEFAULT NULL,
    p_org_party_id text DEFAULT NULL,
    p_title character varying DEFAULT NULL,
    p_description character varying DEFAULT NULL,
    p_created_by text DEFAULT 'UNKNOWN',
    p_visibility integer DEFAULT NULL,
    p_activity_json JSONB DEFAULT NULL
) RETURNS jsonb
```

## Test Coverage

### 1. Comprehensive Tests (25 test cases)

#### Function Validation
- ✅ Function existence and correct signature
- ✅ Return type validation (JSONB)

#### Successful Operations
- ✅ Study creation with all parameters
- ✅ Study creation with minimal parameters (defaults)
- ✅ Activity logging functionality
- ✅ Study collaboration record creation
- ✅ Different visibility parameter values

#### Data Integrity
- ✅ Foreign key relationships validation
- ✅ Default values application
- ✅ Database record creation verification
- ✅ Long text handling (max length strings)

#### Business Logic
- ✅ Duplicate study display ID prevention
- ✅ Organization party ID validation
- ✅ Proper JSON response structure

### 2. Error Scenario Tests (10 test cases)

#### Parameter Validation
- ✅ NULL study display ID handling
- ✅ NULL title (required field) handling
- ✅ Empty string parameters
- ✅ Oversized study display ID (VARCHAR limit)

#### Invalid Data
- ✅ Invalid visibility values
- ✅ Non-existent organization party ID
- ✅ Invalid JSON in activity_json parameter

#### Concurrent Operations
- ✅ Concurrent duplicate creation prevention
- ✅ Race condition handling

### 3. Performance Tests (5 test cases)

#### Timing Benchmarks
- ✅ Single study creation timing (< 5 seconds)
- ✅ Batch creation timing (10 studies < 30 seconds)
- ✅ Activity logging performance (< 10 seconds)

#### Stress Testing
- ✅ Multiple sequential operations
- ✅ Database record verification after batch operations

## Expected Function Behavior

### Success Response
```json
{
    "status": "success",
    "message": "Research study saved successfully",
    "study_id": "generated_unique_id"
}
```

### Failure Response (Duplicate)
```json
{
    "status": "failure",
    "message": "Duplicate study display ID found",
    "study_display_id": "conflicting_id"
}
```

### Error Response
```json
{
    "status": "failure",
    "message": "Error occurred during research study save",
    "error_details": {
        "error": "error_message",
        "detail": "error_detail",
        "hint": "error_hint",
        "context": "error_context",
        "state": "sql_state"
    }
}
```

## Running the Tests

### Option 1: Run All Tests (Including save_research_study)
```bash
psql -d your_database -f suite.pgtap.psql
```

### Option 2: Run Only save_research_study Tests
```bash
psql -d your_database -f run-save-research-study-tests.psql
```

### Option 3: Run Individual Test Functions
```sql
-- Connect to your database
\c your_database

-- Set search path
SET search_path TO drh_udi_assurance, drh_stateless_research_study, drh_stateful_research_study, drh_stateful_party, drh_stateful_master, drh_stateless_util, public;

-- Run specific test function
SELECT * FROM drh_udi_assurance.test_save_research_study_comprehensive();
```

## Prerequisites

### Database Extensions
- pgTAP extension must be installed
- All required schemas must exist:
  - `drh_stateful_research_study`
  - `drh_stateful_party`
  - `drh_stateful_master`
  - `drh_stateless_research_study`
  - `drh_stateless_util`

### Required Tables and Functions
- `drh_stateful_party.party`
- `drh_stateful_research_study.organization`
- `drh_stateful_research_study.research_study`
- `drh_stateful_research_study.study_collaboration`
- `drh_stateful_party.record_status`
- `drh_stateless_util.get_unique_id()`

## Test Data Management

### Setup
Tests automatically create required test data:
- Test party records
- Test organization records
- Test user records

### Cleanup
The test runner uses transactions and can rollback changes to avoid polluting the database. Uncomment the `ROLLBACK;` line in the test runner to enable cleanup.

## Interpreting Results

### Success Output
```
ok 1 - Test data setup completed successfully
ok 2 - Function save_research_study exists with correct signature
ok 3 - Function returns JSONB type
...
ok 25 - All save_research_study tests completed successfully
```

### Failure Output
```
not ok 5 - Study creation returns success status
#   Failed test 'Study creation returns success status'
#   got: 'failure'
#   expected: 'success'
```

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Ensure all required schemas and tables exist
2. **Permission Issues**: Verify the test user has appropriate permissions
3. **pgTAP Not Installed**: Install pgTAP extension in the test schema
4. **Search Path Issues**: Ensure all required schemas are in the search path

### Debug Mode
To debug failing tests, you can:
1. Comment out the `ROLLBACK;` to preserve test data
2. Add additional diagnostic queries
3. Run tests individually to isolate issues

## Maintenance

### Adding New Tests
1. Add new test cases to the appropriate test function
2. Update the test plan count (`PERFORM plan(N);`)
3. Update this documentation

### Modifying Existing Tests
1. Ensure backward compatibility
2. Update test descriptions and documentation
3. Verify all test scenarios still pass

## Performance Benchmarks

Based on the performance tests, the function should meet these benchmarks:
- Single study creation: < 5 seconds
- Batch creation (10 studies): < 30 seconds  
- With activity logging: < 10 seconds

If performance degrades beyond these thresholds, investigate:
- Database indexing
- Function optimization
- Resource constraints
