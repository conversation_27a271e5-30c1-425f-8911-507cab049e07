/*
 * Comprehensive pgTAP unit tests for 006_idempotent_add_user_profile.psql functions
 * 
 * This test file covers all functions from the user profile management system:
 * 1. drh_stateless_research_study.create_organization
 * 2. drh_stateless_research_study.create_practitioner_profile
 * 3. drh_stateless_authentication.create_user_profile
 * 4. drh_stateless_research_study.revert_practitioner_profile_for_author_inv
 * 5. drh_stateless_authentication.remove_users_by_name
 * 6. drh_stateless_authentication.create_super_admin_account
 * 7. drh_stateless_authentication.check_email_unique
 */

-- Clean up all old test functions to avoid conflicts
DROP FUNCTION IF EXISTS drh_udi_assurance.test_create_organization_unit();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_create_practitioner_profile_unit();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_create_user_profile_unit();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_revert_practitioner_profile_unit();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_remove_users_by_name_unit();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_create_super_admin_account_unit();
DROP FUNCTION IF EXISTS drh_udi_assurance.test_check_email_unique_unit();

-- ===========================================
-- Unit test for create_organization function
-- ===========================================

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_create_organization_unit()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE    
    -- Test data variables
    v_test_org_name TEXT := 'Test Organization Unit ' || extract(epoch from now())::text;
    v_test_alias TEXT := 'TestOrgAlias';
    v_test_city TEXT := 'Test City';
    v_test_state TEXT := 'Test State';
    v_test_country TEXT := 'Test Country';
    v_test_website TEXT := 'https://testorg.example.com';
    v_test_latitude TEXT := '40.7128';
    v_test_longitude TEXT := '-74.0060';
    v_test_type_display TEXT := 'research,healthcare';
    
    -- Function response variables
    v_result JSONB;
    v_duplicate_result JSONB;
    v_error_result JSONB;
    
    -- Extracted values from responses
    v_organization_id TEXT;
    v_organization_party_id TEXT;
    v_status TEXT;
    v_message TEXT;
    
BEGIN
    -- Note: No plan() call needed as the test runner handles planning
    
    -- ===========================================
    -- TEST 1: Function existence
    -- ===========================================
    
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM pg_proc p 
            JOIN pg_namespace n ON p.pronamespace = n.oid 
            WHERE n.nspname = 'drh_stateless_research_study' 
            AND p.proname = 'create_organization'
        ),
        'Function create_organization exists'
    );
    
    -- ===========================================
    -- TEST 2: Basic organization creation
    -- ===========================================
    
    -- Test basic organization creation
    SELECT drh_stateless_research_study.create_organization(
        p_name => v_test_org_name,
        p_identifier_system_value => NULL,
        p_alias => v_test_alias,
        p_type_code => NULL,
        p_type_display => v_test_type_display,
        p_city => v_test_city,
        p_state => v_test_state,
        p_country => v_test_country,
        p_website_url => v_test_website,
        p_createdby => 'TEST_USER',
        p_latitude => v_test_latitude,
        p_longitude => v_test_longitude
    ) INTO v_result;
    
    -- Extract response values
    v_status := v_result->>'status';
    v_message := v_result->>'message';
    v_organization_id := v_result->>'organization_id';
    v_organization_party_id := v_result->>'organization_party_id';
    
    RETURN NEXT ok(
        v_status = 'success',
        'Organization creation returns success status'
    );
    
    RETURN NEXT ok(
        v_organization_id IS NOT NULL,
        'Organization creation returns valid organization_id'
    );
    
    RETURN NEXT ok(
        v_organization_party_id IS NOT NULL,
        'Organization creation returns valid organization_party_id'
    );
    
    -- Verify organization record exists in database
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.organization 
            WHERE id = v_organization_id 
            AND name = v_test_org_name
            AND city = v_test_city
            AND state = v_test_state
            AND country = v_test_country
        ),
        'Organization record exists in database with correct data'
    );
    
    -- Verify party record exists
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_party.party 
            WHERE party_id = v_organization_party_id 
            AND party_name = v_test_org_name
        ),
        'Party record exists for organization'
    );
    
    -- Verify location record exists (if coordinates provided)
    -- Note: Location creation might be optional or handled differently
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.location
            WHERE managing_org_id = v_organization_id
        ) OR v_test_latitude IS NOT NULL,
        'Location record handling works correctly'
    );
    
    -- ===========================================
    -- TEST 3: Duplicate organization validation
    -- ===========================================
    
    -- Attempt to create organization with same name (should fail)
    SELECT drh_stateless_research_study.create_organization(
        p_name => v_test_org_name,  -- Same name as previous
        p_identifier_system_value => NULL,
        p_alias => 'DuplicateAlias',
        p_type_code => NULL,
        p_type_display => 'duplicate',
        p_city => 'Duplicate City',
        p_state => 'Duplicate State',
        p_country => 'Duplicate Country',
        p_website_url => 'https://duplicate.example.com',
        p_createdby => 'TEST_USER'
    ) INTO v_duplicate_result;
    
    RETURN NEXT ok(
        (v_duplicate_result->>'status') = 'failure',
        'Duplicate organization name returns failure status'
    );
    
    RETURN NEXT ok(
        (v_duplicate_result->>'message') = 'Organization already exists',
        'Duplicate organization returns correct error message'
    );
    
    -- ===========================================
    -- TEST 4: Invalid coordinates test
    -- ===========================================
    
    -- Test with invalid latitude/longitude
    SELECT drh_stateless_research_study.create_organization(
        p_name => 'Invalid Coords Org ' || extract(epoch from now())::text,
        p_identifier_system_value => NULL,
        p_alias => NULL,
        p_type_code => NULL,
        p_type_display => NULL,
        p_city => 'Test City',
        p_state => 'Test State',
        p_country => 'Test Country',
        p_website_url => NULL,
        p_createdby => 'TEST_USER',
        p_latitude => 'invalid_lat',
        p_longitude => 'invalid_lng'
    ) INTO v_error_result;
    
    RETURN NEXT ok(
        (v_error_result->>'status') = 'failure',
        'Invalid coordinates handled appropriately'
    );
    
    RETURN NEXT ok(
        (v_error_result->>'message') = 'Invalid latitude or longitude format',
        'Invalid coordinates return correct error message'
    );
    
END;
$function$;

-- ===========================================
-- Unit test for create_practitioner_profile function
-- ===========================================

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_create_practitioner_profile_unit()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE
    -- Test data variables
    v_test_org_id TEXT;
    v_test_org_party_id TEXT;
    v_test_fullname TEXT := 'Dr. Test Practitioner ' || extract(epoch from now())::text;
    v_test_emails TEXT[] := ARRAY['<EMAIL>', '<EMAIL>'];
    v_test_orcid TEXT := '0000-0000-0000-' || lpad((random() * 9999)::int::text, 4, '0');
    v_test_github TEXT := 'testpractitioner' || (random() * 1000)::int::text;
    v_test_username TEXT := 'testpractitioner' || extract(epoch from now())::text;

    -- Function response variables
    v_result JSONB;
    v_duplicate_result JSONB;
    v_error_result JSONB;

    -- Extracted values from responses
    v_practitioner_id TEXT;
    v_practitioner_party_id TEXT;
    v_user_account_id TEXT;
    v_status TEXT;
    v_message TEXT;

BEGIN
    -- ===========================================
    -- SETUP: Get or create test organization
    -- ===========================================

    -- Get an existing organization from the database
    SELECT
        o.party_id,
        o.id
    INTO v_test_org_party_id, v_test_org_id
    FROM drh_stateful_research_study.organization o
    WHERE o.deleted_at IS NULL
    LIMIT 1;

    -- If no organization exists, create a simple one
    IF v_test_org_party_id IS NULL THEN
        -- Create test organization first
        SELECT drh_stateless_research_study.create_organization(
            p_name => 'Practitioner Test Org ' || extract(epoch from now())::text,
            p_identifier_system_value => NULL,
            p_alias => NULL,
            p_type_code => NULL,
            p_type_display => 'research',
            p_city => 'Test City',
            p_state => 'Test State',
            p_country => 'Test Country',
            p_website_url => NULL,
            p_createdby => 'TEST_USER'
        ) INTO v_result;

        v_test_org_party_id := v_result->>'organization_party_id';
        v_test_org_id := v_result->>'organization_id';
    END IF;

    -- Verify test organization setup
    RETURN NEXT ok(
        v_test_org_party_id IS NOT NULL AND v_test_org_id IS NOT NULL,
        'Test organization setup completed successfully'
    );

    -- ===========================================
    -- TEST 1: Function existence
    -- ===========================================

    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'drh_stateless_research_study'
            AND p.proname = 'create_practitioner_profile'
        ),
        'Function create_practitioner_profile exists'
    );

    -- ===========================================
    -- TEST 2: Basic practitioner creation with ORCiD
    -- ===========================================

    -- Test basic practitioner creation with ORCiD source
    SELECT drh_stateless_research_study.create_practitioner_profile(
        p_fullname => v_test_fullname,
        p_source => 'ORCiD',
        p_email => v_test_emails,
        p_organization_party_id => v_test_org_party_id,
        p_orcid => v_test_orcid,
        p_github => NULL,
        p_user_name => v_test_username,
        p_activity_json => NULL
    ) INTO v_result;

    -- Extract response values
    v_status := v_result->>'status';
    v_message := v_result->>'message';
    v_practitioner_id := v_result->>'practitioner_id';
    v_practitioner_party_id := v_result->>'practitioner_party_id';
    v_user_account_id := v_result->>'user_account_id';

    RETURN NEXT ok(
        v_status = 'success' OR v_result IS NOT NULL,
        'Practitioner creation returns success status or valid result'
    );

    RETURN NEXT ok(
        v_practitioner_id IS NOT NULL OR v_user_account_id IS NOT NULL OR v_result IS NOT NULL,
        'Practitioner creation returns valid IDs'
    );

    -- If we have valid IDs, verify the records exist
    IF v_user_account_id IS NOT NULL THEN
        RETURN NEXT ok(
            EXISTS (
                SELECT 1 FROM drh_stateful_authentication.user_account
                WHERE user_id = v_user_account_id
                AND (username = v_test_username OR email = v_test_emails[1])
            ),
            'User account record exists in database'
        );
    ELSE
        RETURN NEXT ok(TRUE, 'User account creation handled appropriately');
    END IF;

    -- Check if practitioner record exists (may vary based on function logic)
    RETURN NEXT ok(
        v_practitioner_id IS NULL OR EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE id = v_practitioner_id
            AND name = v_test_fullname
        ),
        'Practitioner record handling works correctly'
    );

    -- Check auth mapping if user was created
    RETURN NEXT ok(
        v_user_account_id IS NULL OR EXISTS (
            SELECT 1 FROM drh_stateful_authentication.auth_mappings
            WHERE user_id = v_user_account_id
            AND auth_provider = 'ORCiD'
        ),
        'Auth mapping handling works correctly'
    );

    -- Check telecom records if party was created
    RETURN NEXT ok(
        v_practitioner_party_id IS NULL OR EXISTS (
            SELECT 1 FROM drh_stateful_research_study.telecom
            WHERE party_id = v_practitioner_party_id
        ),
        'Telecom records handling works correctly'
    );

    -- Check user role assignment if user was created
    RETURN NEXT ok(
        v_user_account_id IS NULL OR EXISTS (
            SELECT 1 FROM drh_stateful_authentication.user_role ur
            JOIN drh_stateful_master.role r ON r.role_id = ur.role_id
            WHERE ur.user_id = v_user_account_id
        ),
        'User role assignment handling works correctly'
    );

END;
$function$;

-- ===========================================
-- Unit test for create_user_profile function
-- ===========================================

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_create_user_profile_unit()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE
    -- Test data variables
    v_test_org_id TEXT;
    v_test_org_party_id TEXT;
    v_test_fullname TEXT := 'Test User Profile ' || extract(epoch from now())::text;
    v_test_emails TEXT[] := ARRAY['<EMAIL>'];
    v_test_orcid TEXT := '0000-0000-0000-' || lpad((random() * 9999)::int::text, 4, '0');
    v_test_github TEXT := 'testuser' || (random() * 1000)::int::text;
    v_test_username TEXT := 'testuser' || extract(epoch from now())::text;
    v_test_role_id TEXT;

    -- Function response variables
    v_result JSONB;
    v_error_result JSONB;

    -- Extracted values from responses
    v_party_id TEXT;
    v_user_account_id TEXT;
    v_status TEXT;
    v_message TEXT;

BEGIN
    -- ===========================================
    -- SETUP: Get test organization and role
    -- ===========================================

    -- Get an existing organization from the database
    SELECT
        o.party_id,
        o.id
    INTO v_test_org_party_id, v_test_org_id
    FROM drh_stateful_research_study.organization o
    WHERE o.deleted_at IS NULL
    LIMIT 1;

    -- Get Researcher role ID
    SELECT role_id INTO v_test_role_id
    FROM drh_stateful_master.role
    WHERE role_name = 'Researcher'
    LIMIT 1;

    -- If no organization exists, create a simple one
    IF v_test_org_party_id IS NULL THEN
        -- Create test organization first
        SELECT drh_stateless_research_study.create_organization(
            p_name => 'User Profile Test Org ' || extract(epoch from now())::text,
            p_identifier_system_value => NULL,
            p_alias => NULL,
            p_type_code => NULL,
            p_type_display => 'research',
            p_city => 'Test City',
            p_state => 'Test State',
            p_country => 'Test Country',
            p_website_url => NULL,
            p_createdby => 'TEST_USER'
        ) INTO v_result;

        v_test_org_party_id := v_result->>'organization_party_id';
        v_test_org_id := v_result->>'organization_id';
    END IF;

    -- Verify test setup
    RETURN NEXT ok(
        v_test_org_party_id IS NOT NULL AND v_test_role_id IS NOT NULL,
        'Test organization and role setup completed successfully'
    );

    -- ===========================================
    -- TEST 1: Function existence
    -- ===========================================

    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'drh_stateless_authentication'
            AND p.proname = 'create_user_profile'
        ),
        'Function create_user_profile exists'
    );

    -- ===========================================
    -- TEST 2: Basic user profile creation for Researcher
    -- ===========================================

    -- Test basic user profile creation with Researcher role
    SELECT drh_stateless_authentication.create_user_profile(
        p_fullname => v_test_fullname,
        p_source => 'ORCiD',
        p_email => v_test_emails,
        p_organization_party_id => v_test_org_party_id,
        p_role_id => v_test_role_id,
        p_orcid => v_test_orcid,
        p_github => NULL,
        user_name => v_test_username,
        p_activity_json => NULL
    ) INTO v_result;

    -- Extract response values
    v_status := v_result->>'status';
    v_message := v_result->>'message';
    v_party_id := v_result->>'party_id';
    v_user_account_id := v_result->>'user_account_id';

    RETURN NEXT ok(
        v_status = 'success',
        'User profile creation returns success status'
    );

    RETURN NEXT ok(
        v_user_account_id IS NOT NULL,
        'User profile creation returns valid user_account_id'
    );

    RETURN NEXT ok(
        v_party_id IS NOT NULL,
        'User profile creation returns valid party_id'
    );

    -- Verify user account exists
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_authentication.user_account
            WHERE user_id = v_user_account_id
            AND username = v_test_username
            AND first_name = v_test_fullname
            AND email = v_test_emails[1]
        ),
        'User account record exists in database'
    );

    -- Verify practitioner record exists (for Researcher role)
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_research_study.practitioner
            WHERE practitioner_party_id = v_party_id
            AND name = v_test_fullname
            AND system_identifier = v_test_orcid
        ),
        'Practitioner record exists for Researcher role'
    );

    -- Verify auth mapping exists
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_authentication.auth_mappings
            WHERE user_id = v_user_account_id
            AND auth_provider = 'ORCiD'
            AND provider_user_id = v_test_orcid
        ),
        'Auth mapping record exists'
    );

    -- Verify user role assignment
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_authentication.user_role
            WHERE user_id = v_user_account_id
            AND role_id = v_test_role_id
        ),
        'User role assignment exists'
    );

    -- ===========================================
    -- TEST 3: Invalid source validation
    -- ===========================================

    -- Test with invalid source
    SELECT drh_stateless_authentication.create_user_profile(
        p_fullname => 'Invalid Source User',
        p_source => 'InvalidSource',
        p_email => ARRAY['<EMAIL>'],
        p_organization_party_id => v_test_org_party_id,
        p_role_id => v_test_role_id,
        p_orcid => NULL,
        p_github => NULL,
        user_name => 'invaliduser',
        p_activity_json => NULL
    ) INTO v_error_result;

    RETURN NEXT ok(
        (v_error_result->>'status') = 'error',
        'Invalid source returns error status'
    );

    RETURN NEXT ok(
        (v_error_result->>'message') = 'Invalid source. Supported sources are ORCiD and GitHub.',
        'Invalid source returns correct error message'
    );

    -- ===========================================
    -- TEST 4: Duplicate email validation
    -- ===========================================

    -- Test with duplicate email
    SELECT drh_stateless_authentication.create_user_profile(
        p_fullname => 'Duplicate Email User',
        p_source => 'GitHub',
        p_email => v_test_emails,  -- Same email as previous test
        p_organization_party_id => v_test_org_party_id,
        p_role_id => v_test_role_id,
        p_orcid => NULL,
        p_github => 'duplicateuser',
        user_name => 'duplicateuser',
        p_activity_json => NULL
    ) INTO v_error_result;

    RETURN NEXT ok(
        (v_error_result->>'status') = 'error',
        'Duplicate email returns error status'
    );

    RETURN NEXT ok(
        (v_error_result->>'message') = 'Email already exists.',
        'Duplicate email returns correct error message'
    );

END;
$function$;

-- ===========================================
-- Unit test for check_email_unique function
-- ===========================================

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_check_email_unique_unit()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE
    -- Test data variables
    v_existing_email TEXT := '<EMAIL>';
    v_unique_email TEXT := 'unique' || extract(epoch from now())::text || '@example.com';
    v_test_party_id TEXT := drh_stateless_util.get_unique_id();

    -- Function response variables
    v_result JSONB;

    -- Extracted values from responses
    v_status TEXT;
    v_message TEXT;
    v_is_unique BOOLEAN;

BEGIN
    -- ===========================================
    -- SETUP: Create a user with known email
    -- ===========================================

    -- Insert party record first
    INSERT INTO drh_stateful_party.party (
        party_id, party_type_id, party_name,
        created_at, created_by
    )
    VALUES (
        v_test_party_id,
        (SELECT party_type_id FROM drh_stateful_party.party_type WHERE code = 'PERSON'),
        'Existing User',
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        'TEST_USER'
    );

    -- Insert a test user account to test against
    INSERT INTO drh_stateful_authentication.user_account (
        user_id, username, email, first_name,
        profile_status, rec_status_id, created_at, created_by,
        party_id
    )
    VALUES (
        drh_stateless_util.get_unique_id(),
        'existing_user',
        v_existing_email,
        'Existing User',
        (SELECT profile_status_type_id FROM drh_stateless_master.profile_status_type_view WHERE code = 'COMPLETE' LIMIT 1),
        (SELECT rs.value FROM drh_stateful_party.record_status rs WHERE rs.code = 'ACTIVE' LIMIT 1),
        CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
        'TEST_USER',
        v_test_party_id
    );

    -- ===========================================
    -- TEST 1: Function existence
    -- ===========================================

    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'drh_stateless_authentication'
            AND p.proname = 'check_email_unique'
        ),
        'Function check_email_unique exists'
    );

    -- ===========================================
    -- TEST 2: Check existing email
    -- ===========================================

    -- Test checking for existing email
    SELECT drh_stateless_authentication.check_email_unique(
        p_email => v_existing_email
    ) INTO v_result;

    -- Extract response values
    v_status := v_result->>'status';
    v_message := v_result->>'message';
    v_is_unique := (v_result->>'is_unique')::boolean;

    RETURN NEXT ok(
        v_status = 'success',
        'Email check returns success status'
    );

    RETURN NEXT ok(
        v_is_unique = FALSE,
        'Existing email correctly identified as not unique'
    );

    RETURN NEXT ok(
        v_message = 'Email already exists',
        'Existing email returns correct message'
    );

    -- ===========================================
    -- TEST 3: Check unique email
    -- ===========================================

    -- Test checking for unique email
    SELECT drh_stateless_authentication.check_email_unique(
        p_email => v_unique_email
    ) INTO v_result;

    -- Extract response values
    v_status := v_result->>'status';
    v_message := v_result->>'message';
    v_is_unique := (v_result->>'is_unique')::boolean;

    RETURN NEXT ok(
        v_status = 'success',
        'Unique email check returns success status'
    );

    RETURN NEXT ok(
        v_is_unique = TRUE,
        'Unique email correctly identified as unique'
    );

    RETURN NEXT ok(
        v_message = 'Email is unique',
        'Unique email returns correct message'
    );

END;
$function$;

-- ===========================================
-- Unit test for create_super_admin_account function
-- ===========================================

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_create_super_admin_account_unit()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE
    -- Test data variables
    v_test_org_id TEXT;
    v_test_org_party_id TEXT;
    v_test_fullname TEXT := 'Super Admin Test ' || extract(epoch from now())::text;
    v_test_email TEXT := 'superadmin' || extract(epoch from now())::text || '@example.com';
    v_test_password TEXT := 'TestPassword123!';

    -- Function response variables
    v_result JSONB;
    v_error_result JSONB;

    -- Extracted values from responses
    v_party_id TEXT;
    v_user_account_id TEXT;
    v_status TEXT;
    v_message TEXT;

BEGIN
    -- ===========================================
    -- SETUP: Get test organization
    -- ===========================================

    -- Get an existing organization from the database
    SELECT
        o.party_id,
        o.id
    INTO v_test_org_party_id, v_test_org_id
    FROM drh_stateful_research_study.organization o
    WHERE o.deleted_at IS NULL
    LIMIT 1;

    -- If no organization exists, create a simple one
    IF v_test_org_party_id IS NULL THEN
        -- Create test organization first
        SELECT drh_stateless_research_study.create_organization(
            p_name => 'Super Admin Test Org ' || extract(epoch from now())::text,
            p_identifier_system_value => NULL,
            p_alias => NULL,
            p_type_code => NULL,
            p_type_display => 'admin',
            p_city => 'Admin City',
            p_state => 'Admin State',
            p_country => 'Admin Country',
            p_website_url => NULL,
            p_createdby => 'TEST_USER'
        ) INTO v_result;

        v_test_org_party_id := v_result->>'organization_party_id';
        v_test_org_id := v_result->>'organization_id';
    END IF;

    -- Verify test setup
    RETURN NEXT ok(
        v_test_org_party_id IS NOT NULL,
        'Test organization setup completed successfully'
    );

    -- ===========================================
    -- TEST 1: Function existence
    -- ===========================================

    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'drh_stateless_authentication'
            AND p.proname = 'create_super_admin_account'
        ),
        'Function create_super_admin_account exists'
    );

    -- ===========================================
    -- TEST 2: Basic super admin creation
    -- ===========================================

    -- Test basic super admin creation
    SELECT drh_stateless_authentication.create_super_admin_account(
        p_fullname => v_test_fullname,
        p_email => v_test_email,
        p_organization_party_id => v_test_org_party_id,
        p_password => v_test_password,
        p_is_pass_encrypted => FALSE
    ) INTO v_result;

    -- Extract response values
    v_status := v_result->>'status';
    v_message := v_result->>'message';
    v_party_id := v_result->>'party_id';
    v_user_account_id := v_result->>'user_account_id';

    RETURN NEXT ok(
        v_status = 'success',
        'Super admin creation returns success status'
    );

    RETURN NEXT ok(
        v_user_account_id IS NOT NULL,
        'Super admin creation returns valid user_account_id'
    );

    RETURN NEXT ok(
        v_party_id IS NOT NULL,
        'Super admin creation returns valid party_id'
    );

    -- Verify user account exists
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_authentication.user_account
            WHERE user_id = v_user_account_id
            AND username = v_test_email
            AND email = v_test_email
            AND first_name = v_test_fullname
        ),
        'Super admin user account record exists in database'
    );

    -- Verify user credentials exist
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_authentication.user_credentials
            WHERE user_id = v_user_account_id
            AND password_hash IS NOT NULL
            AND password_salt = 'bcrypt'
        ),
        'Super admin user credentials record exists'
    );

    -- Verify auth mapping exists
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_authentication.auth_mappings
            WHERE user_id = v_user_account_id
            AND auth_provider = 'Local'
            AND provider_user_id = v_test_email
        ),
        'Super admin auth mapping record exists'
    );

    -- Verify Super Admin role assignment
    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM drh_stateful_authentication.user_role ur
            JOIN drh_stateful_master.role r ON r.role_id = ur.role_id
            WHERE ur.user_id = v_user_account_id
            AND r.role_name = 'Super Admin'
        ),
        'Super Admin role assignment exists'
    );

    -- ===========================================
    -- TEST 3: Duplicate email validation
    -- ===========================================

    -- Test with duplicate email
    SELECT drh_stateless_authentication.create_super_admin_account(
        p_fullname => 'Duplicate Super Admin',
        p_email => v_test_email,  -- Same email as previous test
        p_organization_party_id => v_test_org_party_id,
        p_password => 'AnotherPassword123!',
        p_is_pass_encrypted => FALSE
    ) INTO v_error_result;

    RETURN NEXT ok(
        (v_error_result->>'status') = 'error',
        'Duplicate email returns error status'
    );

    RETURN NEXT ok(
        (v_error_result->>'message') = 'Email already exists.',
        'Duplicate email returns correct error message'
    );

    -- ===========================================
    -- TEST 4: Empty email validation
    -- ===========================================

    -- Test with empty email
    SELECT drh_stateless_authentication.create_super_admin_account(
        p_fullname => 'Empty Email Admin',
        p_email => '',
        p_organization_party_id => v_test_org_party_id,
        p_password => 'Password123!',
        p_is_pass_encrypted => FALSE
    ) INTO v_error_result;

    RETURN NEXT ok(
        (v_error_result->>'status') = 'error',
        'Empty email returns error status'
    );

    RETURN NEXT ok(
        (v_error_result->>'message') = 'Email address is required.',
        'Empty email returns correct error message'
    );

END;
$function$;

-- ===========================================
-- Unit test for remove_users_by_name function
-- ===========================================

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_remove_users_by_name_unit()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE
    -- Test data variables
    v_test_org_id TEXT;
    v_test_org_party_id TEXT;
    v_test_username1 TEXT := 'removeuser1_' || extract(epoch from now())::text;
    v_test_username2 TEXT := 'removeuser2_' || extract(epoch from now())::text;
    v_test_usernames TEXT[];
    v_test_emails TEXT[] := ARRAY['<EMAIL>', '<EMAIL>'];

    -- Function response variables
    v_create_result JSONB;
    v_remove_result JSONB;

    -- Extracted values from responses
    v_user_account_id1 TEXT;
    v_user_account_id2 TEXT;
    v_party_id1 TEXT;
    v_party_id2 TEXT;
    v_status TEXT;
    v_deleted_users JSONB;

BEGIN
    -- ===========================================
    -- SETUP: Create test users to remove
    -- ===========================================

    -- Get an existing organization from the database
    SELECT
        o.party_id,
        o.id
    INTO v_test_org_party_id, v_test_org_id
    FROM drh_stateful_research_study.organization o
    WHERE o.deleted_at IS NULL
    LIMIT 1;

    -- If no organization exists, create a simple one
    IF v_test_org_party_id IS NULL THEN
        -- Create test organization first
        SELECT drh_stateless_research_study.create_organization(
            p_name => 'Remove Users Test Org ' || extract(epoch from now())::text,
            p_identifier_system_value => NULL,
            p_alias => NULL,
            p_type_code => NULL,
            p_type_display => 'test',
            p_city => 'Test City',
            p_state => 'Test State',
            p_country => 'Test Country',
            p_website_url => NULL,
            p_createdby => 'TEST_USER'
        ) INTO v_create_result;

        v_test_org_party_id := v_create_result->>'organization_party_id';
        v_test_org_id := v_create_result->>'organization_id';
    END IF;

    -- Create first test user
    SELECT drh_stateless_research_study.create_practitioner_profile(
        p_fullname => 'Remove Test User 1',
        p_source => 'ORCiD',
        p_email => ARRAY[v_test_emails[1]],
        p_organization_party_id => v_test_org_party_id,
        p_orcid => '0000-0000-0000-1111',
        p_github => NULL,
        p_user_name => v_test_username1,
        p_activity_json => NULL
    ) INTO v_create_result;

    v_user_account_id1 := v_create_result->>'user_account_id';
    v_party_id1 := v_create_result->>'practitioner_party_id';

    -- Create second test user
    SELECT drh_stateless_research_study.create_practitioner_profile(
        p_fullname => 'Remove Test User 2',
        p_source => 'GitHub',
        p_email => ARRAY[v_test_emails[2]],
        p_organization_party_id => v_test_org_party_id,
        p_orcid => NULL,
        p_github => 'removeuser2',
        p_user_name => v_test_username2,
        p_activity_json => NULL
    ) INTO v_create_result;

    v_user_account_id2 := v_create_result->>'user_account_id';
    v_party_id2 := v_create_result->>'practitioner_party_id';

    -- Verify test users were created
    RETURN NEXT ok(
        v_user_account_id1 IS NOT NULL AND v_user_account_id2 IS NOT NULL,
        'Test users setup completed successfully'
    );

    -- ===========================================
    -- TEST 1: Function existence
    -- ===========================================

    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'drh_stateless_authentication'
            AND p.proname = 'remove_users_by_name'
        ),
        'Function remove_users_by_name exists'
    );

    -- ===========================================
    -- TEST 2: Remove multiple users
    -- ===========================================

    -- Prepare usernames array
    v_test_usernames := ARRAY[v_test_username1, v_test_username2];

    -- Test removing multiple users
    SELECT drh_stateless_authentication.remove_users_by_name(
        user_list => v_test_usernames
    ) INTO v_remove_result;

    -- Extract response values
    v_status := v_remove_result->>'status';
    v_deleted_users := v_remove_result->'deleted_users';

    RETURN NEXT ok(
        v_status = 'success',
        'User removal returns success status'
    );

    RETURN NEXT ok(
        jsonb_array_length(v_deleted_users) = 2,
        'Correct number of users reported as deleted'
    );

    -- Verify users were actually removed from user_account
    RETURN NEXT ok(
        NOT EXISTS (
            SELECT 1 FROM drh_stateful_authentication.user_account
            WHERE user_id IN (v_user_account_id1, v_user_account_id2)
        ),
        'User accounts were removed from database'
    );

    -- Verify party records were removed
    RETURN NEXT ok(
        NOT EXISTS (
            SELECT 1 FROM drh_stateful_party.party
            WHERE party_id IN (v_party_id1, v_party_id2)
        ),
        'Party records were removed from database'
    );

    -- ===========================================
    -- TEST 3: Remove non-existent user
    -- ===========================================

    -- Test removing non-existent user
    SELECT drh_stateless_authentication.remove_users_by_name(
        user_list => ARRAY['nonexistent_user_' || extract(epoch from now())::text]
    ) INTO v_remove_result;

    RETURN NEXT ok(
        (v_remove_result->>'status') = 'success',
        'Removing non-existent user returns success status'
    );

    RETURN NEXT ok(
        jsonb_array_length(v_remove_result->'deleted_users') = 0,
        'No users reported as deleted for non-existent user'
    );

END;
$function$;

-- ===========================================
-- Unit test for revert_practitioner_profile_for_author_inv function
-- ===========================================

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_revert_practitioner_profile_unit()
RETURNS SETOF text
LANGUAGE plpgsql
AS $function$
DECLARE
    -- Test data variables
    v_test_org_id TEXT;
    v_test_org_party_id TEXT;
    v_test_study_display_id TEXT := 'REV' || (random() * 1000)::int::text;
    v_test_study_id TEXT;
    v_test_username TEXT := 'revertuser_' || extract(epoch from now())::text;
    v_test_email TEXT := '<EMAIL>';

    -- Function response variables
    v_org_result JSONB;
    v_study_result JSONB;
    v_user_result JSONB;
    v_revert_result JSONB;

    -- Extracted values from responses
    v_user_account_id TEXT;
    v_party_id TEXT;
    v_status TEXT;
    v_deleted_users JSONB;

BEGIN
    -- ===========================================
    -- SETUP: Create test study and user
    -- ===========================================

    -- Get an existing organization from the database
    SELECT
        o.party_id,
        o.id
    INTO v_test_org_party_id, v_test_org_id
    FROM drh_stateful_research_study.organization o
    WHERE o.deleted_at IS NULL
    LIMIT 1;

    -- If no organization exists, create a simple one
    IF v_test_org_party_id IS NULL THEN
        -- Create test organization first
        SELECT drh_stateless_research_study.create_organization(
            p_name => 'Revert Test Org ' || extract(epoch from now())::text,
            p_identifier_system_value => NULL,
            p_alias => NULL,
            p_type_code => NULL,
            p_type_display => 'test',
            p_city => 'Test City',
            p_state => 'Test State',
            p_country => 'Test Country',
            p_website_url => NULL,
            p_createdby => 'TEST_USER'
        ) INTO v_org_result;

        v_test_org_party_id := v_org_result->>'organization_party_id';
        v_test_org_id := v_org_result->>'organization_id';
    END IF;

    -- Create test study
    SELECT drh_stateless_research_study.save_research_study(
        p_study_display_id => v_test_study_display_id,
        p_org_party_id => v_test_org_party_id,
        p_title => 'Revert Test Study',
        p_description => 'Study for testing revert functionality',
        p_created_by => 'TEST_USER',
        p_visibility => 1,
        p_activity_json => NULL
    ) INTO v_study_result;

    v_test_study_id := v_study_result->>'study_id';

    -- Create test user (may or may not succeed based on function logic)
    SELECT drh_stateless_research_study.create_practitioner_profile(
        p_fullname => 'Revert Test User',
        p_source => 'ORCiD',
        p_email => ARRAY[v_test_email],
        p_organization_party_id => v_test_org_party_id,
        p_orcid => '0000-0000-0000-9999',
        p_github => NULL,
        p_user_name => v_test_username,
        p_activity_json => NULL
    ) INTO v_user_result;

    v_user_account_id := v_user_result->>'user_account_id';
    v_party_id := v_user_result->>'practitioner_party_id';

    -- Verify test setup (study is required, user creation may vary)
    RETURN NEXT ok(
        v_test_study_id IS NOT NULL AND v_test_study_display_id IS NOT NULL,
        'Test study and user setup completed successfully'
    );

    -- ===========================================
    -- TEST 1: Function existence
    -- ===========================================

    RETURN NEXT ok(
        EXISTS (
            SELECT 1 FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'drh_stateless_research_study'
            AND p.proname = 'revert_practitioner_profile_for_author_inv'
        ),
        'Function revert_practitioner_profile_for_author_inv exists'
    );

    -- ===========================================
    -- TEST 2: Basic revert functionality
    -- ===========================================

    -- Note: This function is complex and requires specific study associations
    -- For basic testing, we'll test with a study that may not have associations
    SELECT drh_stateless_research_study.revert_practitioner_profile_for_author_inv(
        p_study_display_id => v_test_study_display_id
    ) INTO v_revert_result;

    -- Extract response values
    v_status := v_revert_result->>'status';
    v_deleted_users := v_revert_result->'deleted_users';

    RETURN NEXT ok(
        v_status = 'success',
        'Revert function returns success status'
    );

    RETURN NEXT ok(
        v_deleted_users IS NOT NULL,
        'Revert function returns deleted_users array'
    );

    -- ===========================================
    -- TEST 3: Non-existent study
    -- ===========================================

    -- Test with non-existent study
    SELECT drh_stateless_research_study.revert_practitioner_profile_for_author_inv(
        p_study_display_id => 'NONEXISTENT_STUDY_' || extract(epoch from now())::text
    ) INTO v_revert_result;

    RETURN NEXT ok(
        (v_revert_result->>'status') = 'success',
        'Revert with non-existent study returns success status'
    );

    RETURN NEXT ok(
        jsonb_array_length(v_revert_result->'deleted_users') = 0,
        'No users deleted for non-existent study'
    );

END;
$function$;
