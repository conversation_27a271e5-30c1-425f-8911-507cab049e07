-- Set client encoding and suppress warnings
SET client_encoding = 'UTF-8';
SET client_min_messages = warning;

-- Create the test schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS "drh_udi_assurance";

-- Create pgTAP extension in the test schema
CREATE EXTENSION IF NOT EXISTS pgtap SCHEMA drh_udi_assurance;

-- Reset client messages
RESET client_min_messages;

-- Set search path
SET search_path TO drh_udi_assurance, drh_stateless_research_study, drh_stateful_research_study, drh_stateful_party, drh_stateful_master, drh_stateless_util, drh_stateless_activity_audit, public;

-- Load the test functions
\ir ./004-idempotent-save-research-study-unit-test.psql

-- Begin the test transaction
BEGIN;

-- Run only the comprehensive test
SELECT * FROM drh_udi_assurance.test_save_research_study_comprehensive();

-- Finish and display results
SELECT * FROM drh_udi_assurance.finish();

-- Rollback to clean up test data
ROLLBACK;

\echo 'Comprehensive test completed.'
