/*pgTap function to test organization creation function only*/

--------Test function for create_organization function----------------
 
 DROP FUNCTION IF EXISTS drh_udi_assurance.test_create_organization(text);

CREATE OR REPLACE FUNCTION drh_udi_assurance.test_create_organization()
 RETURNS SETOF text
 LANGUAGE plpgsql
AS $function$
DECLARE    
    v_org_id TEXT;
    v_party_id TEXT;
    v_result JSONB;
    v_duplicate_result JSONB;
    err_context TEXT;
    err_state TEXT;
    err_message TEXT;
    err_detail TEXT;
    err_hint TEXT;
    error_details_json JSONB;
    function_name TEXT := 'create_organization';
    current_query TEXT := pg_catalog.current_query();
    v_table_schema TEXT := 'drh_stateful_research_study';
    v_table_name TEXT := 'organization';
    v_party_id_pk TEXT := drh_stateless_util.get_unique_id()::TEXT;
BEGIN
    -- Test Case 1: Create first organization (should succeed)
    SELECT drh_stateless_research_study.create_organization(
        'Test Organization_2',
        '[{"system":"http://example.com","value":"12345"}]'::jsonb,
        'Test Alias',
        'RESEARCH',
        'Research Organization',
        'Test City',
        'Test State',
        'Test Country',
        'http://test.org',
        'test_user',
        '12.3456',
        '78.9101'
    ) INTO v_result;

    -- First verify that we got a success response
    RETURN NEXT ok(
        v_result->>'status' = 'success',
        'Organization creation should return success status'
    );

    -- Extract the generated organization and party IDs only if creation was successful
    IF v_result->>'status' = 'success' THEN
        v_org_id := v_result->>'organization_id';
        v_party_id := v_result->>'organization_party_id';

        RETURN NEXT isnt(v_org_id, NULL, 'Organization ID should not be null');
        RETURN NEXT isnt(v_party_id, NULL, 'Party ID should not be null');

        -- Verify that the organization exists in the database
        RETURN NEXT ok(
            EXISTS (SELECT 1 FROM drh_stateful_research_study.organization WHERE id = v_org_id),
            'Organization should exist in the database'
        );

        -- Verify that the party exists in the database
        RETURN NEXT ok(
            EXISTS (SELECT 1 FROM drh_stateful_party.party WHERE party_id = v_party_id),
            'Party should exist in the database'
        );
    ELSE
        RETURN NEXT fail('Organization creation failed: ' || (v_result->>'message')::text);
    END IF;

    -- Test Case 2: Attempt to create organization with same name (should fail)
    SELECT drh_stateless_research_study.create_organization(
        'Test Organization_2',  -- Same name as before
        '[{"system":"http://example.com","value":"12346"}]'::jsonb,
        'Test Alias 2',
        'RESEARCH',
        'Research Organization',
        'Test City',
        'Test State',
        'Test Country',
        'http://test.org',
        'test_user',
        '12.3456',
        '78.9101'
    ) INTO v_duplicate_result;

    -- Verify duplicate check worked
    RETURN NEXT ok(
        v_duplicate_result->>'status' = 'failure',
        'Duplicate organization creation should fail'
    );

    RETURN NEXT ok(
        v_duplicate_result->>'message' = 'Organization already exists',
        'Correct error message for duplicate organization'
    );

    -- Test NULL parameters (exception handling)
    BEGIN

       SELECT drh_stateless_research_study.create_organization(
    'Test Org', '{}'::jsonb, 'Alias', 'TYPE_CODE', 'Type Display', 
    'New York', 'NY', 'USA', 'https://example.com', 'admin', 'invalid_lat', 'invalid_long'
);


        RETURN NEXT fail('Should have raised an exception for NULL parameters');
    EXCEPTION
        WHEN OTHERS THEN
            RETURN NEXT pass('Exception raised for NULL parameters as expected');
    END;
    
END;
$function$;
