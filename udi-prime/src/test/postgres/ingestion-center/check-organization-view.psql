-- Check organization_party_view structure and data
\echo 'Checking organization_party_view...'

-- Check if the view exists
SELECT 
    schemaname, 
    viewname, 
    definition 
FROM pg_views 
WHERE schemaname = 'drh_stateless_research_study' 
AND viewname = 'organization_party_view';

\echo 'View columns:'
-- Check view columns
SELECT 
    column_name, 
    data_type, 
    is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'drh_stateless_research_study' 
AND table_name = 'organization_party_view'
ORDER BY ordinal_position;

\echo 'Sample data from organization_party_view:'
-- Check sample data
SELECT * FROM drh_stateless_research_study.organization_party_view LIMIT 5;

\echo 'Checking organization table structure:'
-- Check organization table columns
SELECT 
    column_name, 
    data_type, 
    is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'drh_stateful_research_study' 
AND table_name = 'organization'
ORDER BY ordinal_position;
