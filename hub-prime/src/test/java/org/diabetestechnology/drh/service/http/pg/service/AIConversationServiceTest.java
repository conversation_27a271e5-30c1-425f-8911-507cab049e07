package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.diabetestechnology.drh.service.http.pg.request.AIConversationRequest;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class AIConversationServiceTest {

    @Mock
    private DSLContext dsl;

    @InjectMocks
    private AIConversationService aiConversationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveAIConversation_Success() {

        AIConversationRequest.Message message = new AIConversationRequest.Message(
                "testUser",
                "<p>Hello AI</p>",
                "user",
                "2023-12-01T10:00:00Z",
                "user123");
        AIConversationRequest request = new AIConversationRequest(
                message,
                "provider123",
                "chat_section",
                "party456");

        JSONB expectedResult = JSONB.valueOf("{\"status\":\"success\",\"id\":\"conv123\"}");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB result = aiConversationService.saveAIConversation(request);

        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveAIConversation_Exception() {

        AIConversationRequest.Message message = new AIConversationRequest.Message(
                "testUser",
                "<p>Hello AI</p>",
                "user",
                "2023-12-01T10:00:00Z",
                "user123");
        AIConversationRequest request = new AIConversationRequest(
                message,
                "provider123",
                "chat_section",
                "party456");

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            aiConversationService.saveAIConversation(request);
        });

        assertEquals("Failed to save AI conversation", exception.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAIConversation_Success() {

        String contextSection = "chat_section";
        String providerId = "provider123";
        JSONB expectedResult = JSONB.valueOf("[{\"user\":\"testUser\",\"message\":\"Hello\"}]");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> whereStepMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<JSONB>> andStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("jsonb_agg(message_json)", JSONB.class))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_ai_insights.ai_conversation_log_view")).thenReturn(fromStepMock);
        when(fromStepMock.where(DSL.field("context_section").eq(contextSection))).thenReturn(whereStepMock);
        when(whereStepMock.and(DSL.field("auth_provider_id").eq(providerId))).thenReturn(andStepMock);
        when(andStepMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB result = aiConversationService.getAIConversation(contextSection, providerId);

        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAIConversation_NoResults() {

        String contextSection = "empty_section";
        String providerId = "provider123";

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> whereStepMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<JSONB>> andStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("jsonb_agg(message_json)", JSONB.class))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_ai_insights.ai_conversation_log_view")).thenReturn(fromStepMock);
        when(fromStepMock.where(DSL.field("context_section").eq(contextSection))).thenReturn(whereStepMock);
        when(whereStepMock.and(DSL.field("auth_provider_id").eq(providerId))).thenReturn(andStepMock);
        when(andStepMock.fetchOneInto(JSONB.class)).thenReturn(null);

        JSONB result = aiConversationService.getAIConversation(contextSection, providerId);

        assertEquals(null, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveAIConversation_WithMinimalRequest() {

        AIConversationRequest.Message message = new AIConversationRequest.Message(
                "testUser",
                "<p>Minimal message</p>",
                "assistant",
                "2023-12-01T11:00:00Z",
                "user456");
        AIConversationRequest request = new AIConversationRequest(message, "minimal_section");

        JSONB expectedResult = JSONB.valueOf("{\"status\":\"success\",\"id\":\"conv456\"}");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB result = aiConversationService.saveAIConversation(request);

        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAIConversation_DatabaseException() {

        String contextSection = "error_section";
        String providerId = "provider123";

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database connection failed"));

        assertThrows(RuntimeException.class, () -> {
            aiConversationService.getAIConversation(contextSection, providerId);
        });
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveAIConversation_WithNullMessage() {

        AIConversationRequest request = new AIConversationRequest(
                null,
                "provider123",
                "chat_section",
                "party456");

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Null pointer"));

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            aiConversationService.saveAIConversation(request);
        });

        assertEquals("Failed to save AI conversation", exception.getMessage());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetAIConversation_WithEmptyStrings() {

        String contextSection = "";
        String providerId = "";
        JSONB expectedResult = JSONB.valueOf("[]");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> whereStepMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<JSONB>> andStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("jsonb_agg(message_json)", JSONB.class))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_ai_insights.ai_conversation_log_view")).thenReturn(fromStepMock);
        when(fromStepMock.where(DSL.field("context_section").eq(contextSection))).thenReturn(whereStepMock);
        when(whereStepMock.and(DSL.field("auth_provider_id").eq(providerId))).thenReturn(andStepMock);
        when(andStepMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB result = aiConversationService.getAIConversation(contextSection, providerId);

        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testSaveAIConversation_WithComplexMessage() {

        AIConversationRequest.Message message = new AIConversationRequest.Message(
                "Dr. Smith",
                "<div><h1>Complex HTML</h1><p>With <strong>formatting</strong></p></div>",
                "user",
                "2023-12-01T15:30:45Z",
                "doctor123");
        AIConversationRequest request = new AIConversationRequest(
                message,
                "oauth_provider_456",
                "medical_consultation",
                "party_789");

        JSONB expectedResult = JSONB
                .valueOf("{\"status\":\"success\",\"id\":\"conv789\",\"timestamp\":\"2023-12-01T15:30:45Z\"}");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB result = aiConversationService.saveAIConversation(request);

        assertNotNull(result);
        assertEquals(expectedResult, result);
    }
}
