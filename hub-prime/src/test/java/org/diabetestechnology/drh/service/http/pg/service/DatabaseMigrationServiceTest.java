package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doReturn;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.List;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.request.DatabaseMigrationRequest;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.Result;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;

import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

@ExtendWith(MockitoExtension.class)
public class DatabaseMigrationServiceTest {

    @Mock
    private S3FileUploadService s3FileUploadService;

    @Mock
    private DSLContext duckDsl;

    @Mock
    private DSLContext dsl;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;

    @Mock
    private MultipartFile mockFile;

    @Mock
    private Record1<String> mockRecord;

    private DatabaseMigrationService databaseMigrationService;

    private DatabaseMigrationRequest testRequest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        testRequest = new DatabaseMigrationRequest("STUDY123", "ORG456");

        // Create the service manually with mocked dependencies
        databaseMigrationService = new DatabaseMigrationService(
                s3FileUploadService,
                duckDsl,
                dsl,
                userNameService,
                partyService);
    }

    @Test
    void testInit_Success() {
        // Test the @PostConstruct init method - just verify it doesn't throw exceptions
        assertDoesNotThrow(() -> databaseMigrationService.init());
    }

    @Test
    void testConstructor_Success() {
        // Test that the service can be constructed with all dependencies
        DatabaseMigrationService service = new DatabaseMigrationService(
                s3FileUploadService, duckDsl, dsl, userNameService, partyService);
        assertNotNull(service);
    }

    @Test
    void testUploadAndSaveDBFile_FileUploadFails() throws IOException {
        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest)).thenReturn(null);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest));

        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("Failed to upload file to S3") ||
                exception.getMessage().contains("Failed to migrate database"));
    }

    @Test
    void testUploadAndSaveDBFile_ExceptionHandling() throws IOException {
        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest))
                .thenThrow(new RuntimeException("S3 upload failed"));

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest));

        assertNotNull(exception);
        assertTrue(exception.getMessage().contains("Failed to migrate database"));
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUploadAndSaveDBFile_DatabaseAlreadyExists() throws Exception {

        String fileUrl = "https://s3.amazonaws.com/bucket/test.db";
        String tempFilePath = "/tmp/test.db";
        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest)).thenReturn(fileUrl);
        lenient().when(s3FileUploadService.saveFileToTempLocation(mockFile)).thenReturn(tempFilePath);

        // Mock the exists method to return true (database already exists)
        org.jooq.SelectSelectStep<org.jooq.Record1<Integer>> selectStep = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<Integer>> fromStep = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<Integer>> whereStep = mock(org.jooq.SelectConditionStep.class);

        // Mock the exists method to return true (database already exists)
        // The exists method calls:
        // dsl.fetchExists(dsl.selectOne().from("drh_stateless_raw_data.cgm_raw_db_view").where("study_id
        // = ?", studyd))
        lenient().doReturn(selectStep).when(dsl).selectOne();
        lenient().doReturn(fromStep).when(selectStep).from("drh_stateless_raw_data.cgm_raw_db_view");
        lenient().doReturn(whereStep).when(fromStep).where("study_id = ?", testRequest.studyId());
        lenient().doReturn(true).when(dsl).fetchExists(whereStep); // Simulate record exists

        // Mock the detach database operations to prevent null pointer exceptions
        Result<Record> mockResult = mock(Result.class);
        lenient().when(duckDsl.fetch("SHOW DATABASES;")).thenReturn(mockResult);
        lenient().when(mockResult.getValues(0, String.class)).thenReturn(Collections.emptyList());

        Result<Record> mockPragmaResult = mock(Result.class);
        lenient().when(duckDsl.fetch("PRAGMA database_list;")).thenReturn(mockPragmaResult);
        lenient().when(mockPragmaResult.iterator()).thenReturn(Collections.emptyIterator());

        // Mock DuckDB operations that might be called during the process
        lenient().when(duckDsl.execute(anyString())).thenReturn(0);

        // Mock fetchOne operations that might be called
        Record mockDbFileRecord = mock(Record.class);
        lenient().when(mockDbFileRecord.get("db_file_id", String.class)).thenReturn("test-db-file-id");
        lenient().when(duckDsl.fetchOne(anyString())).thenReturn(mockDbFileRecord);

        // Mock selectDistinct operations that might be called
        org.jooq.SelectSelectStep<org.jooq.Record1<String>> selectDistinctMock = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<String>> fromDistinctMock = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<String>> whereDistinctMock = mock(
                org.jooq.SelectConditionStep.class);
        org.jooq.Result<org.jooq.Record1<String>> resultMock = mock(org.jooq.Result.class);

        lenient().when(duckDsl.selectDistinct(any(org.jooq.Field.class))).thenReturn(selectDistinctMock);
        lenient().when(selectDistinctMock.from(anyString())).thenReturn(fromDistinctMock);
        lenient().when(fromDistinctMock.where(any(org.jooq.Condition.class))).thenReturn(whereDistinctMock);
        lenient().when(whereDistinctMock.fetch()).thenReturn(resultMock);
        lenient().when(resultMock.getValues(0, String.class)).thenReturn(Collections.singletonList("test-db-file-id"));

        String result = databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest);

        assertEquals("Database already exists for study: " + testRequest.studyId(), result);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testUploadAndSaveDBFile_SaveFileToTempLocationFails() throws Exception {

        String fileUrl = "https://s3.amazonaws.com/bucket/test.db";
        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest)).thenReturn(fileUrl);
        lenient().when(s3FileUploadService.saveFileToTempLocation(mockFile)).thenReturn(null);

        lenient().when(dsl.fetchExists(any(org.jooq.Select.class))).thenReturn(false); // Simulate no record exists

        // Mock the detach database operations to prevent null pointer exceptions
        Result<Record> mockResult = mock(Result.class);
        lenient().when(duckDsl.fetch("SHOW DATABASES;")).thenReturn(mockResult);
        lenient().when(mockResult.getValues(0, String.class)).thenReturn(Collections.emptyList());

        Result<Record> mockPragmaResult = mock(Result.class);
        lenient().when(duckDsl.fetch("PRAGMA database_list;")).thenReturn(mockPragmaResult);
        lenient().when(mockPragmaResult.iterator()).thenReturn(Collections.emptyIterator());

        // Mock DuckDB operations that might be called during the process
        lenient().when(duckDsl.execute(anyString())).thenReturn(0);

        // Mock fetchOne operations that might be called
        Record mockDbFileRecord = mock(Record.class);
        lenient().when(mockDbFileRecord.get("db_file_id", String.class)).thenReturn("test-db-file-id");
        lenient().when(duckDsl.fetchOne(anyString())).thenReturn(mockDbFileRecord);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest));

        // The actual exception message is about database migration failure, not file
        // save failure
        assertTrue(exception.getMessage().contains("Failed to migrate database"));
    }

    @Test
    void testUploadAndSaveDBFile_DuckDslException() throws Exception {

        String fileUrl = "https://s3.amazonaws.com/bucket/test.db";
        String tempFilePath = "/tmp/test.db";

        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest)).thenReturn(fileUrl);
        lenient().when(s3FileUploadService.saveFileToTempLocation(mockFile)).thenReturn(tempFilePath);

        lenient().when(dsl.fetchExists(any(org.jooq.Select.class))).thenReturn(false);

        lenient().when(duckDsl.fetchOne(anyString())).thenThrow(new RuntimeException("DuckDB connection failed"));

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest));

        assertTrue(exception.getMessage().contains("Failed to migrate database"));
    }

    @Test
    void testUploadAndSaveDBFile_UserServiceException() throws Exception {

        String fileUrl = "https://s3.amazonaws.com/bucket/test.db";
        String tempFilePath = "/tmp/test.db";
        String distinctDbFileId = "FILE123";

        lenient().when(mockFile.getOriginalFilename()).thenReturn("test.db");
        lenient().when(mockFile.getSize()).thenReturn(1024L);
        lenient().when(s3FileUploadService.uploadDBFileToS3(mockFile, testRequest)).thenReturn(fileUrl);
        lenient().when(s3FileUploadService.saveFileToTempLocation(mockFile)).thenReturn(tempFilePath);

        lenient().when(dsl.fetchExists(any(org.jooq.Select.class))).thenReturn(false);

        @SuppressWarnings("unchecked")
        Record1<String> dbFileIdRecord = mock(Record1.class);
        lenient().when(dbFileIdRecord.get("db_file_id", String.class)).thenReturn(distinctDbFileId);
        lenient().when(duckDsl.fetchOne(anyString())).thenReturn(dbFileIdRecord);

        lenient().when(userNameService.getUserId()).thenThrow(new RuntimeException("User service failed"));

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> databaseMigrationService.uploadAndSaveDBFile(mockFile, testRequest));

        assertTrue(exception.getMessage().contains("Failed to migrate database"));
    }

    @Test
    void testCreateSQLiteDSL_Exception() throws Exception {

        Exception exception = assertThrows(Exception.class, () -> {
            databaseMigrationService.createSQLiteDSL("/invalid/path/test.db");
        });

        assertNotNull(exception);
    }

    @Test
    void testValidateRequiredColumns_Exception() throws Exception {

        Exception exception = assertThrows(Exception.class, () -> {
            databaseMigrationService.validateRequiredColumns("/invalid/path/test.db");
        });

        assertNotNull(exception);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testValidateRequiredColumns_AllColumnsPresent() throws Exception {

        DSLContext mockSqliteDsl = mock(DSLContext.class);

        Result<Record> fileMetaResult = mock(Result.class);
        Record fileMetaRecord1 = mock(Record.class);
        Record fileMetaRecord2 = mock(Record.class);
        Record fileMetaRecord3 = mock(Record.class);
        Record fileMetaRecord4 = mock(Record.class);
        Record fileMetaRecord5 = mock(Record.class);

        when(fileMetaRecord1.get("name", String.class)).thenReturn("file_meta_id");
        when(fileMetaRecord2.get("name", String.class)).thenReturn("db_file_id");
        when(fileMetaRecord3.get("name", String.class)).thenReturn("participant_display_id");
        when(fileMetaRecord4.get("name", String.class)).thenReturn("file_meta_data");
        when(fileMetaRecord5.get("name", String.class)).thenReturn("cgm_data");

        when(fileMetaResult.iterator()).thenReturn(
                List.of(fileMetaRecord1, fileMetaRecord2, fileMetaRecord3, fileMetaRecord4, fileMetaRecord5)
                        .iterator());

        Result<Record> participantResult = mock(Result.class);
        Record participantRecord1 = mock(Record.class);
        Record participantRecord2 = mock(Record.class);
        Record participantRecord3 = mock(Record.class);
        Record participantRecord4 = mock(Record.class);
        Record participantRecord5 = mock(Record.class);
        Record participantRecord6 = mock(Record.class);
        Record participantRecord7 = mock(Record.class);
        Record participantRecord8 = mock(Record.class);
        Record participantRecord9 = mock(Record.class);
        Record participantRecord10 = mock(Record.class);
        Record participantRecord11 = mock(Record.class);
        Record participantRecord12 = mock(Record.class);
        Record participantRecord13 = mock(Record.class);
        Record participantRecord14 = mock(Record.class);
        Record participantRecord15 = mock(Record.class);

        when(participantRecord1.get("name", String.class)).thenReturn("db_file_id");
        when(participantRecord2.get("name", String.class)).thenReturn("tenant_id");
        when(participantRecord3.get("name", String.class)).thenReturn("study_display_id");
        when(participantRecord4.get("name", String.class)).thenReturn("participant_display_id");
        when(participantRecord5.get("name", String.class)).thenReturn("site_id");
        when(participantRecord6.get("name", String.class)).thenReturn("diagnosis_icd");
        when(participantRecord7.get("name", String.class)).thenReturn("med_rxnorm");
        when(participantRecord8.get("name", String.class)).thenReturn("treatment_modality");
        when(participantRecord9.get("name", String.class)).thenReturn("gender");
        when(participantRecord10.get("name", String.class)).thenReturn("race_ethnicity");
        when(participantRecord11.get("name", String.class)).thenReturn("age");
        when(participantRecord12.get("name", String.class)).thenReturn("bmi");
        when(participantRecord13.get("name", String.class)).thenReturn("baseline_hba1c");
        when(participantRecord14.get("name", String.class)).thenReturn("diabetes_type");
        when(participantRecord15.get("name", String.class)).thenReturn("study_arm");

        when(participantResult.iterator()).thenReturn(
                List.of(participantRecord1, participantRecord2, participantRecord3, participantRecord4,
                        participantRecord5, participantRecord6, participantRecord7, participantRecord8,
                        participantRecord9, participantRecord10, participantRecord11, participantRecord12,
                        participantRecord13, participantRecord14, participantRecord15).iterator());

        when(mockSqliteDsl.fetch("PRAGMA table_info(file_meta_ingest_data)")).thenReturn(fileMetaResult);
        when(mockSqliteDsl.fetch("PRAGMA table_info(participant)")).thenReturn(participantResult);

        try {
            Method createSQLiteDSLMethod = DatabaseMigrationService.class.getDeclaredMethod("createSQLiteDSL",
                    String.class);
            createSQLiteDSLMethod.setAccessible(true);

            DatabaseMigrationService spyService = spy(databaseMigrationService);
            doReturn(mockSqliteDsl).when(spyService).createSQLiteDSL(anyString());

            boolean result = spyService.validateRequiredColumns("/valid/path/test.db");
            assertTrue(result);

        } catch (Exception e) {
            fail("Failed to test validateRequiredColumns with all columns present: " + e.getMessage());
        }
    }

    @Test
    @SuppressWarnings("unchecked")
    void testValidateRequiredColumns_MissingColumns() throws Exception {

        DSLContext mockSqliteDsl = mock(DSLContext.class);

        Result<Record> fileMetaResult = mock(Result.class);
        Record fileMetaRecord1 = mock(Record.class);
        Record fileMetaRecord2 = mock(Record.class);
        Record fileMetaRecord3 = mock(Record.class);

        lenient().when(fileMetaRecord1.get("name", String.class)).thenReturn("file_meta_id");
        lenient().when(fileMetaRecord2.get("name", String.class)).thenReturn("db_file_id");
        lenient().when(fileMetaRecord3.get("name", String.class)).thenReturn("participant_display_id");

        lenient().when(fileMetaResult.iterator()).thenReturn(
                List.of(fileMetaRecord1, fileMetaRecord2, fileMetaRecord3).iterator());

        lenient().when(mockSqliteDsl.fetch("PRAGMA table_info(file_meta_ingest_data)")).thenReturn(fileMetaResult);

        // Only return empty result for participant table to simulate missing columns
        Result<Record> participantResult = mock(Result.class);
        lenient().when(participantResult.iterator()).thenReturn(Collections.emptyIterator());
        lenient().when(mockSqliteDsl.fetch("PRAGMA table_info(participant)")).thenReturn(participantResult);

        try {
            DatabaseMigrationService spyService = spy(databaseMigrationService);
            doReturn(mockSqliteDsl).when(spyService).createSQLiteDSL(anyString());

            boolean result = spyService.validateRequiredColumns("/valid/path/test.db");
            assertFalse(result);

        } catch (Exception e) {
            fail("Failed to test validateRequiredColumns with missing columns: " + e.getMessage());
        }
    }

    @Test
    void testValidateParticipantData_Exception() throws Exception {

        Exception exception = assertThrows(Exception.class, () -> {
            databaseMigrationService.validateParticipantData("/invalid/path/test.db");
        });

        assertNotNull(exception);
    }

    @Test
    void testPrepareJson_Success() {
        try {
            Method prepareJsonMethod = DatabaseMigrationService.class.getDeclaredMethod("prepareJson",
                    String.class, String.class, String.class, String.class, long.class,
                    String.class, String.class, String.class);
            prepareJsonMethod.setAccessible(true);

            String result = (String) prepareJsonMethod.invoke(databaseMigrationService,
                    "FILE123", "test", "https://s3.amazonaws.com/test.db", "2024-01-01T00:00:00Z",
                    1024L, "STUDY123", "PARTY123", "ORG456");

            assertNotNull(result);
            assertTrue(result.contains("FILE123"));
            assertTrue(result.contains("test"));
            assertTrue(result.contains("STUDY123"));
        } catch (Exception e) {
            fail("Failed to test prepareJson method: " + e.getMessage());
        }
    }

    @Test
    void testExists_ReturnsTrue() {
        try {
            Method existsMethod = DatabaseMigrationService.class.getDeclaredMethod("exists", String.class);
            existsMethod.setAccessible(true);

            assertNotNull(existsMethod);
            assertEquals("exists", existsMethod.getName());
            assertEquals(1, existsMethod.getParameterCount());
            assertEquals(String.class, existsMethod.getParameterTypes()[0]);
        } catch (Exception e) {
            fail("Failed to test exists method: " + e.getMessage());
        }
    }

    @Test
    void testExists_ReturnsFalse() {
        try {
            Method existsMethod = DatabaseMigrationService.class.getDeclaredMethod("exists", String.class);
            existsMethod.setAccessible(true);

            assertEquals(boolean.class, existsMethod.getReturnType());
            assertTrue(existsMethod.canAccess(databaseMigrationService));
        } catch (Exception e) {
            fail("Failed to test exists method: " + e.getMessage());
        }
    }

    @Test
    void testDetachSqliteDatabase_Success() {
        try {
            Method detachSqliteMethod = DatabaseMigrationService.class.getDeclaredMethod("detachSqliteDatabase");
            detachSqliteMethod.setAccessible(true);

            assertNotNull(detachSqliteMethod);
            assertEquals("detachSqliteDatabase", detachSqliteMethod.getName());
            assertEquals(0, detachSqliteMethod.getParameterCount());
            assertEquals(void.class, detachSqliteMethod.getReturnType());
        } catch (Exception e) {
            fail("Failed to test detachSqliteDatabase method: " + e.getMessage());
        }
    }

    @Test
    void testDetachPostgresDatabase_Success() {
        try {

            when(duckDsl.execute(anyString())).thenReturn(0);
            @SuppressWarnings("unchecked")
            List<String> mockDatabases = mock(List.class);
            when(mockDatabases.contains(anyString())).thenReturn(true);
            @SuppressWarnings("unchecked")
            Result<Record> mockResult = mock(Result.class);
            when(mockResult.getValues(anyInt(), eq(String.class))).thenReturn(mockDatabases);
            lenient().when(duckDsl.fetch(anyString())).thenReturn(mockResult);

            Method detachPostgresMethod = DatabaseMigrationService.class.getDeclaredMethod("detachPostgresDatabase");
            detachPostgresMethod.setAccessible(true);

            java.lang.reflect.Field duckDslField = DatabaseMigrationService.class.getDeclaredField("duckDsl");
            duckDslField.setAccessible(true);
            duckDslField.set(databaseMigrationService, duckDsl);

            assertDoesNotThrow(() -> {
                try {
                    detachPostgresMethod.invoke(databaseMigrationService);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        } catch (Exception e) {
            fail("Failed to test detachPostgresDatabase method: " + e.getMessage());
        }
    }

    @Test
    void testAttachPostgresDatabase_Success() {
        try {

            when(duckDsl.execute(anyString())).thenReturn(0);

            Method attachPostgresMethod = DatabaseMigrationService.class.getDeclaredMethod("attachPostgresDatabase");
            attachPostgresMethod.setAccessible(true);

            java.lang.reflect.Field duckDslField = DatabaseMigrationService.class.getDeclaredField("duckDsl");
            duckDslField.setAccessible(true);
            duckDslField.set(databaseMigrationService, duckDsl);

            assertDoesNotThrow(() -> {
                try {
                    attachPostgresMethod.invoke(databaseMigrationService);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            verify(duckDsl, times(2)).execute(anyString());
        } catch (Exception e) {
            fail("Failed to test attachPostgresDatabase method: " + e.getMessage());
        }
    }

    @Test
    void testAttachSqliteDatabase_Success() {
        try {

            when(duckDsl.execute(anyString())).thenReturn(0);

            Method attachSqliteMethod = DatabaseMigrationService.class.getDeclaredMethod("attachSqliteDatabase",
                    String.class);
            attachSqliteMethod.setAccessible(true);

            java.lang.reflect.Field duckDslField = DatabaseMigrationService.class.getDeclaredField("duckDsl");
            duckDslField.setAccessible(true);
            duckDslField.set(databaseMigrationService, duckDsl);

            assertDoesNotThrow(() -> {
                try {
                    attachSqliteMethod.invoke(databaseMigrationService, "/tmp/test.db");
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            verify(duckDsl, times(2)).execute(anyString());
        } catch (Exception e) {
            fail("Failed to test attachSqliteDatabase method: " + e.getMessage());
        }
    }

    @Test
    void testIsDatabaseAttached_ReturnsTrue() {
        try {
            // Mock the DSL context behavior
            @SuppressWarnings("unchecked")
            Result<Record> mockResult = mock(Result.class);
            Record mockRecord = mock(Record.class);
            when(mockRecord.get("name")).thenReturn("sqlite_study_db");
            when(mockResult.iterator()).thenReturn(List.of(mockRecord).iterator());
            lenient().when(duckDsl.fetch(anyString())).thenReturn(mockResult);

            Method isDatabaseAttachedMethod = DatabaseMigrationService.class.getDeclaredMethod("isDatabaseAttached",
                    String.class);
            isDatabaseAttachedMethod.setAccessible(true);

            // Inject the mock DSL context
            java.lang.reflect.Field duckDslField = DatabaseMigrationService.class.getDeclaredField("duckDsl");
            duckDslField.setAccessible(true);
            duckDslField.set(databaseMigrationService, duckDsl);

            boolean result = (boolean) isDatabaseAttachedMethod.invoke(databaseMigrationService, "sqlite_study_db");
            assertTrue(result);
        } catch (Exception e) {
            fail("Failed to test isDatabaseAttached method: " + e.getMessage());
        }
    }

    @Test
    void testIsDatabaseAttached_ReturnsFalse() {
        try {
            // Mock the DSL context behavior
            @SuppressWarnings("unchecked")
            Result<Record> mockResult = mock(Result.class);
            Record mockRecord = mock(Record.class);
            when(mockRecord.get("name")).thenReturn("other_db");
            when(mockResult.iterator()).thenReturn(List.of(mockRecord).iterator());
            lenient().when(duckDsl.fetch(anyString())).thenReturn(mockResult);

            Method isDatabaseAttachedMethod = DatabaseMigrationService.class.getDeclaredMethod("isDatabaseAttached",
                    String.class);
            isDatabaseAttachedMethod.setAccessible(true);

            // Inject the mock DSL context
            java.lang.reflect.Field duckDslField = DatabaseMigrationService.class.getDeclaredField("duckDsl");
            duckDslField.setAccessible(true);
            duckDslField.set(databaseMigrationService, duckDsl);

            boolean result = (boolean) isDatabaseAttachedMethod.invoke(databaseMigrationService, "sqlite_study_db");
            assertFalse(result);
        } catch (Exception e) {
            fail("Failed to test isDatabaseAttached method: " + e.getMessage());
        }
    }

    @Test
    void testCopyTablesFromSqLiteToPostgres_Exception() {
        try {
            Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                    String.class, String.class, String.class, JSONB.class);
            copyTablesMethod.setAccessible(true);

            // Test with invalid parameters to trigger exception
            Exception exception = assertThrows(Exception.class, () -> {
                try {
                    copyTablesMethod.invoke(databaseMigrationService,
                            "STUDY123", "/invalid/path/test.db", "FILE123", JSONB.valueOf("{}"));
                } catch (java.lang.reflect.InvocationTargetException e) {
                    throw e.getCause();
                }
            });

            assertNotNull(exception);
        } catch (Exception e) {
            fail("Failed to test copyTablesFromSqLiteToPostgres method: " + e.getMessage());
        }
    }

    @Test
    void testMigrateDdatabase_Exception() {
        try {
            Method migrateDatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("migrateDdatabase",
                    String.class, String.class, String.class, JSONB.class);
            migrateDatabaseMethod.setAccessible(true);

            assertDoesNotThrow(() -> {
                try {
                    Object result = migrateDatabaseMethod.invoke(databaseMigrationService,
                            "STUDY123", "/invalid/path/test.db", "FILE123", JSONB.valueOf("{}"));
                    assertNotNull(result);
                } catch (Exception e) {

                }
            });
        } catch (Exception e) {
            fail("Failed to test migrateDdatabase method: " + e.getMessage());
        }
    }

    @Test
    @SuppressWarnings("unchecked")
    void testDetachSqliteDatabase_DatabaseAttached() throws Exception {

        Result<Record> attachedDatabases = mock(Result.class);
        Record mockDbRecord = mock(Record.class);

        lenient().when(duckDsl.fetch("PRAGMA database_list;")).thenReturn(attachedDatabases);
        lenient().when(attachedDatabases.iterator()).thenReturn(List.of(mockDbRecord).iterator());
        lenient().when(mockDbRecord.get("name")).thenReturn("sqlite_study_db");

        Method detachSqliteDatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("detachSqliteDatabase");
        detachSqliteDatabaseMethod.setAccessible(true);

        detachSqliteDatabaseMethod.invoke(databaseMigrationService);

        verify(duckDsl).execute("DETACH sqlite_study_db;");
    }

    @Test
    @SuppressWarnings("unchecked")
    void testDetachSqliteDatabase_DatabaseNotAttached() throws Exception {

        Result<Record> attachedDatabases = mock(Result.class);
        Record mockDbRecord = mock(Record.class);

        lenient().when(duckDsl.fetch("PRAGMA database_list;")).thenReturn(attachedDatabases);
        lenient().when(attachedDatabases.iterator()).thenReturn(List.of(mockDbRecord).iterator());
        lenient().when(mockDbRecord.get("name")).thenReturn("other_db");

        Method detachSqliteDatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("detachSqliteDatabase");
        detachSqliteDatabaseMethod.setAccessible(true);

        detachSqliteDatabaseMethod.invoke(databaseMigrationService);

        verify(duckDsl, never()).execute("DETACH sqlite_study_db;");
    }

    @Test
    @SuppressWarnings("unchecked")
    void testDetachSqliteDatabase_EmptyDatabaseList() throws Exception {

        Result<Record> attachedDatabases = mock(Result.class);

        lenient().when(duckDsl.fetch("PRAGMA database_list;")).thenReturn(attachedDatabases);
        lenient().when(attachedDatabases.iterator()).thenReturn(Collections.emptyIterator());

        Method detachSqliteDatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("detachSqliteDatabase");
        detachSqliteDatabaseMethod.setAccessible(true);

        detachSqliteDatabaseMethod.invoke(databaseMigrationService);

        verify(duckDsl, never()).execute("DETACH sqlite_study_db;");
    }

    @Test
    @SuppressWarnings("unchecked")
    void testCopyTablesFromSqLiteToPostgres_StudyDisplayIdMismatch() throws Exception {

        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{}");

        Record sqliteRecord = mock(Record.class);

        lenient().when(duckDsl.fetchOne("SELECT DISTINCT study_display_id FROM sqlite_study_db.participant"))
                .thenReturn(sqliteRecord);
        lenient().when(sqliteRecord.get("study_display_id", String.class)).thenReturn("SQLITE_STUDY_ID");

        org.jooq.SelectSelectStep<org.jooq.Record1<String>> selectStep = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<String>> fromStep = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<String>> whereStep = mock(org.jooq.SelectConditionStep.class);

        lenient().when(dsl.selectDistinct(any(org.jooq.Field.class))).thenReturn(selectStep);
        lenient().when(selectStep.from(anyString())).thenReturn(fromStep);
        lenient().when(fromStep.where(any(org.jooq.Condition.class))).thenReturn(whereStep);
        lenient().when(whereStep.fetchOneInto(String.class)).thenReturn("POSTGRES_STUDY_ID");

        Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                String.class, String.class, String.class, JSONB.class);
        copyTablesMethod.setAccessible(true);

        String result = (String) copyTablesMethod.invoke(databaseMigrationService, studyId, filePath, distinctDbFileIds,
                dbData);

        assertEquals("Study display id mismatch between SQLite and Postgres", result);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testCopyTablesFromSqLiteToPostgres_ValidateRequiredColumnsFails() throws Exception {

        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{}");

        Record sqliteRecord = mock(Record.class);

        lenient().when(duckDsl.fetchOne("SELECT DISTINCT study_display_id FROM sqlite_study_db.participant"))
                .thenReturn(sqliteRecord);
        lenient().when(sqliteRecord.get("study_display_id", String.class)).thenReturn("SAME_STUDY_ID");

        org.jooq.SelectSelectStep<org.jooq.Record1<String>> selectStep = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<String>> fromStep = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<String>> whereStep = mock(org.jooq.SelectConditionStep.class);

        lenient().when(dsl.selectDistinct(any(org.jooq.Field.class))).thenReturn(selectStep);
        lenient().when(selectStep.from(anyString())).thenReturn(fromStep);
        lenient().when(fromStep.where(any(org.jooq.Condition.class))).thenReturn(whereStep);
        lenient().when(whereStep.fetchOneInto(String.class)).thenReturn("SAME_STUDY_ID");

        DatabaseMigrationService spyService = spy(databaseMigrationService);
        doReturn(false).when(spyService).validateRequiredColumns(filePath);

        Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                String.class, String.class, String.class, JSONB.class);
        copyTablesMethod.setAccessible(true);

        String result = (String) copyTablesMethod.invoke(spyService, studyId, filePath, distinctDbFileIds, dbData);

        assertEquals("The Sqlite Tables do not contains all the required fields", result);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testCopyTablesFromSqLiteToPostgres_ValidateParticipantDataFails() throws Exception {

        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{}");

        Record sqliteRecord = mock(Record.class);

        lenient().when(duckDsl.fetchOne("SELECT DISTINCT study_display_id FROM sqlite_study_db.participant"))
                .thenReturn(sqliteRecord);
        lenient().when(sqliteRecord.get("study_display_id", String.class)).thenReturn("SAME_STUDY_ID");

        org.jooq.SelectSelectStep<org.jooq.Record1<String>> selectStep = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<String>> fromStep = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<String>> whereStep = mock(org.jooq.SelectConditionStep.class);

        lenient().when(dsl.selectDistinct(any(org.jooq.Field.class))).thenReturn(selectStep);
        lenient().when(selectStep.from(anyString())).thenReturn(fromStep);
        lenient().when(fromStep.where(any(org.jooq.Condition.class))).thenReturn(whereStep);
        lenient().when(whereStep.fetchOneInto(String.class)).thenReturn("SAME_STUDY_ID");

        DatabaseMigrationService spyService = spy(databaseMigrationService);
        doReturn(true).when(spyService).validateRequiredColumns(filePath);
        doReturn(false).when(spyService).validateParticipantData(filePath);

        Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                String.class, String.class, String.class, JSONB.class);
        copyTablesMethod.setAccessible(true);

        String result = (String) copyTablesMethod.invoke(spyService, studyId, filePath, distinctDbFileIds, dbData);

        assertEquals("The Sqlite Table for Participant do not contains data for all the required fields", result);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testCopyTablesFromSqLiteToPostgres_Success() throws Exception {

        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{}");

        Record sqliteRecord = mock(Record.class);

        lenient().when(duckDsl.fetchOne("SELECT DISTINCT study_display_id FROM sqlite_study_db.participant"))
                .thenReturn(sqliteRecord);
        lenient().when(sqliteRecord.get("study_display_id", String.class)).thenReturn("SAME_STUDY_ID");

        org.jooq.SelectSelectStep<org.jooq.Record1<String>> selectStep = mock(org.jooq.SelectSelectStep.class);
        org.jooq.SelectJoinStep<org.jooq.Record1<String>> fromStep = mock(org.jooq.SelectJoinStep.class);
        org.jooq.SelectConditionStep<org.jooq.Record1<String>> whereStep = mock(org.jooq.SelectConditionStep.class);

        lenient().when(dsl.selectDistinct(any(org.jooq.Field.class))).thenReturn(selectStep);
        lenient().when(selectStep.from(anyString())).thenReturn(fromStep);
        lenient().when(fromStep.where(any(org.jooq.Condition.class))).thenReturn(whereStep);
        lenient().when(whereStep.fetchOneInto(String.class)).thenReturn("SAME_STUDY_ID");

        DatabaseMigrationService spyService = spy(databaseMigrationService);
        doReturn(true).when(spyService).validateRequiredColumns(filePath);
        doReturn(true).when(spyService).validateParticipantData(filePath);

        Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                String.class, String.class, String.class, JSONB.class);
        copyTablesMethod.setAccessible(true);

        String result = (String) copyTablesMethod.invoke(spyService, studyId, filePath, distinctDbFileIds, dbData);

        assertEquals("Proceed with Database Migration", result);
    }

    @Test
    void testCopyTablesFromSqLiteToPostgres_DatabaseException() throws Exception {

        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{}");

        lenient().when(duckDsl.fetchOne("SELECT DISTINCT study_display_id FROM sqlite_study_db.participant"))
                .thenThrow(new RuntimeException("Database connection failed"));

        Method copyTablesMethod = DatabaseMigrationService.class.getDeclaredMethod("copyTablesFromSqLiteToPostgres",
                String.class, String.class, String.class, JSONB.class);
        copyTablesMethod.setAccessible(true);

        Exception exception = assertThrows(Exception.class, () -> {
            copyTablesMethod.invoke(databaseMigrationService, studyId, filePath, distinctDbFileIds, dbData);
        });

        assertTrue(exception.getCause().getMessage().contains("Failed to copy tables from SQLite to Postgres"));
    }

    @Test
    void testMigrateDdatabase_MethodAccessible() throws Exception {

        try {
            Method migrateDdatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("migrateDdatabase",
                    String.class, String.class, String.class, JSONB.class);
            migrateDdatabaseMethod.setAccessible(true);

            assertNotNull(migrateDdatabaseMethod);
            assertEquals("migrateDdatabase", migrateDdatabaseMethod.getName());
            assertEquals(4, migrateDdatabaseMethod.getParameterCount());

            assertDoesNotThrow(() -> {
                try {
                    Object result = migrateDdatabaseMethod.invoke(databaseMigrationService,
                            "STUDY123", "/invalid/path/test.db", "FILE123", JSONB.valueOf("{}"));
                    assertNotNull(result);
                    assertTrue(result instanceof CompletableFuture);
                } catch (Exception e) {

                }
            });
        } catch (Exception e) {
            fail("Failed to test migrateDdatabase method: " + e.getMessage());
        }
    }

    @Test
    void testMigrateDdatabase_AsyncBehavior() throws Exception {

        try {
            Method migrateDdatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("migrateDdatabase",
                    String.class, String.class, String.class, JSONB.class);
            migrateDdatabaseMethod.setAccessible(true);

            Object result = migrateDdatabaseMethod.invoke(databaseMigrationService,
                    "STUDY123", "/tmp/test.db", "FILE123", JSONB.valueOf("{}"));

            assertNotNull(result);
            assertTrue(result instanceof CompletableFuture);

            @SuppressWarnings("unchecked")
            CompletableFuture<String> future = (CompletableFuture<String>) result;

            assertDoesNotThrow(() -> {
                try {
                    future.get();
                } catch (Exception e) {
                }
            });

        } catch (Exception e) {
            fail("Failed to test migrateDdatabase async behavior: " + e.getMessage());
        }
    }

    @Test
    void testMigrateDdatabase_DuckDbException() throws Exception {

        String studyId = "STUDY123";
        String filePath = "/tmp/test.db";
        String distinctDbFileIds = "FILE123";
        JSONB dbData = JSONB.valueOf("{\"test\": \"data\"}");

        lenient().when(duckDsl.execute(anyString())).thenThrow(new RuntimeException("DuckDB connection failed"));

        Method migrateDdatabaseMethod = DatabaseMigrationService.class.getDeclaredMethod("migrateDdatabase",
                String.class, String.class, String.class, JSONB.class);
        migrateDdatabaseMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        CompletableFuture<String> result = (CompletableFuture<String>) migrateDdatabaseMethod.invoke(
                databaseMigrationService, studyId, filePath, distinctDbFileIds, dbData);

        Exception exception = assertThrows(Exception.class, () -> {
            result.get();
        });

        assertTrue(exception.getCause().getMessage().contains("DuckDB connection failed"));
    }
}
