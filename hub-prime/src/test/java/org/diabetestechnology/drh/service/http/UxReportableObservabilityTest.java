package org.diabetestechnology.drh.service.http;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.Instant;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

class UxReportableObservabilityTest {

    private UxReportableObservability uxReportableObservability;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        uxReportableObservability = new UxReportableObservability();
    }

    @Test
    void testAfterCompletion_SandboxProfile_NoSecureFlags() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uxReportableObservability, "isSandbox", true);
        when(request.getAttribute("startTime")).thenReturn(Instant.now().minusSeconds(1));

        // Act
        uxReportableObservability.afterCompletion(request, response, new Object(), null);

        // Assert
        ArgumentCaptor<String> cookieCaptor = ArgumentCaptor.forClass(String.class);
        verify(response).addHeader(eq("Set-Cookie"), cookieCaptor.capture());

        String cookieValue = cookieCaptor.getValue();
        // In sandbox mode, secure and SameSite should not be present
        assertTrue(!cookieValue.contains("Secure"));
        assertTrue(!cookieValue.contains("SameSite"));
    }

    @Test
    void testAfterCompletion_NonSandboxProfile_WithSecureFlags() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(uxReportableObservability, "isSandbox", false);
        when(request.getAttribute("startTime")).thenReturn(Instant.now().minusSeconds(1));

        // Act
        uxReportableObservability.afterCompletion(request, response, new Object(), null);

        // Assert
        ArgumentCaptor<String> cookieCaptor = ArgumentCaptor.forClass(String.class);
        verify(response).addHeader(eq("Set-Cookie"), cookieCaptor.capture());

        String cookieValue = cookieCaptor.getValue();
        // In non-sandbox mode, secure and SameSite should be present
        assertTrue(cookieValue.contains("Secure"));
        assertTrue(cookieValue.contains("SameSite=Strict"));
    }

    @Test
    void testPreHandle_SetsStartTime() throws Exception {
        // Act
        boolean result = uxReportableObservability.preHandle(request, response, new Object());

        // Assert
        assertTrue(result);
        verify(request).setAttribute(eq("startTime"), org.mockito.ArgumentMatchers.any(Instant.class));
    }
}
