package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.ObservabilityRequestFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogDetails;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogLevel;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogMap;
import org.jooq.DSLContext;
import org.jooq.Record2;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectLimitPercentStep;
import org.jooq.SelectSeekStep1;
import org.jooq.SelectSelectStep;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

public class DbActivityServiceTest {

    @Mock
    private ObservabilityRequestFilter observabilityRequestFilter;

    @Mock
    private DSLContext dsl;

    @Mock
    private MasterService masterService;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpSession session;

    @Mock
    private ServletRequestAttributes servletRequestAttributes;

    @Mock
    private SelectSelectStep<Record2<Object, Object>> selectStep;

    @Mock
    private SelectJoinStep<Record2<Object, Object>> fromStep;

    @Mock
    private SelectConditionStep<Record2<Object, Object>> whereStep;

    @Mock
    private SelectConditionStep<Record2<Object, Object>> andStep;

    @Mock
    private SelectSeekStep1<Record2<Object, Object>, Object> orderStep;

    @Mock
    private SelectLimitPercentStep<Record2<Object, Object>> limitStep;

    @InjectMocks
    private DbActivityService dbActivityService;

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Mock RequestContextHolder
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        when(servletRequestAttributes.getRequest()).thenReturn(request);

        // Mock basic request properties
        when(request.getRequestURI()).thenReturn("/test/url");
        when(request.getSession()).thenReturn(session);
        when(session.getId()).thenReturn("test-session-id");

        // Mock observability filter
        when(observabilityRequestFilter.getUniqueSession(anyString(), any(ContentCachingRequestWrapper.class)))
                .thenReturn("unique-session-id");

        // Mock master service
        when(masterService.getMetricActivityLevel(6)).thenReturn("Level 6");
    }

    @Test
    void testPrepareActivityLogMetadata_WithHierarchyData() throws Exception {
        // Given
        String requestUrl = "/test/url";
        String sessionId = "test-session-id";

        // Mock database query chain
        when(dsl.select(DSL.field("hierarchy_path"), DSL.field("activity_hierarchy")))
                .thenReturn(selectStep);
        when(selectStep.from("drh_stateful_activity_audit.activity_log"))
                .thenReturn(fromStep);
        when(fromStep.where(DSL.field("session_id").eq(sessionId)))
                .thenReturn(whereStep);
        when(whereStep.and(DSL.field("activity_level_id").ne("Level 6")))
                .thenReturn(andStep);
        when(andStep.orderBy(DSL.field("created_at").desc()))
                .thenReturn(orderStep);
        when(orderStep.limit(1))
                .thenReturn(limitStep);

        // Mock database result with hierarchy data
        Map<String, Object> hierarchyData = new HashMap<>();
        hierarchyData.put("hierarchy_path", "existing/path");
        hierarchyData.put("activity_hierarchy", "existing_hierarchy");
        when(limitStep.fetchMaps()).thenReturn(List.of(hierarchyData));

        // Mock LogMap with LogDetails
        LogDetails logDetails = new LogDetails("Test Activity", "TEST", "Test Description",
                requestUrl, LogLevel.DEFAULT);
        LogMap logMap = mock(LogMap.class);
        when(logMap.getLogMap()).thenReturn(Map.of(requestUrl, logDetails));
        dbActivityService.logMap = logMap;

        // When
        String result = dbActivityService.prepareActivityLogMetadata();

        // Then
        assertNotNull(result);

        JsonNode jsonNode = objectMapper.readTree(result);
        assertEquals("unique-session-id", jsonNode.get("session_unique_id").asText());
        assertEquals(requestUrl, jsonNode.get("request_url").asText());
        assertEquals(sessionId, jsonNode.get("session_id").asText());
        assertEquals("existing/path, /test/url", jsonNode.get("hierarchy_path").asText());
        assertNotNull(jsonNode.get("activity_hierarchy"));
    }

    @Test
    void testPrepareActivityLogMetadata_WithoutHierarchyData() throws Exception {
        // Given
        String requestUrl = "/test/url";
        String sessionId = "test-session-id";

        // Mock database query chain
        when(dsl.select(DSL.field("hierarchy_path"), DSL.field("activity_hierarchy")))
                .thenReturn(selectStep);
        when(selectStep.from("drh_stateful_activity_audit.activity_log"))
                .thenReturn(fromStep);
        when(fromStep.where(DSL.field("session_id").eq(sessionId)))
                .thenReturn(whereStep);
        when(whereStep.and(DSL.field("activity_level_id").ne("Level 6")))
                .thenReturn(andStep);
        when(andStep.orderBy(DSL.field("created_at").desc()))
                .thenReturn(orderStep);
        when(orderStep.limit(1))
                .thenReturn(limitStep);

        // Mock empty database result
        when(limitStep.fetchMaps()).thenReturn(Collections.emptyList());

        // Mock LogMap with LogDetails
        LogDetails logDetails = new LogDetails("Test Activity", "TEST", "Test Description",
                requestUrl, LogLevel.DEFAULT);
        LogMap logMap = mock(LogMap.class);
        when(logMap.getLogMap()).thenReturn(Map.of(requestUrl, logDetails));
        dbActivityService.logMap = logMap;

        // When
        String result = dbActivityService.prepareActivityLogMetadata();

        // Then
        assertNotNull(result);

        JsonNode jsonNode = objectMapper.readTree(result);
        assertEquals("unique-session-id", jsonNode.get("session_unique_id").asText());
        assertEquals(requestUrl, jsonNode.get("request_url").asText());
        assertEquals(sessionId, jsonNode.get("session_id").asText());
        assertEquals(requestUrl, jsonNode.get("hierarchy_path").asText());
        assertNotNull(jsonNode.get("activity_hierarchy"));
    }

    @Test
    void testPrepareActivityLogMetadata_WithoutLogDetails() throws Exception {
        // Given
        String requestUrl = "/unknown/url";
        String sessionId = "test-session-id";

        // Override the mock to return the unknown URL
        when(request.getRequestURI()).thenReturn(requestUrl);

        // Mock database query chain
        when(dsl.select(DSL.field("hierarchy_path"), DSL.field("activity_hierarchy")))
                .thenReturn(selectStep);
        when(selectStep.from("drh_stateful_activity_audit.activity_log"))
                .thenReturn(fromStep);
        when(fromStep.where(DSL.field("session_id").eq(sessionId)))
                .thenReturn(whereStep);
        when(whereStep.and(DSL.field("activity_level_id").ne("Level 6")))
                .thenReturn(andStep);
        when(andStep.orderBy(DSL.field("created_at").desc()))
                .thenReturn(orderStep);
        when(orderStep.limit(1))
                .thenReturn(limitStep);

        // Mock empty database result
        when(limitStep.fetchMaps()).thenReturn(Collections.emptyList());

        // Mock LogMap without LogDetails for this URL
        LogMap logMap = mock(LogMap.class);
        when(logMap.getLogMap()).thenReturn(Collections.emptyMap());
        dbActivityService.logMap = logMap;

        // When
        String result = dbActivityService.prepareActivityLogMetadata();

        // Then
        assertNotNull(result);

        JsonNode jsonNode = objectMapper.readTree(result);
        assertEquals("unique-session-id", jsonNode.get("session_unique_id").asText());
        assertEquals(requestUrl, jsonNode.get("request_url").asText());
        assertEquals(sessionId, jsonNode.get("session_id").asText());
        assertEquals("null", jsonNode.get("hierarchy_path").asText());
        assertEquals("null", jsonNode.get("activity_hierarchy").asText());
    }

    @Test
    void testPrepareActivityLogMetadata_WithNullHierarchyPath() throws Exception {
        // Given
        String requestUrl = "/test/url";
        String sessionId = "test-session-id";

        // Mock database query chain
        when(dsl.select(DSL.field("hierarchy_path"), DSL.field("activity_hierarchy")))
                .thenReturn(selectStep);
        when(selectStep.from("drh_stateful_activity_audit.activity_log"))
                .thenReturn(fromStep);
        when(fromStep.where(DSL.field("session_id").eq(sessionId)))
                .thenReturn(whereStep);
        when(whereStep.and(DSL.field("activity_level_id").ne("Level 6")))
                .thenReturn(andStep);
        when(andStep.orderBy(DSL.field("created_at").desc()))
                .thenReturn(orderStep);
        when(orderStep.limit(1))
                .thenReturn(limitStep);

        // Mock database result with null hierarchy_path
        Map<String, Object> hierarchyData = new HashMap<>();
        hierarchyData.put("hierarchy_path", null);
        hierarchyData.put("activity_hierarchy", "existing_hierarchy");
        when(limitStep.fetchMaps()).thenReturn(List.of(hierarchyData));

        // Mock LogMap with LogDetails
        LogDetails logDetails = new LogDetails("Test Activity", "TEST", "Test Description",
                requestUrl, LogLevel.DEFAULT);
        LogMap logMap = mock(LogMap.class);
        when(logMap.getLogMap()).thenReturn(Map.of(requestUrl, logDetails));
        dbActivityService.logMap = logMap;

        // When
        String result = dbActivityService.prepareActivityLogMetadata();

        // Then
        assertNotNull(result);

        JsonNode jsonNode = objectMapper.readTree(result);
        assertEquals(requestUrl, jsonNode.get("hierarchy_path").asText());
    }

    @Test
    void testPrepareActivityLogMetadata_JsonProcessingException() {
        // Given - This test would require mocking ObjectMapper to throw
        // JsonProcessingException
        // For simplicity, we'll test the normal flow and verify the JSON structure

        // Mock database query chain to return empty result
        when(dsl.select(DSL.field("hierarchy_path"), DSL.field("activity_hierarchy")))
                .thenReturn(selectStep);
        when(selectStep.from("drh_stateful_activity_audit.activity_log"))
                .thenReturn(fromStep);
        when(fromStep.where(DSL.field("session_id").eq("test-session-id")))
                .thenReturn(whereStep);
        when(whereStep.and(DSL.field("activity_level_id").ne("Level 6")))
                .thenReturn(andStep);
        when(andStep.orderBy(DSL.field("created_at").desc()))
                .thenReturn(orderStep);
        when(orderStep.limit(1))
                .thenReturn(limitStep);
        when(limitStep.fetchMaps()).thenReturn(Collections.emptyList());

        // Mock LogMap
        LogMap logMap = mock(LogMap.class);
        when(logMap.getLogMap()).thenReturn(Collections.emptyMap());
        dbActivityService.logMap = logMap;

        // When
        String result = dbActivityService.prepareActivityLogMetadata();

        // Then
        assertNotNull(result);
    }
}
