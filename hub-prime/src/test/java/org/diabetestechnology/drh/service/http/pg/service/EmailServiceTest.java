package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.UnsupportedEncodingException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.util.ReflectionTestUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

@ExtendWith(MockitoExtension.class)
public class EmailServiceTest {

    @Mock
    private JavaMailSender mailSender;

    @Mock
    private TemplateEngine templateEngine;

    @Mock
    private MimeMessage mimeMessage;

    @InjectMocks
    private EmailService emailService;

    private static final String TEST_EMAIL_FROM = "<EMAIL>";
    private static final String TEST_OTP_VERIFY_URL = "https://test.diabetesresearchhub.com/verify";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(emailService, "emailFrom", TEST_EMAIL_FROM);
        ReflectionTestUtils.setField(emailService, "otpVerifyUrl", TEST_OTP_VERIFY_URL);
    }

    @Test
    void testSendEmail_Success() throws MessagingException, UnsupportedEncodingException {
        // Arrange
        String toEmail = "<EMAIL>";
        String name = "John Doe";
        String otp = "123456";
        String expectedHtmlContent = "<html>Test email content</html>";

        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        when(templateEngine.process(eq("email-template"), any(Context.class))).thenReturn(expectedHtmlContent);

        // Act
        assertDoesNotThrow(() -> emailService.sendEmail(toEmail, name, otp));

        // Assert
        verify(mailSender, times(1)).createMimeMessage();
        verify(templateEngine, times(1)).process(eq("email-template"), any(Context.class));
        verify(mailSender, times(1)).send(mimeMessage);

        // Verify template context variables
        ArgumentCaptor<Context> contextCaptor = ArgumentCaptor.forClass(Context.class);
        verify(templateEngine).process(eq("email-template"), contextCaptor.capture());

        Context capturedContext = contextCaptor.getValue();
        assertNotNull(capturedContext);
        assertEquals(name, capturedContext.getVariable("name"));
        assertEquals(otp, capturedContext.getVariable("otp"));
        assertEquals(TEST_OTP_VERIFY_URL, capturedContext.getVariable("verifyOtp"));
    }

    @Test
    void testSendEmail_MailException() {
        // Arrange
        String toEmail = "<EMAIL>";
        String name = "Test User";
        String otp = "123456";

        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        when(templateEngine.process(anyString(), any(Context.class))).thenReturn("<html>content</html>");
        doThrow(new MailException("SMTP server error") {
        }).when(mailSender).send(mimeMessage);

        // Act & Assert
        MailException exception = assertThrows(MailException.class,
                () -> emailService.sendEmail(toEmail, name, otp));

        assertEquals("SMTP server error", exception.getMessage());
        verify(mailSender, times(1)).createMimeMessage();
        verify(templateEngine, times(1)).process(eq("email-template"), any(Context.class));
        verify(mailSender, times(1)).send(mimeMessage);
    }

    @Test
    void testSendEmail_TemplateEngineException() {
        // Arrange
        String toEmail = "<EMAIL>";
        String name = "Test User";
        String otp = "123456";

        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        when(templateEngine.process(eq("email-template"), any(Context.class)))
                .thenThrow(new RuntimeException("Template processing error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> emailService.sendEmail(toEmail, name, otp));

        assertEquals("Template processing error", exception.getMessage());
        verify(mailSender, times(1)).createMimeMessage();
        verify(templateEngine, times(1)).process(eq("email-template"), any(Context.class));
        verify(mailSender, never()).send(any(MimeMessage.class));
    }

    @Test
    void testSendEmail_EdgeCases() throws MessagingException, UnsupportedEncodingException {
        // Arrange - Test with null and empty values
        String toEmail = "<EMAIL>";
        String name = null;
        String otp = "";

        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        when(templateEngine.process(anyString(), any(Context.class))).thenReturn("<html>content</html>");

        // Act & Assert
        assertDoesNotThrow(() -> emailService.sendEmail(toEmail, name, otp));

        // Verify template variables are passed correctly even with null/empty values
        ArgumentCaptor<Context> contextCaptor = ArgumentCaptor.forClass(Context.class);
        verify(templateEngine).process(eq("email-template"), contextCaptor.capture());
        assertEquals(name, contextCaptor.getValue().getVariable("name"));
        assertEquals(otp, contextCaptor.getValue().getVariable("otp"));
        assertEquals(TEST_OTP_VERIFY_URL, contextCaptor.getValue().getVariable("verifyOtp"));
    }

}
