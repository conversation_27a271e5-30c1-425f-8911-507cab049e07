package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.List;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.constant.ActionType;
import org.diabetestechnology.drh.service.http.pg.constant.FileProcessingStatus;
import org.diabetestechnology.drh.service.http.pg.constant.FileType;
import org.diabetestechnology.drh.service.http.util.HttpRequestResponseUtil;
import org.jooq.Condition;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.SQLDialect;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.fasterxml.jackson.core.JsonProcessingException;

import jakarta.servlet.http.HttpServletRequest;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("unchecked")
public class InteractionServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;

    @Mock
    private MasterService masterService;

    @Mock
    private HttpServletRequest httpServletRequest;

    @InjectMocks
    private InteractionService interactionService;

    private final String testStudyId = "study123";
    private final String testUserId = "user123";
    private final String testUserPartyId = "userParty123";
    private final String testOrgPartyId = "orgParty123";
    private final String testHubInteractionId = "hubInt123";
    private final String testParticipantId = "participant123";
    private final String testUri = "/api/test";

    @BeforeEach
    void setUp() {

        Configuration mockConfig = mock(Configuration.class);
        lenient().when(dsl.configuration()).thenReturn(mockConfig);
        lenient().when(mockConfig.dialect()).thenReturn(SQLDialect.POSTGRES);

        lenient().when(userNameService.getUserId()).thenReturn(testUserId);
        lenient().when(partyService.getPartyIdByUserId(testUserId)).thenReturn(testUserPartyId);
        lenient().when(partyService.getOrganizationPartyIdByUser(testUserId)).thenReturn(testOrgPartyId);
    }

    @Test
    void testSaveHubInteraction_Success() throws JsonProcessingException {
        String expectedHubInteractionId = "hub123";
        String jsonResponse = "{\"hub_interaction_id\":\"" + expectedHubInteractionId + "\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        String result = interactionService.saveHubInteraction(testStudyId);

        assertNotNull(result);
        assertEquals(expectedHubInteractionId, result);
        verify(dsl).select(any(Field.class));
        verify(selectMock).fetchOneInto(JSONB.class);
    }

    @Test
    void testSaveHubInteraction_NoHubInteractionId() throws JsonProcessingException {
        String jsonResponse = "{\"status\":\"success\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        String result = interactionService.saveHubInteraction(testStudyId);

        assertNull(result);
    }

    @Test
    void testSaveHubInteraction_JsonProcessingException() throws JsonProcessingException {

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.saveHubInteraction(testStudyId);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudy_Success() {

        String studyId = "study123";

        lenient().when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudy(studyId);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudy_NotFound() {

        String studyId = "study123";

        lenient().when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudy(studyId);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudy_DatabaseException() {

        String studyId = "study123";

        when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database connection failed"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudy(studyId);
        });
    }

    @Test
    void testSaveStudyInteraction_Success() {

        try (MockedStatic<HttpRequestResponseUtil> mockedUtil = mockStatic(HttpRequestResponseUtil.class)) {
            mockedUtil.when(HttpRequestResponseUtil::getCurrentRequest).thenReturn(httpServletRequest);
            when(httpServletRequest.getRequestURI()).thenReturn(testUri);
            when(masterService.getActiontype(anyString())).thenReturn(1);
            when(masterService.getInteractionStatus(anyString())).thenReturn(1);

            InteractionService spyService = spy(interactionService);
            doReturn(new ArrayList<>()).when(spyService).getAndSetInteractionHierarchyofStudyInteraction(testStudyId);

            JSONB mockResult = JSONB.valueOf("{\"status\":\"success\"}");
            SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
            when(dsl.select(any(Field.class))).thenReturn(selectMock);
            when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

            assertDoesNotThrow(() -> {
                spyService.saveStudyInteraction(testStudyId, testHubInteractionId, "CREATE", "Test description",
                        "DRAFT", "ACTIVE", "{\"test\":\"request\"}", "{\"test\":\"response\"}", null, 200, "SUCCESS",
                        ActionType.DB_FILE_UPLOAD, "COMPLETED");
            });

            verify(userNameService).getUserId();
            verify(partyService).getPartyIdByUserId(testUserId);
            verify(partyService).getOrganizationPartyIdByUser(testUserId);
        }
    }

    @Test
    void testSaveStudyInteraction_WithException() {

        try (MockedStatic<HttpRequestResponseUtil> mockedUtil = mockStatic(HttpRequestResponseUtil.class)) {
            mockedUtil.when(HttpRequestResponseUtil::getCurrentRequest)
                    .thenThrow(new RuntimeException("Request error"));

            assertDoesNotThrow(() -> {
                interactionService.saveStudyInteraction(testStudyId, testHubInteractionId, "CREATE", "Test description",
                        "DRAFT", "ACTIVE", "{\"test\":\"request\"}", "{\"test\":\"response\"}", null, 200, "SUCCESS",
                        ActionType.DB_FILE_UPLOAD, "COMPLETED");
            });
        }
    }

    @Test
    void testIsDbFileInteractionExistsForStudy_True() {

        when(dsl.fetchExists(any(SelectSelectStep.class))).thenReturn(true);

        Boolean result = interactionService.isDbFileInteractionExistsForStudy(testStudyId);

        assertTrue(result);
    }

    @Test
    void testIsDbFileInteractionExistsForStudy_False() {

        when(dsl.fetchExists(any(SelectSelectStep.class))).thenReturn(false);

        Boolean result = interactionService.isDbFileInteractionExistsForStudy(testStudyId);

        assertFalse(result);
    }

    @Test
    void testIsDbFileInteractionFinishedForAction_Success() {

        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);
        when(masterService.getActiontype(ActionType.DB_FILE_UPLOAD)).thenReturn(1);
        when(dsl.fetchExists(any(SelectSelectStep.class))).thenReturn(true);

        Boolean result = interactionService.isDbFileInteractionFinishedForAction(testStudyId,
                ActionType.DB_FILE_UPLOAD);

        assertTrue(result);
        verify(masterService).getInteractionStatus(FileProcessingStatus.SUCCESS);
        verify(masterService).getActiontype(ActionType.DB_FILE_UPLOAD);
    }

    @Test
    void testIsCompletedCgmRowData_True() {

        when(masterService.getActiontype(ActionType.SAVE_DB_CONTENT)).thenReturn(1);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);
        when(dsl.fetchExists(any(SelectSelectStep.class))).thenReturn(true);

        boolean result = interactionService.isCompletedCgmRowData(testStudyId);

        assertTrue(result);
    }

    @Test
    void testGetSuccessDbFileInteractionIdOfActionType_Success() {

        String actionType = ActionType.DB_FILE_UPLOAD;
        String studyId = "study123";
        String expectedFileInteractionId = "fileInt456";

        when(masterService.getActiontype(actionType)).thenReturn(1);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock2 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock3 = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock1);
        when(andMock1.and(any(Condition.class))).thenReturn(andMock2);
        when(andMock2.and(any(Condition.class))).thenReturn(andMock3);
        when(andMock3.fetchOneInto(String.class)).thenReturn(expectedFileInteractionId);

        String result = interactionService.getSuccessDbFileInteractionIdOfActionType(actionType, studyId);

        assertNotNull(result);
        assertEquals(expectedFileInteractionId, result);
        verify(masterService).getActiontype(actionType);
        verify(masterService).getInteractionStatus(FileProcessingStatus.SUCCESS);
    }

    @Test
    void testGetSuccessDbFileInteractionIdOfActionType_NotFound() {

        String actionType = ActionType.DB_FILE_UPLOAD;
        String studyId = "study123";

        when(masterService.getActiontype(actionType)).thenReturn(1);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock2 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock3 = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock1);
        when(andMock1.and(any(Condition.class))).thenReturn(andMock2);
        when(andMock2.and(any(Condition.class))).thenReturn(andMock3);
        when(andMock3.fetchOneInto(String.class)).thenReturn(null);

        String result = interactionService.getSuccessDbFileInteractionIdOfActionType(actionType, studyId);

        assertNull(result);
        verify(masterService).getActiontype(actionType);
        verify(masterService).getInteractionStatus(FileProcessingStatus.SUCCESS);
    }

    @Test
    void testGetSuccessDbFileInteractionIdOfActionType_DatabaseException() {

        String actionType = ActionType.DB_FILE_UPLOAD;
        String studyId = "study123";

        lenient().when(masterService.getActiontype(actionType)).thenReturn(1);
        lenient().when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database connection failed"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getSuccessDbFileInteractionIdOfActionType(actionType, studyId);
        });
    }

    @Test
    void testGetSuccessDbFileInteractionIdOfActionType_MasterServiceException() {

        String actionType = ActionType.DB_FILE_UPLOAD;
        String studyId = "study123";

        lenient().when(masterService.getActiontype(actionType))
                .thenThrow(new RuntimeException("Master service error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getSuccessDbFileInteractionIdOfActionType(actionType, studyId);
        });
    }

    @Test
    void testGetSuccessDbFileInteractionIdOfActionType_NullStudyId() {

        String actionType = ActionType.DB_FILE_UPLOAD;

        when(masterService.getActiontype(actionType)).thenReturn(1);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock2 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock3 = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock1);
        when(andMock1.and(any(Condition.class))).thenReturn(andMock2);
        when(andMock2.and(any(Condition.class))).thenReturn(andMock3);
        when(andMock3.fetchOneInto(String.class)).thenReturn(null);

        String result = interactionService.getSuccessDbFileInteractionIdOfActionType(actionType, null);

        assertNull(result);
        verify(masterService).getActiontype(actionType);
        verify(masterService).getInteractionStatus(FileProcessingStatus.SUCCESS);
    }

    @Test
    void testGetSuccessDbFileInteractionIdOfActionType_EmptyStudyId() {

        String actionType = ActionType.DB_FILE_UPLOAD;
        String studyId = "";

        when(masterService.getActiontype(actionType)).thenReturn(1);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock2 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock3 = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock1);
        when(andMock1.and(any(Condition.class))).thenReturn(andMock2);
        when(andMock2.and(any(Condition.class))).thenReturn(andMock3);
        when(andMock3.fetchOneInto(String.class)).thenReturn(null);

        String result = interactionService.getSuccessDbFileInteractionIdOfActionType(actionType, studyId);

        assertNull(result);
        verify(masterService).getActiontype(actionType);
        verify(masterService).getInteractionStatus(FileProcessingStatus.SUCCESS);
    }

    @Test
    void testGetSuccessDbFileInteractionIdOfActionType_DifferentActionTypes() {

        String[] actionTypes = {
                ActionType.DB_FILE_UPLOAD,
                ActionType.CGM_MIGRATION,
                ActionType.MEAL_MIGRATION,
                ActionType.FITNESS_MIGRATION,
                ActionType.SAVE_DB_CONTENT
        };

        for (int i = 0; i < actionTypes.length; i++) {
            String actionType = actionTypes[i];
            String expectedFileInteractionId = "fileInt" + (i + 1);

            when(masterService.getActiontype(actionType)).thenReturn(i + 1);
            when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

            SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
            SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
            SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
            SelectConditionStep<Record1<String>> andMock1 = mock(SelectConditionStep.class);
            SelectConditionStep<Record1<String>> andMock2 = mock(SelectConditionStep.class);
            SelectConditionStep<Record1<String>> andMock3 = mock(SelectConditionStep.class);

            when(dsl.select(any(Field.class))).thenReturn(selectMock);
            when(selectMock.from(anyString())).thenReturn(fromMock);
            when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
            when(whereMock.and(any(Condition.class))).thenReturn(andMock1);
            when(andMock1.and(any(Condition.class))).thenReturn(andMock2);
            when(andMock2.and(any(Condition.class))).thenReturn(andMock3);
            when(andMock3.fetchOneInto(String.class)).thenReturn(expectedFileInteractionId);

            String result = interactionService.getSuccessDbFileInteractionIdOfActionType(actionType, testStudyId);

            assertNotNull(result, "Should return file interaction ID for action type: " + actionType);
            assertEquals(expectedFileInteractionId, result);
            verify(masterService).getActiontype(actionType);
            verify(masterService).getInteractionStatus(FileProcessingStatus.SUCCESS);

            reset(dsl, masterService);

            Configuration mockConfig = mock(Configuration.class);
            lenient().when(dsl.configuration()).thenReturn(mockConfig);
            lenient().when(mockConfig.dialect()).thenReturn(SQLDialect.POSTGRES);
        }
    }

    @Test
    void testGetInteractionHierarchy_Success() throws Exception {

        String fileInteractionId = "fileInt123";
        String jsonResponse = "{\"file_interaction_id\":\"" + fileInteractionId + "\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);
        List<String> existingHierarchy = new ArrayList<>();

        List<String> result = interactionService.getInteractionHierarchy(mockResult, existingHierarchy);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(fileInteractionId, result.get(0));
    }

    @Test
    void testGetInteractionHierarchy_WithNullHierarchy() throws Exception {

        String fileInteractionId = "fileInt123";
        String jsonResponse = "{\"file_interaction_id\":\"" + fileInteractionId + "\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        List<String> result = interactionService.getInteractionHierarchy(mockResult, null);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(fileInteractionId, result.get(0));
    }

    @Test
    void testGetInteractionHierarchy_Exception() {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenThrow(new RuntimeException("JSON parsing error"));

        List<String> result = interactionService.getInteractionHierarchy(mockResult, new ArrayList<>());

        assertNull(result);
    }

    @Test
    void testGetLastInteractionId_Success() throws Exception {

        String fileInteractionId = "fileInt123";
        String jsonResponse = "{\"file_interaction_id\":\"" + fileInteractionId + "\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        String result = interactionService.getLastInteractionId(mockResult);

        assertEquals(fileInteractionId, result);
    }

    @Test
    void testGetLastInteractionId_Exception() {

        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenThrow(new RuntimeException("JSON parsing error"));

        String result = interactionService.getLastInteractionId(mockResult);

        assertNull(result);
    }

    @Test
    void testSaveStudyParticipantInteraction_Success() {

        try (MockedStatic<HttpRequestResponseUtil> mockedUtil = mockStatic(HttpRequestResponseUtil.class)) {
            mockedUtil.when(HttpRequestResponseUtil::getCurrentRequest).thenReturn(httpServletRequest);
            when(httpServletRequest.getRequestURI()).thenReturn(testUri);
            when(masterService.getActiontype(anyString())).thenReturn(1);
            when(masterService.getInteractionStatus(anyString())).thenReturn(1);

            InteractionService spyService = spy(interactionService);
            doReturn(new ArrayList<>()).when(spyService)
                    .getAndSetInteractionHierarchyofStudyParticipantInteraction(testStudyId, testParticipantId);

            JSONB mockResult = JSONB.valueOf("{\"status\":\"success\"}");
            SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
            when(dsl.select(any(Field.class))).thenReturn(selectMock);
            when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

            assertDoesNotThrow(() -> {
                spyService.saveStudyParticipantInteraction(testStudyId, testParticipantId, testHubInteractionId,
                        "CREATE",
                        "Test description", "DRAFT", "ACTIVE", "{\"test\":\"request\"}", "{\"test\":\"response\"}",
                        null, 200, "SUCCESS", ActionType.DB_FILE_UPLOAD, "COMPLETED");
            });

            verify(userNameService).getUserId();
            verify(partyService).getPartyIdByUserId(testUserId);
            verify(partyService).getOrganizationPartyIdByUser(testUserId);
        }
    }

    @Test
    void testGetHubIntercationIdOfStudyParticipant_Success() {

        String participantId = "participant123";

        lenient().when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudyParticipant(participantId);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudyParticipant_NotFound() {

        String participantId = "participant123";

        lenient().when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudyParticipant(participantId);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudyParticipant_DatabaseException() {

        String participantId = "participant123";

        when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database connection failed"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudyParticipant(participantId);
        });
    }

    @Test
    void testIsMealsOrFitnessInteractionExist_True() {

        when(masterService.getActiontype(ActionType.MEAL_MIGRATION)).thenReturn(1);
        when(masterService.getActiontype(ActionType.FITNESS_MIGRATION)).thenReturn(2);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);
        when(dsl.fetchExists(any(SelectSelectStep.class))).thenReturn(true);

        Boolean result = interactionService.isMealsOrFitnessInteractionExist(testStudyId);

        assertTrue(result);
    }

    @Test
    void testIsMealsOrFitnessInteractionExist_False() {

        when(masterService.getActiontype(ActionType.MEAL_MIGRATION)).thenReturn(1);
        when(masterService.getActiontype(ActionType.FITNESS_MIGRATION)).thenReturn(2);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);
        when(dsl.fetchExists(any(SelectSelectStep.class))).thenReturn(false);

        Boolean result = interactionService.isMealsOrFitnessInteractionExist(testStudyId);

        assertFalse(result);
    }

    @Test
    void testSaveFileInteraction_Success() {

        try (MockedStatic<HttpRequestResponseUtil> mockedUtil = mockStatic(HttpRequestResponseUtil.class)) {
            mockedUtil.when(HttpRequestResponseUtil::getCurrentRequest).thenReturn(httpServletRequest);
            when(httpServletRequest.getRequestURI()).thenReturn(testUri);
            when(masterService.getActiontype(anyString())).thenReturn(1);
            when(masterService.getInteractionStatus(anyString())).thenReturn(1);

            JSONB mockResult = JSONB.valueOf("{\"status\":\"success\"}");
            SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
            when(dsl.select(any(Field.class))).thenReturn(selectMock);
            when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

            List<String> interactionHierarchy = List.of("int1", "int2");

            JSONB result = interactionService.saveFileInteraction(testHubInteractionId, testStudyId, testParticipantId,
                    "Test description", "{\"test\":\"request\"}", "{\"test\":\"response\"}", "dbFile123",
                    "/path/to/file", "test.csv", "text/csv", "{\"content\":\"test\"}", FileType.DATABASE,
                    "UPLOADED", "Test file", "2023-01-01T10:00:00", "2023-01-01T10:30:00",
                    FileProcessingStatus.SUCCESS, 200, null, interactionHierarchy, ActionType.DB_FILE_UPLOAD);

            assertNotNull(result);
            assertEquals(mockResult, result);
        }
    }

    @Test
    void testSaveFileInteraction_Exception() {

        try (MockedStatic<HttpRequestResponseUtil> mockedUtil = mockStatic(HttpRequestResponseUtil.class)) {
            mockedUtil.when(HttpRequestResponseUtil::getCurrentRequest)
                    .thenThrow(new RuntimeException("Request error"));

            JSONB result = interactionService.saveFileInteraction(testHubInteractionId, testStudyId, testParticipantId,
                    "Test description", "{\"test\":\"request\"}", "{\"test\":\"response\"}", "dbFile123",
                    "/path/to/file", "test.csv", "text/csv", "{\"content\":\"test\"}", FileType.DATABASE,
                    "UPLOADED", "Test file", "2023-01-01T10:00:00", "2023-01-01T10:30:00",
                    FileProcessingStatus.SUCCESS, 200, null, List.of("int1"), ActionType.DB_FILE_UPLOAD);

            assertNull(result);
        }
    }

    @Test
    void testGetLastInteractionIdAndHierarchy_Success() {

        String firstInteractionId = "firstInt123";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        JSONB result = interactionService.getLastInteractionIdAndHierarchy(firstInteractionId);

        assertNull(result);
    }

    @Test
    void testGetLastInteractionIdAndHierarchy_DatabaseException() {

        String firstInteractionId = "firstInt123";
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database connection failed"));

        JSONB result = interactionService.getLastInteractionIdAndHierarchy(firstInteractionId);

        assertNull(result);
    }

    @Test
    void testGetLastInteractionIdAndHierarchy_NullFirstInteractionId() {

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        JSONB result = interactionService.getLastInteractionIdAndHierarchy(null);

        assertNull(result);
    }

    @Test
    void testGetLastInteractionIdAndHierarchy_EmptyFirstInteractionId() {

        String firstInteractionId = "";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        JSONB result = interactionService.getLastInteractionIdAndHierarchy(firstInteractionId);

        assertNull(result);
    }

    @Test
    void testGetLastInteractionIdAndHierarchy_MultipleInteractionIds() {

        String[] interactionIds = { "interaction1", "interaction2", "interaction3", "interaction4", "interaction5" };

        for (String interactionId : interactionIds) {

            lenient().when(dsl.select(any(Field.class)))
                    .thenThrow(new RuntimeException("Database error for " + interactionId));

            JSONB result = interactionService.getLastInteractionIdAndHierarchy(interactionId);

            assertNull(result, "Should return null on exception for interaction: " + interactionId);

            reset(dsl);

            Configuration mockConfig = mock(Configuration.class);
            lenient().when(dsl.configuration()).thenReturn(mockConfig);
            lenient().when(mockConfig.dialect()).thenReturn(SQLDialect.POSTGRES);
        }
    }

    @Test
    void testGetLastInteractionIdAndHierarchy_LongInteractionId() {

        String firstInteractionId = "interaction_with_very_long_id_that_might_cause_issues_in_database_queries_123456789";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        JSONB result = interactionService.getLastInteractionIdAndHierarchy(firstInteractionId);

        assertNull(result);
    }

    @Test
    void testGetLastInteractionIdAndHierarchy_SpecialCharacters() {

        String firstInteractionId = "interaction@123#$%";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        JSONB result = interactionService.getLastInteractionIdAndHierarchy(firstInteractionId);

        assertNull(result);
    }

    @Test
    void testGetLastInteractionIdAndHierarchy_WhitespaceInteractionId() {

        String firstInteractionId = "   ";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        JSONB result = interactionService.getLastInteractionIdAndHierarchy(firstInteractionId);

        assertNull(result);
    }

    @Test
    void testGetLastInteractionHierarchy_Success() throws Exception {

        String firstInteractionId = "firstInt123";

        InteractionService spyService = spy(interactionService);
        String jsonResponse = "{\"interaction_hierarchy\":\"[\\\"int1\\\",\\\"int2\\\"]\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);
        doReturn(mockResult).when(spyService).getLastInteractionIdAndHierarchy(firstInteractionId);

        List<String> result = spyService.getLastInteractionHierarchy(firstInteractionId);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("int1", result.get(0));
        assertEquals("int2", result.get(1));
    }

    @Test
    void testGetLastInteractionHierarchy_InvalidJson() throws Exception {

        String firstInteractionId = "firstInt123";

        InteractionService spyService = spy(interactionService);
        String invalidJsonResponse = "{\"interaction_hierarchy\":\"not_an_array\"}";
        JSONB mockResult = JSONB.valueOf(invalidJsonResponse);
        doReturn(mockResult).when(spyService).getLastInteractionIdAndHierarchy(firstInteractionId);

        assertThrows(Exception.class, () -> {
            spyService.getLastInteractionHierarchy(firstInteractionId);
        });
    }

    @Test
    void testGetLastInteractionHierarchy_NullResponse() throws Exception {

        String firstInteractionId = "firstInt123";

        InteractionService spyService = spy(interactionService);
        doReturn(null).when(spyService).getLastInteractionIdAndHierarchy(firstInteractionId);

        assertThrows(NullPointerException.class, () -> {
            spyService.getLastInteractionHierarchy(firstInteractionId);
        });
    }

    @Test
    void testGetLastInteractionLog_Success() {

        String lastInteractionId = "lastInt123";

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        JSONB result = interactionService.getLastInteractionLog(lastInteractionId);

        assertNull(result);
    }

    @Test
    void testGetLastInteractionLog_Exception() {

        String lastInteractionId = "lastInt123";
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        JSONB result = interactionService.getLastInteractionLog(lastInteractionId);

        assertNull(result);
    }

    @Test
    void testGetAndSetInteractionHierarchyFromInteractionLog_Success() throws Exception {

        String lastFileInteractionId = "lastInt123";
        String jsonResponse = "{\"interaction_hierarchy\":[\"int1\",\"int2\",\"int3\"]}";
        JSONB interactionHierarchy = JSONB.valueOf(jsonResponse);

        List<String> result = interactionService.getAndSetInteractionHierarchyFromInteractionLog(
                interactionHierarchy, lastFileInteractionId);

        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals("int1", result.get(0));
        assertEquals("int2", result.get(1));
        assertEquals("int3", result.get(2));
        assertEquals(lastFileInteractionId, result.get(3));
    }

    @Test
    void testGetAndSetInteractionHierarchyFromInteractionLog_InvalidJson() {

        String lastFileInteractionId = "lastInt123";
        JSONB mockResult = mock(JSONB.class);
        when(mockResult.data()).thenThrow(new RuntimeException("JSON parsing error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyFromInteractionLog(
                mockResult, lastFileInteractionId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyInteraction_Success() {

        String studyId = "study123";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyInteraction(studyId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyInteraction_DatabaseException() {

        String studyId = "study123";
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database connection failed"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyInteraction(studyId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyInteraction_NullStudyId() {

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyInteraction(null);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyInteraction_EmptyStudyId() {

        String studyId = "";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyInteraction(studyId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyInteraction_MultipleStudies() {

        String[] studyIds = { "study1", "study2", "study3", "study4", "study5" };

        for (String studyId : studyIds) {

            lenient().when(dsl.select(any(Field.class)))
                    .thenThrow(new RuntimeException("Database error for " + studyId));

            List<String> result = interactionService.getAndSetInteractionHierarchyofStudyInteraction(studyId);

            assertNotNull(result, "Should return non-null list for study: " + studyId);
            assertTrue(result.isEmpty(), "Should return empty list on exception for study: " + studyId);

            reset(dsl);

            Configuration mockConfig = mock(Configuration.class);
            lenient().when(dsl.configuration()).thenReturn(mockConfig);
            lenient().when(mockConfig.dialect()).thenReturn(SQLDialect.POSTGRES);
        }
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyInteraction_LongStudyId() {

        String studyId = "study_with_very_long_id_that_might_cause_issues_in_database_queries_123456789";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyInteraction(studyId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyInteraction_SpecialCharacters() {

        String studyId = "study@123#$%";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyInteraction(studyId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyInteraction_WhitespaceStudyId() {

        String studyId = "   ";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyInteraction(studyId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyParticipantInteraction_Success() {

        String studyId = "study123";
        String participantId = "participant123";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyParticipantInteraction(
                studyId, participantId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyParticipantInteraction_DatabaseException() {

        String studyId = "study123";
        String participantId = "participant123";

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database connection failed"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyParticipantInteraction(
                studyId, participantId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyParticipantInteraction_NullStudyId() {

        String participantId = "participant123";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyParticipantInteraction(
                null, participantId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyParticipantInteraction_NullParticipantId() {

        String studyId = "study123";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyParticipantInteraction(
                studyId, null);

        assertNotNull(result);
        assertTrue(result.isEmpty()); // Returns empty list on exception
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyParticipantInteraction_EmptyStudyId() {
        String studyId = "";
        String participantId = "participant123";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyParticipantInteraction(
                studyId, participantId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyParticipantInteraction_EmptyParticipantId() {
        String studyId = "study123";
        String participantId = "";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyParticipantInteraction(
                studyId, participantId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyParticipantInteraction_MultipleParticipants() {
        String[][] testCombinations = {
                { "study1", "participant1" },
                { "study2", "participant2" },
                { "study3", "participant3" },
                { "study4", "participant4" },
                { "study5", "participant5" }
        };

        for (String[] combination : testCombinations) {
            String studyId = combination[0];
            String participantId = combination[1];

            lenient().when(dsl.select(any(Field.class)))
                    .thenThrow(new RuntimeException("Database error for " + studyId + "/" + participantId));

            List<String> result = interactionService.getAndSetInteractionHierarchyofStudyParticipantInteraction(
                    studyId, participantId);

            assertNotNull(result,
                    "Should return non-null list for study: " + studyId + ", participant: " + participantId);
            assertTrue(result.isEmpty(),
                    "Should return empty list on exception for study: " + studyId + ", participant: " + participantId);

            reset(dsl);

            Configuration mockConfig = mock(Configuration.class);
            lenient().when(dsl.configuration()).thenReturn(mockConfig);
            lenient().when(mockConfig.dialect()).thenReturn(SQLDialect.POSTGRES);
        }
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyParticipantInteraction_LongIds() {
        String studyId = "study_with_very_long_id_that_might_cause_issues_in_database_queries_123456789";
        String participantId = "participant_with_very_long_id_that_might_cause_issues_in_database_queries_123456789";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyParticipantInteraction(
                studyId, participantId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyParticipantInteraction_SpecialCharacters() {
        String studyId = "study@123#$%";
        String participantId = "participant@123#$%";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyParticipantInteraction(
                studyId, participantId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAndSetInteractionHierarchyofStudyParticipantInteraction_WhitespaceIds() {
        String studyId = "   ";
        String participantId = "   ";

        lenient().when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = interactionService.getAndSetInteractionHierarchyofStudyParticipantInteraction(
                studyId, participantId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testSaveHubInteraction_EmptyStudyId() throws JsonProcessingException {
        String emptyStudyId = "";
        String jsonResponse = "{\"hub_interaction_id\":\"hub123\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        String result = interactionService.saveHubInteraction(emptyStudyId);

        assertEquals("hub123", result);
    }

    @Test
    void testSaveHubInteraction_NullStudyId() throws JsonProcessingException {
        String jsonResponse = "{\"hub_interaction_id\":\"hub123\"}";
        JSONB mockResult = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        String result = interactionService.saveHubInteraction(null);

        assertEquals("hub123", result);
    }

    @Test
    void testIsCompletedCgmRowData_False() {
        when(masterService.getActiontype(ActionType.SAVE_DB_CONTENT)).thenReturn(1);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);
        when(dsl.fetchExists(any(SelectSelectStep.class))).thenReturn(false);

        boolean result = interactionService.isCompletedCgmRowData(testStudyId);

        assertFalse(result);
    }

    @Test
    void testIsDbFileInteractionFinishedForAction_False() {
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);
        when(masterService.getActiontype(ActionType.DB_FILE_UPLOAD)).thenReturn(1);
        when(dsl.fetchExists(any(SelectSelectStep.class))).thenReturn(false);

        Boolean result = interactionService.isDbFileInteractionFinishedForAction(testStudyId,
                ActionType.DB_FILE_UPLOAD);

        assertFalse(result);
    }

    @Test
    void testGetDbFileIdOfCompletedCgmRowData_Success() {

        String studyId = "study123";
        String expectedDbFileId = "dbFile456";

        when(masterService.getActiontype(ActionType.SAVE_DB_CONTENT)).thenReturn(3);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock2 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock3 = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock1);
        when(andMock1.and(any(Condition.class))).thenReturn(andMock2);
        when(andMock2.and(any(Condition.class))).thenReturn(andMock3);
        when(andMock3.fetchOneInto(String.class)).thenReturn(expectedDbFileId);

        String result = interactionService.getDbFileIdOfCompletedCgmRowData(studyId);

        assertNotNull(result);
        assertEquals(expectedDbFileId, result);
        verify(masterService).getActiontype(ActionType.SAVE_DB_CONTENT);
        verify(masterService).getInteractionStatus(FileProcessingStatus.SUCCESS);
    }

    @Test
    void testGetDbFileIdOfCompletedCgmRowData_NotFound() {

        String studyId = "study123";

        when(masterService.getActiontype(ActionType.SAVE_DB_CONTENT)).thenReturn(3);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock2 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock3 = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock1);
        when(andMock1.and(any(Condition.class))).thenReturn(andMock2);
        when(andMock2.and(any(Condition.class))).thenReturn(andMock3);
        when(andMock3.fetchOneInto(String.class)).thenReturn(null);

        String result = interactionService.getDbFileIdOfCompletedCgmRowData(studyId);

        assertNull(result);
        verify(masterService).getActiontype(ActionType.SAVE_DB_CONTENT);
        verify(masterService).getInteractionStatus(FileProcessingStatus.SUCCESS);
    }

    @Test
    void testGetDbFileIdOfCompletedCgmRowData_NullStudyId() {

        when(masterService.getActiontype(ActionType.SAVE_DB_CONTENT)).thenReturn(3);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock2 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock3 = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock1);
        when(andMock1.and(any(Condition.class))).thenReturn(andMock2);
        when(andMock2.and(any(Condition.class))).thenReturn(andMock3);
        when(andMock3.fetchOneInto(String.class)).thenReturn(null);

        String result = interactionService.getDbFileIdOfCompletedCgmRowData(null);

        assertNull(result);
        verify(masterService).getActiontype(ActionType.SAVE_DB_CONTENT);
        verify(masterService).getInteractionStatus(FileProcessingStatus.SUCCESS);
    }

    @Test
    void testGetDbFileIdOfCompletedCgmRowData_EmptyStudyId() {

        String studyId = "";

        when(masterService.getActiontype(ActionType.SAVE_DB_CONTENT)).thenReturn(3);
        when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

        SelectSelectStep<Record1<String>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<String>> fromMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<String>> whereMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock1 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock2 = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<String>> andMock3 = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock1);
        when(andMock1.and(any(Condition.class))).thenReturn(andMock2);
        when(andMock2.and(any(Condition.class))).thenReturn(andMock3);
        when(andMock3.fetchOneInto(String.class)).thenReturn(null);

        String result = interactionService.getDbFileIdOfCompletedCgmRowData(studyId);

        assertNull(result);
        verify(masterService).getActiontype(ActionType.SAVE_DB_CONTENT);
        verify(masterService).getInteractionStatus(FileProcessingStatus.SUCCESS);
    }

    @Test
    void testGetDbFileIdOfCompletedCgmRowData_MasterServiceException() {

        String studyId = "study123";
        lenient().when(masterService.getActiontype(ActionType.SAVE_DB_CONTENT))
                .thenThrow(new RuntimeException("Master service error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getDbFileIdOfCompletedCgmRowData(studyId);
        });
    }

    @Test
    void testGetDbFileIdOfCompletedCgmRowData_DatabaseException() {

        String studyId = "study123";

        lenient().when(masterService.getActiontype(ActionType.SAVE_DB_CONTENT)).thenReturn(3);
        lenient().when(masterService.getInteractionStatus(FileProcessingStatus.SUCCESS)).thenReturn(1);

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database connection failed"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getDbFileIdOfCompletedCgmRowData(studyId);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudy_NullStudyId() {

        lenient().when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudy(null);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudy_EmptyStudyId() {

        String studyId = "";

        lenient().when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudy(studyId);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudy_MultipleStudies() {

        String[] studyIds = { "study1", "study2", "study3", "study4", "study5" };

        for (String studyId : studyIds) {
            lenient().when(dsl.selectDistinct(any(Field.class)))
                    .thenThrow(new RuntimeException("Database error for " + studyId));

            assertThrows(RuntimeException.class, () -> {
                interactionService.getHubIntercationIdOfStudy(studyId);
            }, "Should throw exception for study: " + studyId);

            reset(dsl);

            Configuration mockConfig = mock(Configuration.class);
            lenient().when(dsl.configuration()).thenReturn(mockConfig);
            lenient().when(mockConfig.dialect()).thenReturn(SQLDialect.POSTGRES);
        }
    }

    @Test
    void testGetHubIntercationIdOfStudyParticipant_NullParticipantId() {

        lenient().when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudyParticipant(null);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudyParticipant_EmptyParticipantId() {

        String participantId = "";

        lenient().when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudyParticipant(participantId);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudyParticipant_MultipleParticipants() {

        String[] participantIds = { "participant1", "participant2", "participant3", "participant4", "participant5" };

        for (String participantId : participantIds) {

            lenient().when(dsl.selectDistinct(any(Field.class)))
                    .thenThrow(new RuntimeException("Database error for " + participantId));

            assertThrows(RuntimeException.class, () -> {
                interactionService.getHubIntercationIdOfStudyParticipant(participantId);
            }, "Should throw exception for participant: " + participantId);

            reset(dsl);

            Configuration mockConfig = mock(Configuration.class);
            lenient().when(dsl.configuration()).thenReturn(mockConfig);
            lenient().when(mockConfig.dialect()).thenReturn(SQLDialect.POSTGRES);
        }
    }

    @Test
    void testGetHubIntercationIdOfStudyParticipant_LongParticipantId() {

        String participantId = "participant_with_very_long_id_that_might_cause_issues_in_database_queries_123456789";

        lenient().when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudyParticipant(participantId);
        });
    }

    @Test
    void testGetHubIntercationIdOfStudyParticipant_SpecialCharacters() {

        String participantId = "participant@123#$%";

        lenient().when(dsl.selectDistinct(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            interactionService.getHubIntercationIdOfStudyParticipant(participantId);
        });
    }

    @Test
    void testSaveStudyInteraction_WithNullRequest() {

        try (MockedStatic<HttpRequestResponseUtil> mockedUtil = mockStatic(HttpRequestResponseUtil.class)) {
            mockedUtil.when(HttpRequestResponseUtil::getCurrentRequest).thenReturn(httpServletRequest);
            when(httpServletRequest.getRequestURI()).thenReturn(testUri);
            when(masterService.getActiontype(anyString())).thenReturn(1);
            when(masterService.getInteractionStatus(anyString())).thenReturn(1);

            InteractionService spyService = spy(interactionService);
            doReturn(new ArrayList<>()).when(spyService).getAndSetInteractionHierarchyofStudyInteraction(testStudyId);

            JSONB mockResult = JSONB.valueOf("{\"status\":\"success\"}");
            SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
            when(dsl.select(any(Field.class))).thenReturn(selectMock);
            when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

            assertDoesNotThrow(() -> {
                spyService.saveStudyInteraction(testStudyId, testHubInteractionId, "CREATE", "Test description",
                        "DRAFT", "ACTIVE", null, "{\"test\":\"response\"}", null, 200, "SUCCESS",
                        ActionType.DB_FILE_UPLOAD, "COMPLETED");
            });
        }
    }

    @Test
    void testSaveStudyInteraction_WithNullResponse() {

        try (MockedStatic<HttpRequestResponseUtil> mockedUtil = mockStatic(HttpRequestResponseUtil.class)) {
            mockedUtil.when(HttpRequestResponseUtil::getCurrentRequest).thenReturn(httpServletRequest);
            when(httpServletRequest.getRequestURI()).thenReturn(testUri);
            when(masterService.getActiontype(anyString())).thenReturn(1);
            when(masterService.getInteractionStatus(anyString())).thenReturn(1);

            InteractionService spyService = spy(interactionService);
            doReturn(new ArrayList<>()).when(spyService).getAndSetInteractionHierarchyofStudyInteraction(testStudyId);

            JSONB mockResult = JSONB.valueOf("{\"status\":\"success\"}");
            SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
            when(dsl.select(any(Field.class))).thenReturn(selectMock);
            when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

            assertDoesNotThrow(() -> {
                spyService.saveStudyInteraction(testStudyId, testHubInteractionId, "CREATE", "Test description",
                        "DRAFT", "ACTIVE", "{\"test\":\"request\"}", null, null, 200, "SUCCESS",
                        ActionType.DB_FILE_UPLOAD, "COMPLETED");
            });
        }
    }

    @Test
    void testSaveStudyParticipantInteraction_WithErrorResponse() {

        try (MockedStatic<HttpRequestResponseUtil> mockedUtil = mockStatic(HttpRequestResponseUtil.class)) {
            mockedUtil.when(HttpRequestResponseUtil::getCurrentRequest).thenReturn(httpServletRequest);
            when(httpServletRequest.getRequestURI()).thenReturn(testUri);
            when(masterService.getActiontype(anyString())).thenReturn(1);
            when(masterService.getInteractionStatus(anyString())).thenReturn(1);

            InteractionService spyService = spy(interactionService);
            doReturn(new ArrayList<>()).when(spyService)
                    .getAndSetInteractionHierarchyofStudyParticipantInteraction(testStudyId, testParticipantId);

            JSONB mockResult = JSONB.valueOf("{\"status\":\"success\"}");
            SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
            when(dsl.select(any(Field.class))).thenReturn(selectMock);
            when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockResult);

            assertDoesNotThrow(() -> {
                spyService.saveStudyParticipantInteraction(testStudyId, testParticipantId, testHubInteractionId,
                        "CREATE",
                        "Test description", "DRAFT", "ACTIVE", "{\"test\":\"request\"}", "{\"test\":\"response\"}",
                        "{\"error\":\"test error\"}", 500, "ERROR", ActionType.DB_FILE_UPLOAD, "FAILED");
            });
        }
    }

}
