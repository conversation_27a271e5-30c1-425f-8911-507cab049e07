package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.ux.Presentation;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SessionReportServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private Presentation presentation;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;

    @InjectMocks
    private SessionReportService sessionReportService;

    @Mock
    private SelectSelectStep<Record1<JSONB>> selectStep;

    @Mock
    private SelectJoinStep<Record1<JSONB>> joinStep;

    @Mock
    private SelectConditionStep<Record1<JSONB>> conditionStep;

    @BeforeEach
    void setUp() {
        // Common setup for mocks
    }

    @Test
    void testGetSessionDetails_SuperAdmin_Success() {
        // Arrange
        when(presentation.isSuperAdmin()).thenReturn(true);
        when(userNameService.isAdmin()).thenReturn(false);

        JSONB mockResult = JSONB.valueOf("{\"sessions\": []}");

        // Mock the DSL chain
        when(dsl.select(any(Field.class))).thenReturn(selectStep);
        when(selectStep.from("drh_stateless_activity_audit.session_duration_view")).thenReturn(joinStep);
        when(joinStep.where(any(Condition.class))).thenReturn(conditionStep);
        when(conditionStep.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        // Act
        Object result = sessionReportService.getSessionDetails();

        // Assert
        assertNotNull(result);
        assertEquals(mockResult, result);
        verify(presentation).isSuperAdmin();
        verify(userNameService).isAdmin();
    }

    @Test
    void testGetSessionDetails_NormalUser_Success() {
        // Arrange
        when(presentation.isSuperAdmin()).thenReturn(false);
        when(userNameService.isAdmin()).thenReturn(false);
        when(userNameService.getUserId()).thenReturn("user123");
        when(partyService.getPartyIdByUserId("user123")).thenReturn("party123");

        JSONB mockResult = JSONB.valueOf("{\"sessions\": []}");

        // Mock the DSL chain
        when(dsl.select(any(Field.class))).thenReturn(selectStep);
        when(selectStep.from("drh_stateless_activity_audit.session_duration_view")).thenReturn(joinStep);
        when(joinStep.where(any(Condition.class))).thenReturn(conditionStep);
        when(conditionStep.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        // Act
        Object result = sessionReportService.getSessionDetails();

        // Assert
        assertNotNull(result);
        assertEquals(mockResult, result);
        verify(presentation).isSuperAdmin();
        verify(userNameService).isAdmin();
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId("user123");
    }

    @Test
    void testGetSessionDetails_InvalidUser_ReturnsEmptyMap() {
        // Arrange - Test edge case where normal user has no valid party ID
        when(presentation.isSuperAdmin()).thenReturn(false);
        when(userNameService.isAdmin()).thenReturn(false);
        when(userNameService.getUserId()).thenReturn("user123");
        when(partyService.getPartyIdByUserId("user123")).thenReturn(null);

        // Act
        Object result = sessionReportService.getSessionDetails();

        // Assert
        assertNotNull(result);
        assertEquals(Map.of(), result);
        verify(presentation).isSuperAdmin();
        verify(userNameService).isAdmin();
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId("user123");
        verifyNoInteractions(dsl);
    }

    @Test
    void testGetSessionDetails_Exception_ReturnsErrorResponse() {
        // Arrange
        when(presentation.isSuperAdmin()).thenThrow(new RuntimeException("Database error"));

        // Act
        Object result = sessionReportService.getSessionDetails();

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof Response);
        Response response = (Response) result;
        assertEquals(Map.of(), response.getData());
        assertEquals("error", response.getStatus());
        assertEquals("Failed to fetch session details", response.getMessage());
        assertEquals("Database error", response.getErrors());
    }

    @Test
    void testGetOrganizationSessionReport_Success() {
        // Arrange
        String organizationId = "org123";
        String startDate = "2023-01-01";
        String endDate = "2023-12-31";
        JSONB mockResult = JSONB.valueOf("{\"report\": \"data\"}");

        // Mock the DSL chain
        when(dsl.select(any(Field.class))).thenReturn(selectStep);
        when(selectStep.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        // Act
        JSONB result = sessionReportService.getOrganizationSessionReport(organizationId, startDate, endDate);

        // Assert
        assertNotNull(result);
        assertEquals(mockResult, result);
    }

    @Test
    void testGetApplicationSessionReport_Success() {
        // Arrange
        String startDate = "2023-01-01";
        String endDate = "2023-12-31";
        JSONB mockResult = JSONB.valueOf("{\"report\": \"data\"}");

        // Mock the DSL chain
        when(dsl.select(any(Field.class))).thenReturn(selectStep);
        when(selectStep.fetchOneInto(JSONB.class)).thenReturn(mockResult);

        // Act
        JSONB result = sessionReportService.getApplicationSessionReport(startDate, endDate);

        // Assert
        assertNotNull(result);
        assertEquals(mockResult, result);
    }
}
