package org.diabetestechnology.drh.service.http.pg.service.email;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentMap;

import org.diabetestechnology.drh.service.http.pg.constant.email.OtpValidationResponse;
import org.diabetestechnology.drh.service.http.pg.constant.email.OtpVerificationState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OtpServiceTest {

    @InjectMocks
    private OtpService otpService;

    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String INVALID_EMAIL = "<EMAIL>";

    @BeforeEach
    void setUp() {
        otpService = new OtpService();
    }

    @Test
    void testGenerateAndSendOtp_ShouldGenerateValidOtp() {
        // When
        String otp = otpService.generateAndSendOtp(TEST_EMAIL);

        // Then
        assertNotNull(otp);
        assertEquals(6, otp.length());
        assertTrue(otp.matches("\\d{6}"));
    }

    @Test
    void testGenerateAndSendOtp_ShouldStoreOtpInStore() throws Exception {
        // When
        String otp = otpService.generateAndSendOtp(TEST_EMAIL);

        // Then
        ConcurrentMap<String, OtpVerificationState> otpStore = getOtpStore();
        assertTrue(otpStore.containsKey(TEST_EMAIL));
        assertEquals(otp, otpStore.get(TEST_EMAIL).getOtp());
    }

    @Test
    void testVerifyOtp_WithValidOtp_ShouldReturnSuccess() {
        // Given
        String otp = otpService.generateAndSendOtp(TEST_EMAIL);

        // When
        String result = otpService.verifyOtp(TEST_EMAIL, otp);

        // Then
        assertEquals(OtpValidationResponse.OTP_VERIFICATION_SUCCESS, result);
    }

    @Test
    void testVerifyOtp_WithInvalidOtp_ShouldReturnFailure() {
        // Given
        otpService.generateAndSendOtp(TEST_EMAIL);

        // When
        String result = otpService.verifyOtp(TEST_EMAIL, "123456");

        // Then
        assertEquals(OtpValidationResponse.OTP_VERIFICATION_FAILURE, result);
    }

    @Test
    void testVerifyOtp_WithNonExistentEmail_ShouldReturnNotFound() {
        // When
        String result = otpService.verifyOtp(INVALID_EMAIL, "123456");

        // Then
        assertEquals(OtpValidationResponse.OTP_NOT_FOUND, result);
    }

    @Test
    void testVerifyOtp_WithExpiredOtp_ShouldReturnExpired() throws Exception {
        // Given
        String otp = otpService.generateAndSendOtp(TEST_EMAIL);

        // Manually expire the OTP by setting expiry to past
        ConcurrentMap<String, OtpVerificationState> otpStore = getOtpStore();
        OtpVerificationState state = otpStore.get(TEST_EMAIL);
        setExpiredState(state);

        // When
        String result = otpService.verifyOtp(TEST_EMAIL, otp);

        // Then
        assertEquals(OtpValidationResponse.OTP_EXPIRED, result);
    }

    @Test
    void testVerifyOtp_WithMaxAttemptsExceeded_ShouldReturnMaxAttemptsExceeded() throws Exception {
        // Given
        String otp = otpService.generateAndSendOtp(TEST_EMAIL);

        // Exhaust retry attempts
        otpService.verifyOtp(TEST_EMAIL, "wrong1");
        otpService.verifyOtp(TEST_EMAIL, "wrong2");
        otpService.verifyOtp(TEST_EMAIL, "wrong3");

        // When
        String result = otpService.verifyOtp(TEST_EMAIL, otp);

        // Then
        assertEquals(OtpValidationResponse.OTP_MAX_ATTEMPTS_EXCEEDED, result);
    }

    @Test
    void testVerifyOtp_ShouldRemoveOtpAfterSuccessfulVerification() throws Exception {
        // Given
        String otp = otpService.generateAndSendOtp(TEST_EMAIL);

        // When
        otpService.verifyOtp(TEST_EMAIL, otp);

        // Then
        ConcurrentMap<String, OtpVerificationState> otpStore = getOtpStore();
        assertTrue(!otpStore.containsKey(TEST_EMAIL));
    }

    @Test
    void testVerifyOtp_ShouldRemoveOtpAfterExpiry() throws Exception {
        // Given
        String otp = otpService.generateAndSendOtp(TEST_EMAIL);

        // Manually expire the OTP
        ConcurrentMap<String, OtpVerificationState> otpStore = getOtpStore();
        OtpVerificationState state = otpStore.get(TEST_EMAIL);
        setExpiredState(state);

        // When
        otpService.verifyOtp(TEST_EMAIL, otp);

        // Then
        assertTrue(!otpStore.containsKey(TEST_EMAIL));
    }

    @Test
    void testVerifyOtp_ShouldRemoveOtpAfterMaxAttempts() throws Exception {
        // Given
        String otp = otpService.generateAndSendOtp(TEST_EMAIL);

        // Exhaust retry attempts
        otpService.verifyOtp(TEST_EMAIL, "wrong1");
        otpService.verifyOtp(TEST_EMAIL, "wrong2");
        otpService.verifyOtp(TEST_EMAIL, "wrong3");

        // When
        otpService.verifyOtp(TEST_EMAIL, otp);

        // Then
        ConcurrentMap<String, OtpVerificationState> otpStore = getOtpStore();
        assertTrue(!otpStore.containsKey(TEST_EMAIL));
    }

    @Test
    void testMultipleOtpGeneration_ShouldOverwritePreviousOtp() throws Exception {
        // Given
        String firstOtp = otpService.generateAndSendOtp(TEST_EMAIL);
        String secondOtp = otpService.generateAndSendOtp(TEST_EMAIL);

        // When
        String result = otpService.verifyOtp(TEST_EMAIL, secondOtp);

        // Then
        assertEquals(OtpValidationResponse.OTP_VERIFICATION_SUCCESS, result);

        // Verify first OTP is no longer valid
        otpService.generateAndSendOtp(TEST_EMAIL);
        String invalidResult = otpService.verifyOtp(TEST_EMAIL, firstOtp);
        assertEquals(OtpValidationResponse.OTP_VERIFICATION_FAILURE, invalidResult);
    }

    // Helper methods for reflection-based testing
    @SuppressWarnings("unchecked")
    private ConcurrentMap<String, OtpVerificationState> getOtpStore() throws Exception {
        Field otpStoreField = OtpService.class.getDeclaredField("otpStore");
        otpStoreField.setAccessible(true);
        return (ConcurrentMap<String, OtpVerificationState>) otpStoreField.get(otpService);
    }

    private void setExpiredState(OtpVerificationState state) throws Exception {
        Field expiryField = OtpVerificationState.class.getDeclaredField("expiry");
        expiryField.setAccessible(true);
        expiryField.set(state, java.time.LocalDateTime.now().minusMinutes(1));
    }
}
