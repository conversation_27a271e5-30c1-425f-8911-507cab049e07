package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectLimitPercentStep;
import org.jooq.SelectSelectStep;
import org.jooq.Table;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;

@SuppressWarnings("unchecked")
public class AuthUserDetailsServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    @Mock
    private UserDetails userDetails;

    @Mock
    private DefaultOAuth2User oAuth2User;

    @InjectMocks
    private AuthUserDetailsService authUserDetailsService;

    private MockedStatic<SecurityContextHolder> securityContextHolderMock;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        securityContextHolderMock = mockStatic(SecurityContextHolder.class);
        securityContextHolderMock.when(SecurityContextHolder::getContext).thenReturn(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
    }

    @AfterEach
    void tearDown() {
        securityContextHolderMock.close();
    }

    @Test
    void testGetRoles_EmailProvider_Success() {

        String userId = "<EMAIL>";
        String authProvider = "email";
        List<String> expectedRoles = List.of("Super Admin", "Admin");

        SelectSelectStep<Record1<Object>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Object>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Object>> whereStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("role_name"))).thenReturn(selectMock);
        when(selectMock.from(any(Table.class))).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.fetch(DSL.field("role_name"), String.class)).thenReturn(expectedRoles);

        List<String> result = authUserDetailsService.getRoles(userId, authProvider);

        assertNotNull(result);
        assertEquals(expectedRoles, result);
    }

    @Test
    void testGetRoles_EmailProvider_EmptyResult() {

        String userId = "<EMAIL>";
        String authProvider = "email";

        SelectSelectStep<Record1<Object>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Object>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Object>> whereStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("role_name"))).thenReturn(selectMock);
        when(selectMock.from(any(Table.class))).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.fetch(DSL.field("role_name"), String.class)).thenReturn(List.of());

        List<String> result = authUserDetailsService.getRoles(userId, authProvider);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetRoles_OAuthProvider_Success() {

        String userId = "github123";
        String authProvider = "GitHub";
        List<String> expectedRoles = List.of("Researcher", "User");

        Result<Record> resultMock = mock(Result.class);
        when(dsl.fetch(anyString(), eq(userId), eq(authProvider))).thenReturn(resultMock);
        when(resultMock.getValues("role_name", String.class)).thenReturn(expectedRoles);

        List<String> result = authUserDetailsService.getRoles(userId, authProvider);

        assertNotNull(result);
        assertEquals(expectedRoles, result);
    }

    @Test
    void testGetRoles_OAuthProvider_EmptyResult() {

        String userId = "github456";
        String authProvider = "GitHub";

        Result<Record> resultMock = mock(Result.class);
        when(dsl.fetch(anyString(), eq(userId), eq(authProvider))).thenReturn(resultMock);
        when(resultMock.getValues("role_name", String.class)).thenReturn(List.of());

        List<String> result = authUserDetailsService.getRoles(userId, authProvider);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testIsSuperAdmin_True() {

        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        Record1<Boolean> recordMock = mock(Record1.class);
        when(dsl.fetchOne(any(SelectSelectStep.class))).thenReturn(recordMock);
        when(recordMock.value1()).thenReturn(true);

        boolean result = authUserDetailsService.isSuperAdmin();

        assertTrue(result);
    }

    @Test
    void testIsSuperAdmin_False() {

        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        Record1<Boolean> recordMock = mock(Record1.class);
        when(dsl.fetchOne(any(SelectSelectStep.class))).thenReturn(recordMock);
        when(recordMock.value1()).thenReturn(false);

        boolean result = authUserDetailsService.isSuperAdmin();

        assertFalse(result);
    }

    @Test
    void testIsSuperAdmin_NotUserDetails() {

        when(authentication.getPrincipal()).thenReturn(oAuth2User);

        boolean result = authUserDetailsService.isSuperAdmin();

        assertFalse(result);
    }

    @Test
    void testGetEmailLoginUserFullName_Success() {

        String expectedFullName = "John Doe";
        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        SelectSelectStep<Record1<Object>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Object>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Object>> whereStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("full_name"))).thenReturn(selectMock);
        when(selectMock.from(any(Table.class))).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.fetchOneInto(String.class)).thenReturn(expectedFullName);

        String result = authUserDetailsService.getEmailLoginUserFullName();

        assertEquals(expectedFullName, result);
    }

    @Test
    void testGetEmailLoginUserFullName_NotUserDetails() {

        when(authentication.getPrincipal()).thenReturn(oAuth2User);

        String result = authUserDetailsService.getEmailLoginUserFullName();

        assertEquals("Anonymous", result);
    }

    @Test
    void testGetEmailLoginUserFullName_NullUsername() {

        String expectedFullName = "Anonymous User";
        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(userDetails.getUsername()).thenReturn(null);

        SelectSelectStep<Record1<Object>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Object>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Object>> whereStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("full_name"))).thenReturn(selectMock);
        when(selectMock.from(any(Table.class))).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.fetchOneInto(String.class)).thenReturn(expectedFullName);

        String result = authUserDetailsService.getEmailLoginUserFullName();

        assertEquals(expectedFullName, result);
    }

    @Test
    void testGetLoginUserFullName_UserDetails() {

        String expectedFullName = "Jane Smith";
        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        SelectSelectStep<Record1<Object>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Object>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Object>> whereStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("full_name"))).thenReturn(selectMock);
        when(selectMock.from(any(Table.class))).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.fetchOneInto(String.class)).thenReturn(expectedFullName);

        Object result = authUserDetailsService.getLoginUserFullName();

        assertEquals(expectedFullName, result);
    }

    @Test
    void testGetLoginUserFullName_OAuth2User_ExistsInDatabase() {

        String expectedFirstName = "GitHub User";
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("login", "githubuser123");
        attributes.put("name", "GitHub User Full Name");

        when(authentication.getPrincipal()).thenReturn(oAuth2User);
        when(oAuth2User.getAttribute("login")).thenReturn("githubuser123");

        SelectSelectStep<Record1<Boolean>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Boolean>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Boolean>> whereStepMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<Boolean>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.value(true))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.user_profile_view")).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOptional()).thenReturn(Optional.of(mock(Record1.class)));

        SelectSelectStep<Record1<Object>> nameSelectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Object>> nameFromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Object>> nameWhereStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("first_name"))).thenReturn(nameSelectMock);
        when(nameSelectMock.from(any(Table.class))).thenReturn(nameFromStepMock);
        when(nameFromStepMock.where(any(Condition.class))).thenReturn(nameWhereStepMock);
        when(nameWhereStepMock.fetchOneInto(String.class)).thenReturn(expectedFirstName);

        Object result = authUserDetailsService.getLoginUserFullName();
        assertEquals(expectedFirstName, result);
    }

    @Test
    void testGetLoginUserFullName_OAuth2User_NotInDatabase() {

        Map<String, Object> attributes = new HashMap<>();
        attributes.put("login", "newgithubuser");
        attributes.put("name", "New GitHub User");

        when(authentication.getPrincipal()).thenReturn(oAuth2User);
        when(oAuth2User.getAttribute("login")).thenReturn("newgithubuser");
        when(oAuth2User.getAttribute("name")).thenReturn("New GitHub User");

        SelectSelectStep<Record1<Boolean>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Boolean>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Boolean>> whereStepMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<Boolean>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.value(true))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.user_profile_view")).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOptional()).thenReturn(Optional.empty());

        Object result = authUserDetailsService.getLoginUserFullName();

        assertEquals("New GitHub User", result);
    }

    @Test
    void testGetLoginUserFullName_OAuth2User_NoNameAttribute() {

        Map<String, Object> attributes = new HashMap<>();
        attributes.put("login", "userwithnologin");

        when(authentication.getPrincipal()).thenReturn(oAuth2User);
        when(oAuth2User.getAttribute("login")).thenReturn("userwithnologin");
        when(oAuth2User.getAttribute("name")).thenReturn(null);

        SelectSelectStep<Record1<Boolean>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Boolean>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Boolean>> whereStepMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<Boolean>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.value(true))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.user_profile_view")).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOptional()).thenReturn(Optional.empty());

        Object result = authUserDetailsService.getLoginUserFullName();

        assertEquals("userwithnologin", result);
    }

    @Test
    void testGetLoginUserFullName_UnknownPrincipalType() {

        String unknownPrincipal = "unknown";
        when(authentication.getPrincipal()).thenReturn(unknownPrincipal);

        Object result = authUserDetailsService.getLoginUserFullName();

        assertEquals("Anonymous", result);
    }

    @Test
    void testGetRoles_EmailProvider_NullResult() {
        String userId = "<EMAIL>";
        String authProvider = "email";

        SelectSelectStep<Record1<Object>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Object>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Object>> whereStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("role_name"))).thenReturn(selectMock);
        when(selectMock.from(any(Table.class))).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.fetch(DSL.field("role_name"), String.class)).thenReturn(null);

        List<String> result = authUserDetailsService.getRoles(userId, authProvider);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetRoles_OAuthProvider_NullResult() {

        String userId = "github789";
        String authProvider = "GitHub";

        Result<Record> resultMock = mock(Result.class);
        when(dsl.fetch(anyString(), eq(userId), eq(authProvider))).thenReturn(resultMock);
        when(resultMock.getValues("role_name", String.class)).thenReturn(null);

        List<String> result = authUserDetailsService.getRoles(userId, authProvider);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testIsSuperAdmin_NullUsername() {

        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(userDetails.getUsername()).thenReturn(null);

        Record1<Boolean> recordMock = mock(Record1.class);
        when(dsl.fetchOne(any(SelectSelectStep.class))).thenReturn(recordMock);
        when(recordMock.value1()).thenReturn(false);

        boolean result = authUserDetailsService.isSuperAdmin();

        assertFalse(result);
    }
}
