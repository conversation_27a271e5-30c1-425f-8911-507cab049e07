package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import org.diabetestechnology.drh.service.http.pg.Response;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.SelectFromStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("unchecked")
public class SuperAdminServiceTest {

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private DSLContext dsl;

    @Mock
    private Record recordMock;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private SelectSelectStep<Record1<JSONB>> selectJsonbMock;

    @Mock
    private Record1<JSONB> jsonbRecordMock;

    @Mock
    private SelectSelectStep<Record> selectMock;

    @Mock
    private SelectJoinStep<Record> fromMock;

    @Mock
    private SelectConditionStep<Record> whereMock;

    @Mock
    private SelectConditionStep<Record> andMock;

    @InjectMocks
    private SuperAdminService superAdminService;

    private final String testEmail = "<EMAIL>";
    private final String testPassword = "password123";
    private final String testOrgPartyId = "org123";
    private final String testFullName = "John Doe";
    private final String superAdminPassword = "superAdminPass";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(superAdminService, "superAdminPassword", superAdminPassword);
        ReflectionTestUtils.setField(superAdminService, "organizationPartyId", testOrgPartyId);
    }

    @Test
    void testAuthenticateSuperAdmin_Success() {
        String hashedPassword = "$2a$10$hashedPasswordExample";

        when(dsl.select()).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.super_admin_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock);
        when(andMock.fetchOne()).thenReturn(recordMock);
        when(recordMock.get("password", String.class)).thenReturn(hashedPassword);
        when(passwordEncoder.matches(testPassword, hashedPassword)).thenReturn(true);

        try (MockedStatic<SecurityContextHolder> mockedSecurityContextHolder = mockStatic(
                SecurityContextHolder.class)) {
            mockedSecurityContextHolder.when(SecurityContextHolder::getContext).thenReturn(securityContext);

            boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, testOrgPartyId);

            assertTrue(result);
            verify(passwordEncoder).matches(testPassword, hashedPassword);
            verify(securityContext).setAuthentication(any(UsernamePasswordAuthenticationToken.class));
        }
    }

    @Test
    void testAuthenticateSuperAdmin_UserNotFound() {
        when(dsl.select()).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.super_admin_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock);
        when(andMock.fetchOne()).thenReturn(null);

        boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, testOrgPartyId);

        assertFalse(result);
        verify(passwordEncoder, never()).matches(any(), any());
    }

    @Test
    void testAuthenticateSuperAdmin_PasswordMismatch() {
        String hashedPassword = "$2a$10$hashedPasswordExample";

        when(dsl.select()).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.super_admin_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock);
        when(andMock.fetchOne()).thenReturn(recordMock);
        when(recordMock.get("password", String.class)).thenReturn(hashedPassword);
        when(passwordEncoder.matches(testPassword, hashedPassword)).thenReturn(false);

        boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, testOrgPartyId);

        assertFalse(result);
        verify(passwordEncoder).matches(testPassword, hashedPassword);
    }

    @Test
    void testAuthenticateSuperAdmin_DatabaseException() {
        when(dsl.select()).thenThrow(new RuntimeException("Database connection error"));

        boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, testOrgPartyId);

        assertFalse(result);
        verify(passwordEncoder, never()).matches(any(), any());
    }

    @Test
    void testAuthenticateSuperAdmin_NullEmail() {
        boolean result = superAdminService.authenticateSuperAdmin(null, testPassword, testOrgPartyId);

        assertFalse(result);
        verify(passwordEncoder, never()).matches(any(), any());
    }

    @Test
    void testAuthenticateSuperAdmin_EmptyEmail() {
        boolean result = superAdminService.authenticateSuperAdmin("", testPassword, testOrgPartyId);

        assertFalse(result);
        verify(passwordEncoder, never()).matches(any(), any());
    }

    @Test
    void testAuthenticateSuperAdmin_NullPassword() {
        boolean result = superAdminService.authenticateSuperAdmin(testEmail, null, testOrgPartyId);

        assertFalse(result);
    }

    @Test
    void testAuthenticateSuperAdmin_EmptyPassword() {
        boolean result = superAdminService.authenticateSuperAdmin(testEmail, "", testOrgPartyId);

        assertFalse(result);
    }

    @Test
    void testAuthenticateSuperAdmin_NullOrganizationPartyId() {
        boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, null);

        assertFalse(result);
    }

    @Test
    void testAuthenticateSuperAdmin_EmptyOrganizationPartyId() {
        boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, "");

        assertFalse(result);
    }

    @Test
    void testAuthenticateSuperAdmin_GeneralException() {
        when(dsl.select()).thenThrow(new RuntimeException("General database error"));

        boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, testOrgPartyId);

        assertFalse(result);
        verify(passwordEncoder, never()).matches(any(), any());
    }

    @Test
    void testAuthenticateSuperAdmin_InvalidEmailFormat() {
        boolean result = superAdminService.authenticateSuperAdmin("invalid-email", testPassword, testOrgPartyId);

        assertFalse(result);
    }

    @Test
    void testAuthenticateSuperAdmin_SpecialCharactersInPassword() {
        boolean result = superAdminService.authenticateSuperAdmin(testEmail, "p@ssw0rd!@#$%", testOrgPartyId);

        assertFalse(result);
    }

    @Test
    void testAuthenticateSuperAdmin_LongEmail() {
        String longEmail = "a".repeat(100) + "@example.com";

        boolean result = superAdminService.authenticateSuperAdmin(longEmail, testPassword, testOrgPartyId);

        assertFalse(result);
    }

    @Test
    void testAuthenticateSuperAdmin_LongPassword() {
        String longPassword = "password".repeat(50);

        boolean result = superAdminService.authenticateSuperAdmin(testEmail, longPassword, testOrgPartyId);

        assertFalse(result);
    }

    @Test
    void testAuthenticateSuperAdmin_ValidEmailDifferentOrganization() {
        String differentOrgId = "differentOrg123";

        when(dsl.select()).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.super_admin_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock);
        when(andMock.fetchOne()).thenReturn(null);

        boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, differentOrgId);

        assertFalse(result);
        verify(passwordEncoder, never()).matches(any(), any());
    }

    @Test
    void testAuthenticateSuperAdmin_CaseInsensitiveEmail() {
        String hashedPassword = "$2a$10$hashedPasswordExample";
        String upperCaseEmail = testEmail.toUpperCase();

        when(dsl.select()).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.super_admin_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock);
        when(andMock.fetchOne()).thenReturn(recordMock);
        when(recordMock.get("password", String.class)).thenReturn(hashedPassword);
        when(passwordEncoder.matches(testPassword, hashedPassword)).thenReturn(true);

        try (MockedStatic<SecurityContextHolder> mockedSecurityContextHolder = mockStatic(
                SecurityContextHolder.class)) {
            mockedSecurityContextHolder.when(SecurityContextHolder::getContext).thenReturn(securityContext);

            boolean result = superAdminService.authenticateSuperAdmin(upperCaseEmail, testPassword, testOrgPartyId);

            assertTrue(result);
            verify(passwordEncoder).matches(testPassword, hashedPassword);
            verify(securityContext).setAuthentication(any(UsernamePasswordAuthenticationToken.class));
        }
    }

    @Test
    void testAuthenticateSuperAdmin_WhitespaceInInputs() {
        String emailWithSpaces = "  " + testEmail + "  ";
        String passwordWithSpaces = "  " + testPassword + "  ";
        String orgIdWithSpaces = "  " + testOrgPartyId + "  ";

        boolean result = superAdminService.authenticateSuperAdmin(emailWithSpaces, passwordWithSpaces, orgIdWithSpaces);

        assertFalse(result);
    }

    @Test
    void testAuthenticateSuperAdmin_SecurityContextException() {
        String hashedPassword = "$2a$10$hashedPasswordExample";

        when(dsl.select()).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.super_admin_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock);
        when(andMock.fetchOne()).thenReturn(recordMock);
        when(recordMock.get("password", String.class)).thenReturn(hashedPassword);
        when(passwordEncoder.matches(testPassword, hashedPassword)).thenReturn(true);

        try (MockedStatic<SecurityContextHolder> mockedSecurityContextHolder = mockStatic(
                SecurityContextHolder.class)) {
            mockedSecurityContextHolder.when(SecurityContextHolder::getContext)
                    .thenThrow(new RuntimeException("Security context error"));

            boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, testOrgPartyId);

            assertFalse(result);
            verify(passwordEncoder).matches(testPassword, hashedPassword);
        }
    }

    @Test
    void testAuthenticateSuperAdmin_PasswordEncoderException() {
        String hashedPassword = "$2a$10$hashedPasswordExample";

        when(dsl.select()).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.super_admin_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock);
        when(andMock.fetchOne()).thenReturn(recordMock);
        when(recordMock.get("password", String.class)).thenReturn(hashedPassword);
        when(passwordEncoder.matches(testPassword, hashedPassword))
                .thenThrow(new RuntimeException("Password encoder error"));

        boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, testOrgPartyId);

        assertFalse(result);
        verify(passwordEncoder).matches(testPassword, hashedPassword);
    }

    @Test
    void testAuthenticateSuperAdmin_NullPasswordFromDatabase() {
        when(dsl.select()).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.super_admin_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock);
        when(andMock.fetchOne()).thenReturn(recordMock);
        when(recordMock.get("password", String.class)).thenReturn(null);

        boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, testOrgPartyId);

        assertFalse(result);
    }

    @Test
    void testAuthenticateSuperAdmin_EmptyPasswordFromDatabase() {
        when(dsl.select()).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.super_admin_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.and(any(Condition.class))).thenReturn(andMock);
        when(andMock.fetchOne()).thenReturn(recordMock);
        when(recordMock.get("password", String.class)).thenReturn("");

        boolean result = superAdminService.authenticateSuperAdmin(testEmail, testPassword, testOrgPartyId);

        assertFalse(result);
        verify(passwordEncoder).matches(testPassword, "");
    }

    @Test
    void testAddSuperAdmin_Success() {
        JSONB expectedResult = JSONB
                .jsonb("{\"status\": \"success\", \"message\": \"Super Admin created successfully\"}");
        when(jsonbRecordMock.value1()).thenReturn(expectedResult);
        when(selectJsonbMock.fetchOne()).thenReturn(jsonbRecordMock);
        when(dsl.select(any(Field.class))).thenReturn(selectJsonbMock);

        Object result = superAdminService.addSuperAdmin(testFullName, testEmail);

        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    @Test
    void testAddSuperAdmin_Exception() {
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        Object result = superAdminService.addSuperAdmin(testFullName, testEmail);

        assertNotNull(result);
        assertTrue(result instanceof Response);
        Response response = (Response) result;
        assertEquals("error", response.getStatus());
        assertEquals("Failed to Add Super Admin", response.getMessage());
        assertTrue(response.getErrors().toString().contains("Database error"));
    }

    @Test
    void testAddSuperAdmin_NullParameters() {
        Object result1 = superAdminService.addSuperAdmin(null, testEmail);
        Object result2 = superAdminService.addSuperAdmin(testFullName, null);

        assertNotNull(result1);
        assertNotNull(result2);
    }

    @Test
    void testAddSuperAdmin_EmptyParameters() {
        Object result1 = superAdminService.addSuperAdmin("", testEmail);
        Object result2 = superAdminService.addSuperAdmin(testFullName, "");

        assertNotNull(result1);
        assertNotNull(result2);
    }
}
