package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Map;
import java.util.Optional;

import org.diabetestechnology.drh.service.http.GitHubUserAuthorizationFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.diabetestechnology.drh.service.http.pg.request.PractitionerRequest;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;

import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectLimitPercentStep;
import org.jooq.SelectSeekStep1;
import org.jooq.SelectSelectStep;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;

public class PractitionerServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private UserNameService userNameService;
    @Mock
    private DbActivityService activityLogService;
    @Mock
    private AuditService auditService;
    @Mock
    private AuthUserDetailsService authUserDetailsService;
    @Mock
    private PartyService partyService;
    @Mock
    private GitHubUserAuthorizationFilter gitHubUserAuthorizationFilter;

    @InjectMocks
    private PractitionerService practitionerService;

    private SelectSelectStep<Record1<JSONB>> selectMock;
    private SelectJoinStep<Record1<JSONB>> joinStepMock;
    private SelectConditionStep<Record1<JSONB>> conditionStepMock;

    @SuppressWarnings("unchecked")
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        selectMock = mock(SelectSelectStep.class);
        joinStepMock = mock(SelectJoinStep.class);
        conditionStepMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);

    }

    @SuppressWarnings("unchecked")
    @Test
    void testCreatePractitionerProfile_GitHub() throws Exception {
        PractitionerRequest request = mock(PractitionerRequest.class);
        when(request.name()).thenReturn("John Doe");
        when(request.email()).thenReturn(new String[] { "<EMAIL>" });
        when(request.organizationPartyId()).thenReturn("Org123");

        when(userNameService.getUserId()).thenReturn("user123");
        when(userNameService.getUserProvider()).thenReturn("GitHub");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(JSONB.valueOf("{\"status\": \"success\"}"));

        JSONB result = practitionerService.createPractitionerProfile(request);

        assertNotNull(result);
        assertEquals("{\"status\": \"success\"}", result.data());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testCreatePractitionerProfile_Orcid() throws Exception {
        PractitionerRequest request = mock(PractitionerRequest.class);
        when(request.name()).thenReturn("Jane Doe");
        when(request.email()).thenReturn(new String[] { "<EMAIL>" });
        when(request.organizationPartyId()).thenReturn("Org456");
        when(request.orcid()).thenReturn("0000-0002-1825-0097");

        when(userNameService.getUserId()).thenReturn("user456");
        when(userNameService.getUserProvider()).thenReturn("Orcid");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(JSONB.valueOf("{\"status\": \"success\"}"));

        JSONB result = practitionerService.createPractitionerProfile(request);

        assertNotNull(result);
        assertEquals("{\"status\": \"success\"}", result.data());
    }

    @Test
    void testCreatePractitionerProfile_InvalidProvider() throws Exception {
        PractitionerRequest request = mock(PractitionerRequest.class);
        when(userNameService.getUserProvider()).thenReturn("Unknown");

        JSONB result = practitionerService.createPractitionerProfile(request);

        assertNull(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetUserDetails_UserFound() {
        String userId = "testUser123";
        JSONB jsonbMock = JSONB.valueOf("{\"practitioner_id\": 123}");

        when(userNameService.getUserId()).thenReturn(userId);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinStepMock);
        when(joinStepMock.where(any(org.jooq.Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(jsonbMock);

        Object result = practitionerService.getUserDetails();

        assertEquals(jsonbMock.data(), result);
    }

    @Test
    void testGetUserDetails_AnonymousUser() {
        when(userNameService.getUserId()).thenReturn("Anonymous");

        Object result = practitionerService.getUserDetails();

        assertEquals("{}", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetUserDetails_NoUserFound() {
        when(userNameService.getUserId()).thenReturn("unknownUser");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);

        Object result = practitionerService.getUserDetails();

        assertEquals("{}", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetUserDetails_ExceptionHandling() {
        when(userNameService.getUserId()).thenReturn("validUserId");
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("DB error"));

        Object result = practitionerService.getUserDetails();

        assertEquals("{}", result);
    }

    @Test
    void testIsUserExists_Anonymous() {

        when(userNameService.getUserId()).thenReturn("Anonymous");
        Boolean result = practitionerService.isUserExists();
        assertEquals(false, result);
    }

    @Test
    void testIsUserExists_IsEmpty() {

        when(userNameService.getUserId()).thenReturn("");
        Boolean result = practitionerService.isUserExists();
        assertEquals(false, result);
    }

    @Test
    void testIsUserExists_null() {

        when(userNameService.getUserId()).thenReturn(null);
        Boolean result = practitionerService.isUserExists();
        assertEquals(false, result);
    }

    @Test
    void testGetUserOrganization_EmptyUserId() {

        when(userNameService.getUserId()).thenReturn("");
        String result = practitionerService.getUserOrganization();
        assertEquals("", result);
    }

    @Test
    void testGetUserOrganization_AnonymousUser() {

        when(userNameService.getUserId()).thenReturn("Anonymous");
        String result = practitionerService.getUserOrganization();
        assertEquals("", result);
    }

    @Test
    void testGetUserOrganization_NullUserId() {

        when(userNameService.getUserId()).thenReturn(null);
        String result = practitionerService.getUserOrganization();
        assertEquals("", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetUserOrganization_ExceptionThrown() {
        when(userNameService.getUserId()).thenReturn("validUserId");
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        String result = practitionerService.getUserOrganization();
        assertEquals("", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUserExists_ValidUser() {
        when(userNameService.getUserId()).thenReturn("valid-user-id");

        SelectSelectStep<Record1<Boolean>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Boolean>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Boolean>> whereStepMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<Boolean>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.value(true))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.user_profile_view")).thenReturn(fromStepMock);

        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOptional()).thenReturn(Optional.of(mock(Record1.class)));

        Boolean result = practitionerService.isUserExists();
        assertTrue(result);
    }

    @Test
    void testIsUserExists_ExceptionHandling() {

        when(userNameService.getUserId()).thenReturn("valid-user-id");
        when(dsl.select(DSL.value(true))).thenThrow(new RuntimeException("Database error"));

        Boolean result = practitionerService.isUserExists();
        assertNotNull(result);
        assertFalse(result);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Test
    void test_GetUserOrganization() {
        when(userNameService.getUserId()).thenReturn("valid-user-id");

        SelectSelectStep<Record1<?>> selectMock1 = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<?>> fromStepMock1 = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<?>> whereStepMock1 = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<?>> limitStepMock1 = mock(SelectLimitPercentStep.class);

        SelectSelectStep<Record1<?>> selectMock2 = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<?>> fromStepMock2 = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<?>> whereStepMock2 = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<?>> limitStepMock2 = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.field("organization_party_id"))).thenReturn((SelectSelectStep) selectMock1);
        when(selectMock1.from("drh_stateless_authentication.user_profile_view")).thenReturn(fromStepMock1);
        when(fromStepMock1.where(any(Condition.class))).thenReturn(whereStepMock1);
        when(whereStepMock1.limit(1)).thenReturn(limitStepMock1);
        when(limitStepMock1.fetchOneInto(String.class)).thenReturn("mockOrgId");

        when(dsl.select(DSL.field("organization_name"))).thenReturn((SelectSelectStep) selectMock2);
        when(selectMock2.from("drh_stateless_research_study.organization_party_view")).thenReturn(fromStepMock2);
        when(fromStepMock2.where(any(Condition.class))).thenReturn(whereStepMock2);
        when(whereStepMock2.limit(1)).thenReturn(limitStepMock2);
        when(limitStepMock2.fetchOneInto(String.class)).thenReturn("Mock Organization Name");

        String result = practitionerService.getUserOrganization();

        assertNotNull(result);
        assertEquals("Mock Organization Name", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetLoggedInUserDetails_success() {
        String userId = "user123";
        String expectedJson = "{\"practitioner_id\":\"user123\"}";

        JSONB mockJsonb = JSONB.valueOf(expectedJson);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionStepMock = mock(SelectConditionStep.class);

        when(auditService.getCurrentRequest()).thenReturn(null);
        when(userNameService.getUserId()).thenReturn(userId);
        when(authUserDetailsService.isSuperAdmin()).thenReturn(false);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinMock);
        when(joinMock.where(any(Condition.class))).thenReturn(conditionStepMock);
        when(conditionStepMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Object result = practitionerService.getLoggedInUserDetails();

        assertEquals(mockJsonb, result);
    }

    @Test
    void testGetLoggedInUserDetails_anonymousUser_returnsEmptyJson() {
        when(auditService.getCurrentRequest()).thenReturn(null);
        when(userNameService.getUserId()).thenReturn("Anonymous");

        Object result = practitionerService.getLoggedInUserDetails();

        assertEquals("{}", result.toString());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetLoggedInUserDetails_whenExceptionThrown_returnsEmptyJson() {
        when(auditService.getCurrentRequest()).thenReturn(null);
        when(userNameService.getUserId()).thenReturn("user123");
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("DB error"));

        Object result = practitionerService.getLoggedInUserDetails();

        assertEquals("{}", result.toString());
    }

    @SuppressWarnings("unchecked")
    @Test
    void testCheckEmailUnique_Success() throws Exception {
        String email = "<EMAIL>";
        String jsonResponse = "{\"status\":\"success\",\"is_unique\":true,\"email\":\"<EMAIL>\"}";
        JSONB mockJsonb = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Map<String, Object> result = practitionerService.checkEmailUnique(email);

        assertNotNull(result);
        assertEquals("success", result.get("status"));
        assertEquals(true, result.get("is_unique"));
        assertEquals("<EMAIL>", result.get("email"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testCheckEmailUnique_EmailNotUnique() throws Exception {
        String email = "<EMAIL>";
        String jsonResponse = "{\"status\":\"error\",\"message\":\"Email is not unique\",\"email\":\"<EMAIL>\"}";
        JSONB mockJsonb = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Map<String, Object> result = practitionerService.checkEmailUnique(email);

        assertNotNull(result);
        assertEquals("error", result.get("status"));
        assertEquals("Email is not unique", result.get("message"));
        assertEquals("<EMAIL>", result.get("email"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testCheckEmailUnique_NullResult() {
        String email = "<EMAIL>";

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(null);

        Map<String, Object> result = practitionerService.checkEmailUnique(email);

        assertNotNull(result);
        assertEquals("error", result.get("status"));
        assertEquals("Error while fetching data from Database",
                result.get("message"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testCheckEmailUnique_Exception() {
        String email = "<EMAIL>";

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        Map<String, Object> result = practitionerService.checkEmailUnique(email);

        assertNotNull(result);
        assertEquals("error", result.get("status"));
        assertEquals("Failed to check email uniqueness", result.get("message"));
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUniqueEmail_True() throws Exception {
        String email = "<EMAIL>";
        String jsonResponse = "{\"status\":\"success\",\"is_unique\":true}";
        JSONB mockJsonb = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Boolean result = practitionerService.isUniqueEmail(email);

        assertTrue(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUniqueEmail_False() throws Exception {
        String email = "<EMAIL>";
        String jsonResponse = "{\"status\":\"success\",\"is_unique\":false}";
        JSONB mockJsonb = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Boolean result = practitionerService.isUniqueEmail(email);

        assertFalse(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUniqueEmail_FailureStatus() throws Exception {
        String email = "<EMAIL>";
        String jsonResponse = "{\"status\":\"error\",\"message\":\"Database error\"}";
        JSONB mockJsonb = JSONB.valueOf(jsonResponse);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Boolean result = practitionerService.isUniqueEmail(email);

        assertFalse(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUniqueEmail_EmptyResult() {
        String email = "<EMAIL>";
        JSONB mockJsonb = JSONB.valueOf("");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        assertThrows(RuntimeException.class, () -> {
            practitionerService.isUniqueEmail(email);
        });
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUniqueEmail_Exception() {
        String email = "<EMAIL>";

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            practitionerService.isUniqueEmail(email);
        });
    }

    @Test
    void testGetAvatarUrl_DefaultOAuth2User() {
        DefaultOAuth2User mockUser = mock(DefaultOAuth2User.class);
        when(mockUser.getAttribute("avatar_url")).thenReturn("https://github.com/avatar.jpg");
        when(userNameService.getUserPrincipal()).thenReturn(mockUser);

        String result = practitionerService.getAvatarUrl();

        assertEquals("https://github.com/avatar.jpg", result);
    }

    @Test
    void testGetAvatarUrl_DefaultOAuth2User_NullAvatar() {
        DefaultOAuth2User mockUser = mock(DefaultOAuth2User.class);
        when(mockUser.getAttribute("avatar_url")).thenReturn(null);
        when(userNameService.getUserPrincipal()).thenReturn(mockUser);

        String result = practitionerService.getAvatarUrl();

        assertEquals("/user.jpg", result);
    }

    @Test
    void testGetAvatarUrl_NotDefaultOAuth2User() {
        UserDetails mockUser = mock(UserDetails.class);
        when(userNameService.getUserPrincipal()).thenReturn(mockUser);

        String result = practitionerService.getAvatarUrl();

        assertEquals("/user.jpg", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testUpdateProfileDetails_Success() throws Exception {
        String userId = "user123";
        String partyId = "party123";
        JSONB inputJson = JSONB.valueOf("{\"name\":\"John Doe\"}");
        JSONB expectedResult = JSONB.valueOf("{\"status\":\"success\"}");

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(partyId);
        when(activityLogService.prepareActivityLogMetadata()).thenReturn("{}");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        Object result = practitionerService.updateProfileDetails(inputJson);

        assertEquals(expectedResult, result);
    }

    @Test
    void testUpdateProfileDetails_Exception() {
        String userId = "user123";
        JSONB inputJson = JSONB.valueOf("{\"name\":\"John Doe\"}");

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            practitionerService.updateProfileDetails(inputJson);
        });
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetProfileDetails_Success() {
        String userId = "user123";
        String partyId = "party123";
        String jsonResponse = "{\"party_id\":\"party123\",\"name\":\"John Doe\"}";
        JSONB mockJsonb = JSONB.valueOf(jsonResponse);

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(partyId);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinMock);
        when(joinMock.where(any(Condition.class))).thenReturn(conditionMock);
        when(conditionMock.fetchOneInto(JSONB.class)).thenReturn(mockJsonb);

        Object result = practitionerService.getProfileDetails();

        assertEquals(jsonResponse, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetProfileDetails_NullResult() {
        String userId = "user123";
        String partyId = "party123";

        when(userNameService.getUserId()).thenReturn(userId);
        when(partyService.getPartyIdByUserId(userId)).thenReturn(partyId);

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<JSONB>> joinMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<JSONB>> conditionMock = mock(SelectConditionStep.class);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from(anyString())).thenReturn(joinMock);
        when(joinMock.where(any(Condition.class))).thenReturn(conditionMock);
        when(conditionMock.fetchOneInto(JSONB.class)).thenReturn(null);

        Object result = practitionerService.getProfileDetails();

        assertEquals("{}", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUserEmailExists_True() {
        String email = "<EMAIL>";

        SelectSelectStep<Record1<Boolean>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Boolean>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Boolean>> whereStepMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<Boolean>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.value(true))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.profile_details_view")).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOptional()).thenReturn(Optional.of(mock(Record1.class)));

        Boolean result = practitionerService.isUserEmailExists(email);

        assertTrue(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUserEmailExists_False() {
        String email = "<EMAIL>";

        SelectSelectStep<Record1<Boolean>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Boolean>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Boolean>> whereStepMock = mock(SelectConditionStep.class);
        SelectLimitPercentStep<Record1<Boolean>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.value(true))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.profile_details_view")).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOptional()).thenReturn(Optional.empty());

        Boolean result = practitionerService.isUserEmailExists(email);

        assertFalse(result);
    }

    @Test
    void testIsUserEmailExists_Exception() {
        String email = "<EMAIL>";

        when(dsl.select(DSL.value(true))).thenThrow(new RuntimeException("Database error"));

        Boolean result = practitionerService.isUserEmailExists(email);

        assertFalse(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetUserNameByEmail_Success() {
        String email = "<EMAIL>";
        String expectedName = "John Doe";

        SelectSelectStep<Record1<Object>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Object>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Object>> whereStepMock = mock(SelectConditionStep.class);

        when(dsl.select(DSL.field("name"))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.profile_details_view")).thenReturn(fromStepMock);
        when(fromStepMock.where(any(Condition.class))).thenReturn(whereStepMock);
        when(whereStepMock.fetchOneInto(String.class)).thenReturn(expectedName);

        Object result = practitionerService.getUserNameByEmail(email);

        assertEquals(expectedName, result);
    }

    @Test
    void testGetUserNameByEmail_Exception() {
        String email = "<EMAIL>";

        when(dsl.select(DSL.field("name"))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            practitionerService.getUserNameByEmail(email);
        });
    }

    @SuppressWarnings("unchecked")
    @Test
    void testLinkExternalAuthProvider_Success() throws Exception {
        String email = "<EMAIL>";
        String provider = "GitHub";
        String providerId = "github123";
        JSONB expectedResult = JSONB.valueOf("{\"status\":\"success\"}");

        SelectSelectStep<Record1<JSONB>> selectMock = mock(SelectSelectStep.class);
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB result = practitionerService.linkExternalAuthProvider(email, provider,
                providerId);

        assertEquals(expectedResult, result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testLinkExternalAuthProvider_Exception() {
        String email = "<EMAIL>";
        String provider = "GitHub";
        String providerId = "github123";

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        assertThrows(RuntimeException.class, () -> {
            practitionerService.linkExternalAuthProvider(email, provider, providerId);
        });
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetEmailByUserId_Success() {
        String userId = "user123";
        String verificationStatus = "verified";
        String expectedEmail = "<EMAIL>";

        SelectSelectStep<Record1<Object>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Object>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Object>> whereStepMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<Object>> andStepMock = mock(SelectConditionStep.class);
        SelectSeekStep1<Record1<Object>, Object> orderByStepMock = mock(SelectSeekStep1.class);
        SelectLimitPercentStep<Record1<Object>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.field("email"))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.user_account_verification_log_view"))
                .thenReturn(fromStepMock);
        when(fromStepMock.where(DSL.field("provider_id").eq(DSL.val(userId)))).thenReturn(whereStepMock);
        when(whereStepMock.and(DSL.field("verification_status_id").eq(DSL.val(verificationStatus))))
                .thenReturn(andStepMock);
        when(andStepMock.orderBy(DSL.field("updated_at").desc())).thenReturn(orderByStepMock);
        when(orderByStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOneInto(String.class)).thenReturn(expectedEmail);

        String result = practitionerService.getEmailByUserId(userId,
                verificationStatus);

        assertEquals(expectedEmail, result);
    }

    @Test
    void testGetEmailByUserId_Exception() {
        String userId = "user123";
        String verificationStatus = "verified";

        when(dsl.select(DSL.field("email"))).thenThrow(new RuntimeException("Database error"));

        String result = practitionerService.getEmailByUserId(userId,
                verificationStatus);

        assertNull(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUserVerified_True() {
        String userId = "user123";
        String verificationStatus = "verified";

        SelectSelectStep<Record1<Boolean>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Boolean>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Boolean>> whereStepMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<Boolean>> andStepMock = mock(SelectConditionStep.class);
        SelectSeekStep1<Record1<Boolean>, Object> orderByStepMock = mock(SelectSeekStep1.class);
        SelectLimitPercentStep<Record1<Boolean>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.value(true))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.user_account_verification_log_view"))
                .thenReturn(fromStepMock);
        when(fromStepMock.where(DSL.field("provider_id").eq(DSL.val(userId)))).thenReturn(whereStepMock);
        when(whereStepMock.and(DSL.field("verification_status_id").eq(DSL.val(verificationStatus))))
                .thenReturn(andStepMock);
        when(andStepMock.orderBy(DSL.field("updated_at").desc())).thenReturn(orderByStepMock);
        when(orderByStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOptional()).thenReturn(Optional.of(mock(Record1.class)));

        Boolean result = practitionerService.isUserVerified(userId,
                verificationStatus);

        assertTrue(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testIsUserVerified_False() {
        String userId = "user123";
        String verificationStatus = "verified";

        SelectSelectStep<Record1<Boolean>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Boolean>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Boolean>> whereStepMock = mock(SelectConditionStep.class);
        SelectConditionStep<Record1<Boolean>> andStepMock = mock(SelectConditionStep.class);
        SelectSeekStep1<Record1<Boolean>, Object> orderByStepMock = mock(SelectSeekStep1.class);
        SelectLimitPercentStep<Record1<Boolean>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.value(true))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.user_account_verification_log_view"))
                .thenReturn(fromStepMock);
        when(fromStepMock.where(DSL.field("provider_id").eq(DSL.val(userId)))).thenReturn(whereStepMock);
        when(whereStepMock.and(DSL.field("verification_status_id").eq(DSL.val(verificationStatus))))
                .thenReturn(andStepMock);
        when(andStepMock.orderBy(DSL.field("updated_at").desc())).thenReturn(orderByStepMock);
        when(orderByStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOptional()).thenReturn(Optional.empty());

        Boolean result = practitionerService.isUserVerified(userId,
                verificationStatus);

        assertFalse(result);
    }

    @Test
    void testIsUserVerified_Exception() {
        String userId = "user123";
        String verificationStatus = "verified";

        when(dsl.select(DSL.value(true))).thenThrow(new RuntimeException("Database error"));

        Boolean result = practitionerService.isUserVerified(userId,
                verificationStatus);

        assertFalse(result);
    }

    @SuppressWarnings("unchecked")
    @Test
    void testGetExistingProvider_Success() {
        String email = "<EMAIL>";
        String expectedProvider = "GitHub";

        SelectSelectStep<Record1<Object>> selectMock = mock(SelectSelectStep.class);
        SelectJoinStep<Record1<Object>> fromStepMock = mock(SelectJoinStep.class);
        SelectConditionStep<Record1<Object>> whereStepMock = mock(SelectConditionStep.class);
        SelectSeekStep1<Record1<Object>, Object> orderByStepMock = mock(SelectSeekStep1.class);
        SelectLimitPercentStep<Record1<Object>> limitStepMock = mock(SelectLimitPercentStep.class);

        when(dsl.select(DSL.field("auth_provider"))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.user_profile_view")).thenReturn(fromStepMock);
        when(fromStepMock.where(DSL.field("user_account_primary_email").eq(DSL.val(email)))).thenReturn(whereStepMock);
        when(whereStepMock.orderBy(DSL.field("created_at").desc())).thenReturn(orderByStepMock);
        when(orderByStepMock.limit(1)).thenReturn(limitStepMock);
        when(limitStepMock.fetchOneInto(String.class)).thenReturn(expectedProvider);

        String result = practitionerService.getExistingProvider(email);

        assertEquals(expectedProvider, result);
    }

    @Test
    void testGetExistingProvider_Exception() {
        String email = "<EMAIL>";

        when(dsl.select(DSL.field("auth_provider"))).thenThrow(new RuntimeException("Database error"));

        String result = practitionerService.getExistingProvider(email);

        assertNull(result);
    }

}
