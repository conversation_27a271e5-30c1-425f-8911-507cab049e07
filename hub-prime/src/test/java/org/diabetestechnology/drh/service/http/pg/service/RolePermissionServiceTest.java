package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class RolePermissionServiceTest {

    @Mock
    private UserRoleService userRoleService;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;

    @InjectMocks
    private RolePermissionService rolePermissionService;

    private static final String TEST_USER_ID = "testUser123";
    private static final String TEST_PARTY_ID = "party456";
    private static final String TEST_RESOURCE = "ADMINISTRATION";
    private static final String TEST_MENU_NAME = "User Management";

    @BeforeEach
    void setUp() {
        // Common setup for mocks
    }

    // Tests for getPermissionsForUser method
    @Test
    void testGetPermissionsForUser_Success() {
        // Arrange
        List<String> expectedPermissions = List.of("ADMIN:VIEW", "USER:EDIT", "REPORT:VIEW");

        when(userNameService.getUserId()).thenReturn(TEST_USER_ID);
        when(partyService.getPartyIdByUserId(TEST_USER_ID)).thenReturn(TEST_PARTY_ID);
        when(userRoleService.getFlatPermissionListByRoles(TEST_PARTY_ID)).thenReturn(expectedPermissions);

        // Act
        List<String> result = rolePermissionService.getPermissionsForUser();

        // Assert
        assertNotNull(result);
        assertEquals(expectedPermissions, result);
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId(TEST_USER_ID);
        verify(userRoleService).getFlatPermissionListByRoles(TEST_PARTY_ID);
    }

    @Test
    void testGetPermissionsForUser_Exception_ReturnsEmptyList() {
        // Arrange
        when(userNameService.getUserId()).thenThrow(new RuntimeException("User service error"));

        // Act
        List<String> result = rolePermissionService.getPermissionsForUser();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(userNameService).getUserId();
    }

    // Tests for checkPermissionByResource method
    @Test
    void testCheckPermissionByResource_HasPermission_ReturnsTrue() throws Throwable {
        // Arrange
        List<String> userRoles = List.of("ADMIN", "USER");

        when(userNameService.getUserId()).thenReturn(TEST_USER_ID);
        when(partyService.getPartyIdByUserId(TEST_USER_ID)).thenReturn(TEST_PARTY_ID);
        when(userRoleService.getUserRolesByPartyId(TEST_PARTY_ID)).thenReturn(userRoles);
        when(userRoleService.checkPermissionListByRolesAndResource(userRoles, TEST_RESOURCE)).thenReturn(true);

        // Act
        Boolean result = rolePermissionService.checkPermissionByResource(TEST_RESOURCE);

        // Assert
        assertTrue(result);
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId(TEST_USER_ID);
        verify(userRoleService).getUserRolesByPartyId(TEST_PARTY_ID);
        verify(userRoleService).checkPermissionListByRolesAndResource(userRoles, TEST_RESOURCE);
    }

    @Test
    void testCheckPermissionByResource_NoPermission_ReturnsFalse() throws Throwable {
        // Arrange
        List<String> userRoles = List.of("USER");

        when(userNameService.getUserId()).thenReturn(TEST_USER_ID);
        when(partyService.getPartyIdByUserId(TEST_USER_ID)).thenReturn(TEST_PARTY_ID);
        when(userRoleService.getUserRolesByPartyId(TEST_PARTY_ID)).thenReturn(userRoles);
        when(userRoleService.checkPermissionListByRolesAndResource(userRoles, TEST_RESOURCE)).thenReturn(false);

        // Act
        Boolean result = rolePermissionService.checkPermissionByResource(TEST_RESOURCE);

        // Assert
        assertFalse(result);
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId(TEST_USER_ID);
        verify(userRoleService).getUserRolesByPartyId(TEST_PARTY_ID);
        verify(userRoleService).checkPermissionListByRolesAndResource(userRoles, TEST_RESOURCE);
    }

    // Tests for checkPermissionByMenuName method
    @Test
    void testCheckPermissionByMenuName_HasPermission_ReturnsTrue() throws Throwable {
        // Arrange
        List<String> userRoles = List.of("ADMIN", "USER");

        when(userNameService.getUserId()).thenReturn(TEST_USER_ID);
        when(partyService.getPartyIdByUserId(TEST_USER_ID)).thenReturn(TEST_PARTY_ID);
        when(userRoleService.getUserRolesByPartyId(TEST_PARTY_ID)).thenReturn(userRoles);
        when(userRoleService.checkPermissionListByRolesAndMenuName(userRoles, TEST_MENU_NAME)).thenReturn(true);

        // Act
        Boolean result = rolePermissionService.checkPermissionByMenuName(TEST_MENU_NAME);

        // Assert
        assertTrue(result);
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId(TEST_USER_ID);
        verify(userRoleService).getUserRolesByPartyId(TEST_PARTY_ID);
        verify(userRoleService).checkPermissionListByRolesAndMenuName(userRoles, TEST_MENU_NAME);
    }

    @Test
    void testCheckPermissionByMenuName_NoPermission_ReturnsFalse() throws Throwable {
        // Arrange
        List<String> userRoles = List.of("USER");

        when(userNameService.getUserId()).thenReturn(TEST_USER_ID);
        when(partyService.getPartyIdByUserId(TEST_USER_ID)).thenReturn(TEST_PARTY_ID);
        when(userRoleService.getUserRolesByPartyId(TEST_PARTY_ID)).thenReturn(userRoles);
        when(userRoleService.checkPermissionListByRolesAndMenuName(userRoles, TEST_MENU_NAME)).thenReturn(false);

        // Act
        Boolean result = rolePermissionService.checkPermissionByMenuName(TEST_MENU_NAME);

        // Assert
        assertFalse(result);
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId(TEST_USER_ID);
        verify(userRoleService).getUserRolesByPartyId(TEST_PARTY_ID);
        verify(userRoleService).checkPermissionListByRolesAndMenuName(userRoles, TEST_MENU_NAME);
    }

    @Test
    void testCheckPermissionByMenuName_Exception_PropagatesThrowable() throws Throwable {
        // Arrange
        when(userNameService.getUserId()).thenThrow(new RuntimeException("Service error"));

        // Act & Assert
        try {
            rolePermissionService.checkPermissionByMenuName(TEST_MENU_NAME);
        } catch (RuntimeException e) {
            assertEquals("Service error", e.getMessage());
        }

        verify(userNameService).getUserId();
    }

    @Test
    void testCheckPermissionByResource_Exception_PropagatesThrowable() throws Throwable {
        // Arrange
        when(userNameService.getUserId()).thenThrow(new RuntimeException("Service error"));

        // Act & Assert
        try {
            rolePermissionService.checkPermissionByResource(TEST_RESOURCE);
        } catch (RuntimeException e) {
            assertEquals("Service error", e.getMessage());
        }

        verify(userNameService).getUserId();
    }

}
