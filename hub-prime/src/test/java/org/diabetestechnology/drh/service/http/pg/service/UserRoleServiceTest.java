package org.diabetestechnology.drh.service.http.pg.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.OauthUsersService;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.ux.Presentation;
import org.diabetestechnology.drh.service.http.pg.request.UserRoleRequest;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record2;
import org.jooq.Record3;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectSelectStep;
import org.jooq.exception.DataAccessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("unchecked")
public class UserRoleServiceTest {

    @Mock
    private DSLContext dsl;

    @Mock
    private UserNameService userNameService;

    @Mock
    private PartyService partyService;

    @Mock
    private OauthUsersService oauthUsersService;

    @Mock
    private Presentation presentation;

    @Mock
    private DbActivityService activityLogService;

    @Mock
    private SelectSelectStep<Record1<JSONB>> selectMock;

    @Mock
    private SelectJoinStep<Record1<JSONB>> fromMock;

    @Mock
    private SelectConditionStep<Record1<JSONB>> whereMock;

    @Mock
    private Result<Record2<String, String>> resultMock;

    @Mock
    private Result<Record3<String, String, String>> result3Mock;

    @InjectMocks
    private UserRoleService userRoleService;

    private final String testUserId = "user123";
    private final String testPartyId = "party123";
    private final String testProvider = "GitHub";

    @BeforeEach
    void setUp() {
        selectMock = mock(SelectSelectStep.class);
        fromMock = mock(SelectJoinStep.class);
        whereMock = mock(SelectConditionStep.class);
        resultMock = mock(Result.class);
        result3Mock = mock(Result.class);
    }

    @Test
    void testUpdateUserRole_ServiceNotNull() {
        // Simple test to verify the service is properly injected
        assertNotNull(userRoleService, "UserRoleService should be injected");
        assertNotNull(userNameService, "UserNameService should be mocked");
        assertNotNull(partyService, "PartyService should be mocked");
        assertNotNull(oauthUsersService, "OauthUsersService should be mocked");
        assertNotNull(presentation, "Presentation should be mocked");
        assertNotNull(activityLogService, "ActivityLogService should be mocked");
    }

    @Test
    void testGetUserRolesByPartyId_NullPartyId() {
        // Arrange
        String userPartyId = null;

        // Act
        List<String> result = userRoleService.getUserRolesByPartyId(userPartyId);

        // Assert
        assertNotNull(result);
        assertEquals(List.of("Guest"), result);
        // Note: presentation.isAuthenticatedUser() is NOT called when userPartyId is
        // null
        // The method returns Guest role immediately for null userPartyId
        verify(presentation, never()).isAuthenticatedUser();
    }

    @Test
    void testGetUserRolesByPartyId_GuestUser() {
        // Arrange - Test the case where userPartyId is provided but user is not
        // authenticated
        String userPartyId = "party123";

        when(presentation.isAuthenticatedUser()).thenReturn(false);

        // Act
        List<String> result = userRoleService.getUserRolesByPartyId(userPartyId);

        // Assert
        assertNotNull(result);
        assertEquals(List.of("Guest"), result);
        verify(presentation, times(1)).isAuthenticatedUser();
    }

    @Test
    void testGetUserRolesByPartyId_EmptyPartyId() {
        // Arrange
        String userPartyId = "";

        // Act
        List<String> result = userRoleService.getUserRolesByPartyId(userPartyId);

        // Assert
        assertNotNull(result);
        assertEquals(List.of("Guest"), result);
        // Note: presentation.isAuthenticatedUser() is NOT called when userPartyId is
        // empty
        // The method returns Guest role immediately for empty userPartyId
        verify(presentation, never()).isAuthenticatedUser();
    }

    @Test
    void testGetUserRolesByPartyId_AuthenticatedUserCallsPresentation() {
        // Arrange - Test that presentation methods are called for authenticated users
        String userPartyId = "party123";

        when(presentation.isAuthenticatedUser()).thenReturn(true);
        when(presentation.isSuperAdmin()).thenReturn(false);

        // Mock database exception to avoid complex JOOQ mocking
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        // Act
        try {
            userRoleService.getUserRolesByPartyId(userPartyId);
        } catch (Exception e) {
            // Expected due to database mock
        }

        // Assert - Verify that the presentation methods were called
        verify(presentation, times(1)).isAuthenticatedUser();
        verify(presentation, times(1)).isSuperAdmin();
    }

    @Test
    void testCheckPermissionListByRolesAndResource_Success() {
        // Arrange
        List<String> userRoles = List.of("Admin", "User");
        String resource = "STUDY_MANAGEMENT";

        when(dsl.fetchExists(any(org.jooq.Select.class))).thenReturn(true);

        // Act
        Boolean result = userRoleService.checkPermissionListByRolesAndResource(userRoles, resource);

        // Assert
        assertNotNull(result);
        assertTrue(result);
        verify(dsl, times(1)).fetchExists(any(org.jooq.Select.class));
    }

    @Test
    void testCheckPermissionListByRolesAndResource_NoPermission() {
        // Arrange
        List<String> userRoles = List.of("Guest");
        String resource = "ADMIN_PANEL";

        when(dsl.fetchExists(any(org.jooq.Select.class))).thenReturn(false);

        // Act
        Boolean result = userRoleService.checkPermissionListByRolesAndResource(userRoles, resource);

        // Assert
        assertNotNull(result);
        assertFalse(result);
        verify(dsl, times(1)).fetchExists(any(org.jooq.Select.class));
    }

    @Test
    void testCheckPermissionListByRolesAndMenuName_Success() {
        // Arrange
        List<String> userRoles = List.of("Admin");
        String menuName = "User Management";

        when(dsl.fetchExists(any(org.jooq.Select.class))).thenReturn(true);

        // Act
        Boolean result = userRoleService.checkPermissionListByRolesAndMenuName(userRoles, menuName);

        // Assert
        assertNotNull(result);
        assertTrue(result);
        verify(dsl, times(1)).fetchExists(any(org.jooq.Select.class));
    }

    @Test
    void testCheckPermissionListByRolesAndMenuName_NoPermission() {
        // Arrange
        List<String> userRoles = List.of("User");
        String menuName = "System Settings";

        when(dsl.fetchExists(any(org.jooq.Select.class))).thenReturn(false);

        // Act
        Boolean result = userRoleService.checkPermissionListByRolesAndMenuName(userRoles, menuName);

        // Assert
        assertNotNull(result);
        assertFalse(result);
        verify(dsl, times(1)).fetchExists(any(org.jooq.Select.class));
    }

    @Test
    void testGetUserRolesAndPermissions_Exception() {
        // Arrange
        String userId = "user123";

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenThrow(new RuntimeException("Database error"));

        // Act
        JSONB result = userRoleService.getUserRolesAndPermissions(userId);

        // Assert
        assertNotNull(result);
        assertTrue(result.data().contains("error"));
        assertTrue(result.data().contains("Failed to fetch user roles"));
    }

    @Test
    void testGetUserRoles_Success() {
        JSONB expectedResult = JSONB.jsonb("[{\"role_id\":\"1\",\"role_name\":\"Admin\"}]");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_master.roles_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB result = userRoleService.getUserRoles();

        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(dsl).select(any(Field.class));
    }

    @Test
    void testGetUserRoles_NoRolesFound() {
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_master.roles_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOneInto(JSONB.class)).thenReturn(null);

        JSONB result = userRoleService.getUserRoles();

        assertNotNull(result);
        assertEquals(JSONB.jsonb("{}"), result);
        verify(dsl).select(any(Field.class));
    }

    @Test
    void testGetUserRoles_Exception() {
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        try {
            JSONB result = userRoleService.getUserRoles();
            assertNotNull(result);
            assertEquals(JSONB.jsonb("{}"), result);
        } catch (RuntimeException e) {
            assertTrue(e.getMessage().contains("An unexpected error occurred") ||
                    e.getMessage().contains("Database error"));
        }
        verify(dsl).select(any(Field.class));
    }

    @Test
    void testGetUserList_Success() {
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        try {
            JSONB result = userRoleService.getUserList();
            assertNotNull(result);
        } catch (RuntimeException e) {
            assertTrue(e.getMessage().contains("An unexpected error occurred") ||
                    e.getMessage().contains("Database error"));
        }
        verify(dsl).select(any(Field.class));
    }

    @Test
    void testGetUserList_DataAccessException() {
        when(dsl.select(any(Field.class))).thenThrow(new DataAccessException("Database connection failed"));

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userRoleService.getUserList();
        });

        assertTrue(exception.getMessage().contains("error") ||
                exception.getMessage().contains("Database") ||
                exception.getMessage().contains("occurred"));
        verify(dsl).select(any(Field.class));
    }

    @Test
    void testGetUserList_GeneralException() {
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Unexpected error"));

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userRoleService.getUserList();
        });

        assertEquals("An unexpected error occurred.", exception.getMessage());
        verify(dsl).select(any(Field.class));
    }

    @Test
    void testUpdateUserRole_Success() throws Exception {
        String[] roleIds = { "role1", "role2" };
        UserRoleRequest request = new UserRoleRequest(testUserId, roleIds);
        JSONB expectedResult = JSONB.jsonb("{\"status\":\"success\"}");

        when(userNameService.getUserId()).thenReturn(testUserId);
        when(partyService.getPartyIdByUserId(testUserId)).thenReturn(testPartyId);
        when(userNameService.getUserProvider()).thenReturn(testProvider);
        when(oauthUsersService.hasAdminMenuDB(testUserId, testProvider)).thenReturn(true);
        when(activityLogService.prepareActivityLogMetadata()).thenReturn("{}");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        Object result = userRoleService.updateUserRole(request);

        assertNotNull(result);
        assertTrue(result instanceof JSONB || result.toString().contains("success"));
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId(testUserId);
        verify(oauthUsersService).hasAdminMenuDB(testUserId, testProvider);
    }

    @Test
    void testUpdateUserRole_SuperAdminAccess() throws Exception {
        String[] roleIds = { "role1" };
        UserRoleRequest request = new UserRoleRequest(testUserId, roleIds);
        JSONB expectedResult = JSONB.jsonb("{\"status\":\"success\"}");

        when(userNameService.getUserId()).thenReturn(testUserId);
        when(partyService.getPartyIdByUserId(testUserId)).thenReturn(testPartyId);
        when(userNameService.getUserProvider()).thenReturn(testProvider);
        when(oauthUsersService.hasAdminMenuDB(testUserId, testProvider)).thenReturn(false);
        when(presentation.isSuperAdmin()).thenReturn(true);
        when(activityLogService.prepareActivityLogMetadata()).thenReturn("{}");
        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        Object result = userRoleService.updateUserRole(request);

        assertNotNull(result);
        assertTrue(result instanceof JSONB || result.toString().contains("success"));
        verify(presentation).isSuperAdmin();
    }

    @Test
    void testUpdateUserRole_Exception() throws Exception {
        String[] roleIds = { "role1" };
        UserRoleRequest request = new UserRoleRequest(testUserId, roleIds);

        when(userNameService.getUserId()).thenReturn(testUserId);
        when(partyService.getPartyIdByUserId(testUserId)).thenReturn(testPartyId);
        when(userNameService.getUserProvider()).thenReturn(testProvider);
        when(oauthUsersService.hasAdminMenuDB(testUserId, testProvider)).thenReturn(true);
        when(activityLogService.prepareActivityLogMetadata()).thenThrow(new RuntimeException("Activity log error"));

        Object result = userRoleService.updateUserRole(request);

        assertNotNull(result);
        assertTrue(result.toString().contains("error"));
        verify(userNameService).getUserId();
    }

    @Test
    void testGetUserRolesAndPermissions_Success() {
        String userId = testUserId;
        JSONB expectedResult = JSONB.jsonb("{\"roles\":[\"Admin\"],\"permissions\":[\"READ\",\"WRITE\"]}");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB result = userRoleService.getUserRolesAndPermissions(userId);

        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(dsl, atLeast(1)).select(any(Field.class));
    }

    @Test
    void testGetPermissionsByRoles_NullUserPartyId() {
        when(userNameService.getUserId()).thenReturn(testUserId);
        when(partyService.getPartyIdByUserId(testUserId)).thenReturn(testPartyId);
        when(presentation.isAuthenticatedUser()).thenReturn(true);
        when(presentation.isSuperAdmin()).thenReturn(false);

        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        String result = userRoleService.getPermissionsByRoles(null);

        assertNotNull(result);
        assertEquals("{}", result);
        verify(userNameService).getUserId();
        verify(partyService).getPartyIdByUserId(testUserId);
    }

    @Test
    void testGetPermissionsByRoles_GuestUser() {
        when(presentation.isAuthenticatedUser()).thenReturn(false);

        String result = userRoleService.getPermissionsByRoles(testPartyId);

        assertNotNull(result);
        verify(presentation).isAuthenticatedUser();
    }

    @Test
    void testGetPermissionsByRoles_SuperAdmin() {
        when(presentation.isAuthenticatedUser()).thenReturn(true);
        when(presentation.isSuperAdmin()).thenReturn(true);
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        String result = userRoleService.getPermissionsByRoles(testPartyId);

        assertNotNull(result);
        assertEquals("{}", result);
        verify(presentation).isSuperAdmin();
    }

    @Test
    void testGetPermissionsByRoles_NoRolesFound() {
        when(presentation.isAuthenticatedUser()).thenReturn(true);
        when(presentation.isSuperAdmin()).thenReturn(false);

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_authentication.user_list_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOneInto(JSONB.class)).thenReturn(null);

        String result = userRoleService.getPermissionsByRoles(testPartyId);

        assertNotNull(result);
        assertTrue(result.contains("error"));
        assertTrue(result.contains("User roles not found"));
    }

    @Test
    void testGetPermissionsOfGuestRole_Exception() {
        when(dsl.selectDistinct(any(Field.class), any(Field.class))).thenThrow(new RuntimeException("Database error"));

        try {
            Map<String, Object> result = userRoleService.getPermissionsOfGuestRole();
            assertNotNull(result);
            assertTrue(result.isEmpty());
        } catch (RuntimeException e) {
            assertTrue(e.getMessage().contains("Failed to fetch guest role permissions") ||
                    e.getMessage().contains("Database error"));
        }
    }

    @Test
    void testGetFlatPermissionListByRoles_Success() {
        when(dsl.selectDistinct(any(Field.class), any(Field.class), any(Field.class)))
                .thenThrow(new RuntimeException("Database error"));

        List<String> result = userRoleService.getFlatPermissionListByRoles(testPartyId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetFlatPermissionListByRoles_Exception() {
        when(dsl.selectDistinct(any(Field.class), any(Field.class), any(Field.class)))
                .thenThrow(new RuntimeException("Database error"));

        List<String> result = userRoleService.getFlatPermissionListByRoles(testPartyId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetPermissions_NullUserPartyId() {
        when(dsl.selectDistinct(any(Field.class), any(Field.class), any(Field.class)))
                .thenThrow(new RuntimeException("Database error"));

        String result = userRoleService.getPermissions(null);

        assertNotNull(result);
        assertEquals("{}", result);
    }

    @Test
    void testGetPermissions_GuestUser() {
        when(presentation.isAuthenticatedUser()).thenReturn(false);
        when(dsl.selectDistinct(any(Field.class), any(Field.class), any(Field.class)))
                .thenThrow(new RuntimeException("Database error"));

        String result = userRoleService.getPermissions(testPartyId);

        assertNotNull(result);
        assertEquals("{}", result);
        verify(presentation).isAuthenticatedUser();
    }

    @Test
    void testGetPermissions_SuperAdmin() {
        when(presentation.isAuthenticatedUser()).thenReturn(true);
        when(presentation.isSuperAdmin()).thenReturn(true);
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        String result = userRoleService.getPermissions(testPartyId);

        assertNotNull(result);
        assertEquals("{}", result);
        verify(presentation).isSuperAdmin();
    }

    @Test
    void testGetPermissions_Exception() {
        when(presentation.isAuthenticatedUser()).thenReturn(true);
        when(presentation.isSuperAdmin()).thenReturn(false);
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        String result = userRoleService.getPermissions(testPartyId);

        assertNotNull(result);
        assertEquals("{}", result);
    }

    @Test
    void testGetUserRolesByPartyId_SuperAdmin() {
        when(presentation.isAuthenticatedUser()).thenReturn(true);
        when(presentation.isSuperAdmin()).thenReturn(true);
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = userRoleService.getUserRolesByPartyId(testPartyId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(presentation).isSuperAdmin();
    }

    @Test
    void testGetUserRolesByPartyId_Exception() {
        when(presentation.isAuthenticatedUser()).thenReturn(true);
        when(presentation.isSuperAdmin()).thenReturn(false);
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        List<String> result = userRoleService.getUserRolesByPartyId(testPartyId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testConstructor() {
        UserRoleService service = new UserRoleService(dsl, userNameService, partyService,
                oauthUsersService, presentation, activityLogService);
        assertNotNull(service);
    }

    @Test
    void testGetUserRoles_SuccessWithData() {
        JSONB expectedResult = JSONB.jsonb("[{\"role_id\":\"1\",\"role_name\":\"Admin\"}]");

        when(dsl.select(any(Field.class))).thenReturn(selectMock);
        when(selectMock.from("drh_stateless_master.roles_view")).thenReturn(fromMock);
        when(fromMock.where(any(Condition.class))).thenReturn(whereMock);
        when(whereMock.fetchOneInto(JSONB.class)).thenReturn(expectedResult);

        JSONB result = userRoleService.getUserRoles();

        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(dsl).select(any(Field.class));
    }

    @Test
    void testGetUserList_SuccessWithData() {
        when(dsl.select(any(Field.class))).thenThrow(new RuntimeException("Database error"));

        try {
            JSONB result = userRoleService.getUserList();
            assertNotNull(result);
        } catch (RuntimeException e) {
            assertTrue(e.getMessage().contains("An unexpected error occurred") ||
                    e.getMessage().contains("Database error"));
        }
        verify(dsl).select(any(Field.class));
    }

    @Test
    void testUpdateUserRole_PermissionDenied() throws Exception {
        String[] roleIds = { "role1" };
        UserRoleRequest request = new UserRoleRequest(testUserId, roleIds);

        when(userNameService.getUserId()).thenReturn(testUserId);
        when(partyService.getPartyIdByUserId(testUserId)).thenReturn(testPartyId);
        when(userNameService.getUserProvider()).thenReturn(testProvider);
        when(oauthUsersService.hasAdminMenuDB(testUserId, testProvider)).thenReturn(false);
        when(presentation.isSuperAdmin()).thenReturn(false);

        Object result = userRoleService.updateUserRole(request);

        assertNotNull(result);
        assertTrue(result.toString().contains("Permission denied"));
        verify(userNameService).getUserId();
        verify(oauthUsersService).hasAdminMenuDB(testUserId, testProvider);
        verify(presentation).isSuperAdmin();
    }

}
