document.addEventListener("DOMContentLoaded", function () {
  // renderChatList();
  renderSuggestions();
  document.head.insertAdjacentHTML(
    "beforeend",
    '<link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.16/tailwind.min.css" rel="stylesheet">'
  );
  const chatElementRef = document.getElementById("chat-element");

  const style = document.createElement("style");
  style.innerHTML = `
  .hidden-chat {
    display: none;
  }
  #chat-widget-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    flex-direction: column;
  }
  #chat-popup {
    height: 70vh;
    max-height: 70vh;
    transition: all 0.3s;
    overflow: hidden-chat;
  }
  .loader {
    border: 4px solid #f3f3f3; / Light grey /
    border-top: 4px solid #3498db; / Blue /
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
  }


   @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

  @media (max-width: 768px) {
    #chat-popup {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      max-height: 100%;
      border-radius: 0;
    }
  }
  `;

  document.head.appendChild(style);
  const scriptTag = document.querySelector('script[src="/js/ai.js"]');
  var userName = "";
  var userId = "";
  if (scriptTag) {
    userId = scriptTag.getAttribute("data-user-id")
      ? scriptTag.getAttribute("data-user-id")
      : "Anonymous";
    userName = scriptTag.getAttribute("data-user-name")
      ? scriptTag.getAttribute("data-user-name")
      : "Anonymous";
  }

  loadChatHistory();
  onUserRequest();



  function onUserRequest(message) {
    if (scriptTag) {
      const apiUrl = scriptTag.getAttribute("data-api-url");
      // const chatElementRef = document.getElementById("chat-element");


      // Add the messagePreprocessor function
      chatElementRef.messagePreprocessor = (message) => {
        const timestamp = new Date().toUTCString();
        const readableTime = new Date(timestamp).toLocaleString(); // auto uses local timezone

        // Different styling for user and AI messages
        if (message.role === "user") {


          // Ensure timestamp and readableTime are defined
          const timestamp = new Date().toUTCString();
          const readableTime = new Date(timestamp).toLocaleString(); // Local timezone
          const userMessagehtml = `<div class="chat-box">
            <div style="margin-bottom: 10px; color: white; font-size: 13px;">
          <img src="/ai-user.jpg" alt="User Icon" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 5px;">
            <b class="chat-username">${userName !== "Anonymous" ? userName : "User"}</b>
          </div>
            <div style="margin-bottom: 10px;">${message.html}</div>
            <div style="color: white; text-align: right;">${readableTime}</div>
          </div>`;
          const messageIndexArray = chatElementRef.getMessages() || [];
          const messageIndex = messageIndexArray.length - 1; // Get the index of the last message
          // Update the user message in the chat
          chatElementRef.updateMessage(
            {
              html: `
        <div class="chat-box">
          <div style="margin-bottom: 10px; color: white; font-size: 13px;">
          <img src="/ai-user.jpg" alt="User Icon" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 5px;">
            <b class="chat-username">${userName !== "Anonymous" ? userName : "User"}</b>
          </div>
          <div style="margin-bottom: 10px;">${message.html}</div>
          <div style="color: white; text-align: right;">${readableTime}</div>
        </div>
      `,
            },
            messageIndex // Index of the message to update
          );

          // Store the user message in localStorage
          storeMessage(userName, userMessagehtml, "user", timestamp, userId, "vanaAI");
        } else if (message.role === "ai") {

          return {
            html: `
            <div class="chat-box">
               <div style="margin-bottom: 10px;">
            <img src="/path-to-icon1.png" alt="AI Icon" style="width: 20px; height: 20px; vertical-align: middle; margin-right: 5px;">
            <b>Tool</b>
            </div>
              <div style="margin-bottom: 10px;">${message.html}</div>
              <div style="font-size: 12px; color: #6b7280; text-align: right;">${readableTime}</div>
            </div>
          `,
            role: message.role,
          };
        }
      };

      chatElementRef.connect = {
        handler: async (body, signals) => {
          const timestamp = new Date().toUTCString();

          // Preprocess the user's question
          const userMessage = chatElementRef.messagePreprocessor({
            html: body.messages[0].text,
            role: "user",
          });
          // Send the user's message to the API
          fetch(`${apiUrl}`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              question: body.messages[0].text, // Send the user message as 'question' to the Flask API
            }),
          })
            .then((response) => response.json())
            .then((data) => {
              console.log("data", data);
              if (data.error) {
                const timestamp = new Date().toUTCString();
                const messageDiv = '<div class="chat-box"  style="color: #374151;border-radius: 5px;padding: 10px;">' + data.error + '</div>';
                storeMessage(userName, messageDiv, "ai", timestamp, userId, "vanaAI");
                signals.onResponse({ html: messageDiv });
                throw new Error("Failed to fetch response from the API.");
              }

              const { sql, df_html } = data;
              const timestamp = new Date().toUTCString();

              console.log(df_html)
              let df_html_updated = df_html.replaceAll(
                /<table border=\"1\" class=\"dataframe table table-bordered\">/g,
                '<table style="width: 100%;margin: 0 auto;border: 1px solid #e2e8f0;border-radius: 0.375rem;overflow: hidden; border-bottom:none">'
              );


              df_html_updated = df_html_updated.replaceAll(
                /<thead>/g,
                '<thead style="font-size: 0.75rem;line - height: 1rem;color: #374151;">'
              );
              df_html_updated = df_html_updated.replaceAll(
                /<tr>/g,
                '<tr style="text-align:left;">'
              );
              df_html_updated = df_html_updated.replaceAll(
                /<th>/g,
                '<th style="padding: 0.75rem 1.5rem;text-transform: capitalize;text-align:left;border-bottom: 1px solid #E5E7EB;">'
              );
              df_html_updated = df_html_updated.replaceAll(
                /<td>/g,
                '<td style="padding: 0.75rem 1.5rem; border-bottom: 1px solid #E5E7EB;">'
              );
              // df_html_updated = df_html_updated.replace("table-bordered", "");
              df_html_updated = df_html_updated.replace('border="1"', "");

              console.log(df_html_updated);


              const htmlContent = `
              <div class="custom-ai-response">
                <div class="table-container" style="overflow-x: auto; width: 100%;">
                  ${df_html_updated} <!-- Insert the table HTML -->
                </div>
              </div>
            `;

              // Preprocess the AI's answer
              const aiMessage = chatElementRef.messagePreprocessor({
                html: htmlContent,
                role: "ai",
              });

              storeMessage("ai", aiMessage.html, aiMessage.role, timestamp, userId, "vanaAI");
              signals.onResponse({ html: aiMessage.html }); // `response` should match your API response field
            });
        },
      };
    }
  }

  async function loadChatHistory() {
    const elementRef = document.getElementById("chat-element");
    let chatHistory = [];
    if (userId != "Anonymous") {
      chatHistory = await new Promise((resolve, reject) => {
        fetchRawData(
          "/ai-conversation?contextSection=vanaAI",
          (res, error) => {
            if (error) {
              reject(error); // Handle errors
            } else {
              resolve(res?.data.conversations || []); // Resolve with fetched data
            }
          }
        );
      });
    } else {
      chatHistory = JSON.parse(localStorage.getItem("vanaAI")) || [];
    }

    chatHistory?.forEach(obj => {
      if (obj?.role === "ai") {
        obj.html = obj.html.replaceAll(
          /<th>/g,
          '<th class="head-table-class">'
        );
        obj.html = obj.html.replaceAll(
          /<td>/g,
          '<td class="row-table-class">'
        );
        obj.html = obj.html.replace("table-bordered", "");
        obj.html = obj.html.replace('border="1"', "");
      }
    });
    const userEntries = chatHistory?.filter(entry => entry.userId == userId);
    const updatedEntries = userEntries?.map(entry => {
      const parser = new DOMParser();
      const doc = parser.parseFromString(entry.html, "text/html");

      const usernameElement = doc.querySelector('.chat-username');

      if (usernameElement && usernameElement.innerHTML.trim() !== userName) {
        usernameElement.innerHTML = userName;
      }

      return {
        ...entry,
        html: doc.body.innerHTML // Update the HTML with the new username
      };
    });
    const filteredData = updatedEntries?.filter(item => item.html.includes('class="chat-box"'));
    elementRef.history = filteredData;
  }
});

function renderSuggestions() {
  const suggestionBox = document.getElementById("suggestion-box");
  suggestionBox.innerHTML = ""; // Clear any existing suggestions

  // Get the API URL from the script tag
  const scriptTag = document.querySelector('script[src="/js/ai.js"]');
  const apiUrl = scriptTag.getAttribute("data-suggestion-url");
  document.cookie = "role=Researcher; path=/";
  document.cookie = "user=<EMAIL>; path=/";
  // Perform the fetch request
  fetch(apiUrl, {
    method: "GET",
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.error) return;

      // Create and append the header
      const headerBox = document.createElement("div");
      headerBox.className = "py-0 px-2";
      headerBox.textContent = data.header;
      suggestionBox.appendChild(headerBox);

      // Create the suggestion list container
      const listContainer = document.createElement("div");
      listContainer.className = "px-0 py-2 space-y-4 w-full suggestion-list";
      listContainer.id = "suggestion-list";
      headerBox.appendChild(listContainer);
      // Iterate over suggestions and create list items
      data?.questions?.slice(0, 5).forEach((suggestion) => {
        const listItem = document.createElement("div");
        listItem.className = "suggestion-item";
        listItem.textContent = suggestion;

        // Add click event to each suggestion
        // listItem.addEventListener('click', () => handleSuggestionClick(suggestion));

        // Append list item to the container
        listContainer.appendChild(listItem);
      });
      const chatElementRef = document.getElementById("chat-element");

      chatElementRef.htmlClassUtilities = {
        ["suggestion-item"]: {
          events: {
            click: (event) => {
              const text = event.target.innerText;
              chatElementRef.submitUserMessage(text);
            },
          },
          styles: {
            default: {
              backgroundColor: "#f2f2f2",
              borderRadius: "10px",
              padding: "10px",
              cursor: "pointer",
              textAlign: "left",
              color: "#1D4ED8",
              marginTop: "10px",
              marginBottom: "10px",
            },
            hover: { backgroundColor: "#ebebeb" },
            click: { backgroundColor: "#e4e4e4" },
          },
        },
        ["table"]: {
          styles: {
            default: {
              border: "1px solid black",
              padding: "1px",
              borderCollapse: "collapse"
            }
          }
        },
        ["head-table-class"]: {
          styles: {
            default: {
              border: "0px",
              padding: "3px",
              background: "#dbdbdb"
            }
          }
        },
        ["row-table-class"]: {
          styles: {
            default: {
              border: "1px",
              padding: "3px"
            }
          }
        },
        ["suggestion-list"]: {
          styles: {
            default: {
              marginTop: "15px",
            }
          }
        }
      };
    })
    .catch((error) => console.error("Error fetching suggestions:", error));
}

function toggleAccordion(button) {
  const content = button.nextElementSibling;
  if (content.style.display == "none") {
    content.style.display = "block"; // Toggle visibility
    button.querySelector('.material-icons').style.transform = "rotate(180deg)"; // Rotate arrow icon
    button.querySelector('.material-icons').style.transition = "transform 0.3s ease-in-out";
  }
  else {
    content.style.display = "none"; // Toggle visibility
    button.querySelector('.material-icons').style.transform = "rotate(0deg)"; // Rotate arrow icon
    button.querySelector('.material-icons').style.transition = "transform 0.3s ease-in-out";
  }

}
const chatElementRef = document.getElementById("chat-element");
// const inputBox = chatElementRef.getInputBox(); // Get the input box element or API reference

// Check if the input box is empty

chatElementRef.validateInput = (text, files) => {
  console.log(text);
  return text || files.length > 0;
};



