import {
    AGGridAide,
    AGGridAideBuilder,
} from "@presentation/shell/aggrid-aide.js";

let filterInfo = {};

let clearFilter = false;

const filterCategories = [
    {
        "value": "age",
        "text": "Age",
        "title": "Age"
    },
    {
        "value": "tir",
        "text": "Time in Range (TIR)",
        "title": "TIR"
    },
    {
        "value": "tbr",
        "text": "Time Below Range (TBR)",
        "title": "TBR"
    },
    {
        "value": "tar",
        "text": "Time Above Range (TAR)",
        "title": "TAR"
    },
    {
        "value": "wear_time",
        "text": "% Wear Time",
        "title": "Wear Time"
    },
    {
        "value": "gri",
        "text": "Glycemic Risk Index (GRI)",
        "title": "GRI"
    },
    {
        "value": "gender",
        "text": "Gender",
        "title": "Gender"
    },
    {
        "value": "gmi",
        "text": "Glucose Management Indicator (GMI)",
        "title": "GMI"
    },
    {
        "value": "days_wear",
        "text": "Days of Wear",
        "title": "Days of Wear"
    },
    {
        "value": "hba1c",
        "text": "HbA1c",
        "title": "HbA1c"
    }
];

let defaultGridCols = [
    {
        headerName: "Study ID",
        field: "study_display_id",
        filter: false,
        sortable: true,
        cellRenderer: function (params) {
            if (params.value) {
                const link = document.createElement("a");
                link.href = "/study/info/" + params.data.study_id + "?tab=Population Percentage";
                link.innerText = params.value;
                return link;
            } else {
                return null;
            }
        },
    },
    {
        headerName: "Study Name",
        field: "title",
        enableRowGroup: true,
        maxWidth: 300,
        filter: false,
        tooltipValueGetter: (params) => `${params.value}`,
        flex: 1
    },
    {
        headerName: "NCT Number",
        field: "nct_number",
        enableRowGroup: true,
        filter: false,
        flex: 1
    },
    {
        headerName: "Description",
        field: "description",
        maxWidth: 500,
        enableRowGroup: true,
        filter: false,
        tooltipValueGetter: (params) => `${params.value}`,
        flex: 2
    },
    {
        headerName: "From Date",
        field: "start_date",
        enableRowGroup: true,
        filter: false,
        flex: 1
    },
    {
        headerName: "To Date",
        field: "end_date",
        enableRowGroup: true,
        filter: false,
        flex: 1
    }];

let customColumns = [{
    headerName: "Gender",
    field: "gender",
    filter: false
},
{
    headerName: "Age", field: "age", filter: false, sortable: true
},
{
    headerName: "Study Arm",
    field: "study_arm",
    filter: false
},
{
    headerName: "Baseline HbA1c",
    field: "hba1c",
    filter: false
},
{
    headerName: "TIR", field: "tir", filter: false
},
{
    headerName: "TAR", field: "tar", filter: false
},
{
    headerName: "TAR(VH)",
    field: "tar_vh",
    filter: false
},
{
    headerName: "TAR(H)",
    field: "tar_h",
    filter: false
},
{
    headerName: "TBR", field: "tbr", filter: false
},
{
    headerName: "TBR(L)",
    field: "tbr_l",
    filter: false
},
{
    headerName: "TBR(VL)",
    field: "tbr_vl",
    filter: false
},
{ headerName: "GMI", field: "gmi", filter: false },
{
    headerName: "%GV",
    field: "percent_gv",
    filter: false
},
{ headerName: "GRI", field: "gri", filter: false },
{
    headerName: "Days Of Wear",
    field: "days_wear",
    filter: false
},
{
    headerName: "%Wear Time",
    field: "wear_time",
    filter: false
},
{
    headerName: "Data Start Date",
    field: "data_start_date",
    filter: false
},
{
    headerName: "Data End Date",
    field: "data_end_date",
    filter: false
}];

//let gridCols = JSON.parse(JSON.stringify(defaultGridCols));
let gridCols = defaultGridCols.map(col => ({ ...col }));

//let gridCols = defaultGridCols;

export const appliedFilters = document.getElementById("applied-filters");
export const filterCategory = document.getElementById("filter-category-opt");

export const optionsData = [
    { value: "equals", text: "Equals (=)" },
    { value: "greaterThan", text: "Greater than (>)" },
    { value: "greatersOrEqual", text: "Greater than or equal to (>=)" },
    { value: "lessThan", text: "Less than (<)" },
    { value: "lessOrEqual", text: "Less than or equal to (<=)" },
    { value: "between", text: "Between (Range)" }
];

export const genderOptionsData = [
    { value: "", text: "Select" },
    { value: "Male", text: "Male" },
    { value: "Female", text: "Female" },
    { value: "Others", text: "Other" },
    { value: "Unknown", text: "Unknown" },
];


let agGridFilterParams = {};

const dynamicFilters = document.getElementById("dynamic-filters");

const removeButtonHtml = `<div class="box-border w-[41px] h-11 border rounded-[3px] border-solid border-[#E3C3C5] bg-[#E8D0D1] justify-center flex items-center">
                              <svg width="14" height="18" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.6393 2.35223H10.1469V1.65374C10.1469 0.88231 9.52144 0.256775 8.74997 0.256775H5.25758C4.48611 0.256775 3.86061 0.88231 3.86061 1.65374V2.35223H0.368225V3.05071H1.08787L1.78703 16.3218C1.78703 17.0933 2.41253 17.7188 3.18399 17.7188H10.8673C11.6387 17.7188 12.2642 17.0933 12.2642 16.3218L12.9511 3.05071H13.6394L13.6393 2.35223ZM4.5591 1.65374C4.5591 1.26836 4.8729 0.955259 5.25758 0.955259H8.74997C9.13535 0.955259 9.44845 1.26836 9.44845 1.65374V2.35223H4.5591V1.65374ZM11.5671 16.2856L11.5657 16.3034V16.3218C11.5657 16.7065 11.2526 17.0203 10.8672 17.0203H3.18396C2.79928 17.0203 2.48548 16.7065 2.48548 16.3218V16.3034L2.48481 16.285L1.78703 3.05071H12.252L11.5671 16.2856Z" fill="#CE131B"/>
                                <path d="M7.353 4.44765H6.65451V15.6233H7.353V4.44765Z" fill="#CE131B"/>
                                <path d="M5.27326 15.6008L4.55843 4.44701L3.86132 4.49131L4.57618 15.6451L5.27326 15.6008Z" fill="#CE131B"/>
                                <path d="M10.1517 4.46948L9.4546 4.42585L8.74997 15.6015L9.44708 15.6451L10.1517 4.46948Z" fill="#CE131B"/>
                              </svg>
                            </div>`;
const applyButtonHtml = `<div class="box-border w-[41px] h-11 border rounded-[3px] border-solid border-[#BAD2AF] bg-[#d3e9c9] justify-center flex items-center">
                            <svg width="18" height="13" viewBox="0 0 18 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M16.3334 1.5L6.25002 11.5833L1.66669 7" stroke="#628652" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                          </div>`;

document.addEventListener("DOMContentLoaded", function () {
    showActionButtons();
    initGrid();
    // Handle checkbox event for all filter options
    document.querySelectorAll(".filter-cat-opt").forEach(checkbox => {
        checkbox.addEventListener("change", (event) => {
            const selectedCategory = event.target.value;
            if (event.target.checked) {
                const selectedTitle = event.target.previousElementSibling.textContent.trim();
                if (selectedCategory) {
                    document.getElementById("filter_manadatory").classList.add("hidden");

                    // Check if the category has already been added
                    if (document.querySelector(`[data-category="${selectedCategory}"]`)) {
                        alert(`The ${selectedCategory} filter has already been added.`);
                        return;
                    }

                    let filterDiv = createFilterDiv(selectedCategory, selectedTitle);
                } else {
                    document.getElementById("filter_manadatory").classList.remove("hidden");
                }
                // Handle filter application logic here
            } else {
                console.log(`${event.target.value} filter removed`);
                // Handle filter removal logic here
                removeFilter(selectedCategory);
            }
            showActionButtons();
        });
    });

    // Select a parent element that is present in the DOM during page load
    document.body.addEventListener('click', function (event) {
        if (event.target.classList.contains('applied-filter-div')) {
            let this_ele = event.target;
            let dataId = event.target.querySelector('.applied-filter-span').getAttribute('data-id');
            let catTextArr = filterCategories.find(opt => opt.value === dataId);
            let filterDiv = createFilterDiv(dataId, catTextArr.text);
            filterDiv.style.display = "flex";
            this_ele.remove();
        }
        if (event.target.classList.contains('applied-filter-span')) {
            // Handle the click event for the dynamically created element here
            let this_ele = event.target;
            let dataId = event.target.getAttribute('data-id');
            let catTextArr = filterCategories.find(opt => opt.value == dataId);
            let filterDiv = createFilterDiv(dataId, catTextArr.text, filterInfo.filters[dataId]);
            filterDiv.style.display = "flex";
            this_ele.parentNode.remove();
        }

    });

    document.getElementById("clear-all").addEventListener("click", function () {
        filterInfo = {};
        clearFilter = true;
        // Clear all dynamic filters
        document.getElementById("dynamic-filters").innerHTML = "";
        document.getElementById("applied-filters").innerHTML = "";

        agGridFilterParams = window.agGridFilterParams = {}
        let filterItems = document.getElementsByClassName("filter-item");
        for (let i = 0; i < filterItems.length; i++) {
            filterItems[i].classList.remove("active-filter");
        }
        const checkboxes = document.querySelectorAll(".filter-cat-opt");
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        //gridCols = JSON.parse(JSON.stringify(defaultGridCols));
        gridCols = defaultGridCols.map(col => ({ ...col }));
        applyFilter();
        clearFilter = false;
        showActionButtons();
    });

    document.getElementById("filter-toggle").addEventListener("click", function () {
        const toggleButton = document.getElementById("filter-toggle");
        const accordionBody = document.getElementById("accordion-collapse-body-1");
        const filterHeaderButton = document.getElementById("filter-header-button");
        const searchStudyLeft = document.getElementById("search-study-left");
        const searchStudyRight = document.getElementById("search-study-right");

        // Toggle visibility of the accordion and filter header button
        accordionBody.classList.toggle("hidden");
        filterHeaderButton.classList.toggle("hidden");

        // Toggle rounded corners and adjust column spans
        if (toggleButton.classList.contains("rounded-l")) {
            toggleButton.classList.replace("rounded-l", "rounded");
            searchStudyLeft.classList.remove("col-span-12", "md:col-span-12", "lg:col-span-4", "xl:col-span-2");
            searchStudyLeft.classList.add("col-auto");
            //searchStudyRight.classList.replace("col-span-12", "col-span-11");
            searchStudyRight.classList.remove("md:col-span-12", "lg:col-span-8", "xl:col-span-10");
        }
        else {
            toggleButton.classList.replace("rounded", "rounded-l");
            searchStudyLeft.classList.remove("col-auto");
            searchStudyLeft.classList.add("col-span-12", "md:col-span-12", "lg:col-span-4", "xl:col-span-2");
            //searchStudyRight.classList.replace("col-span-11", "col-span-12");
            searchStudyRight.classList.add("md:col-span-12", "lg:col-span-8", "xl:col-span-10");
        }

    });

});

function showActionButtons() {
    var button = document.getElementById("clear-all");
    var containerDiv = document.getElementById("filter-container")
    const checkedCount = document.querySelectorAll(".filter-cat-opt:checked").length;
    if (checkedCount == 0) {
        button.classList.add('hidden');
        containerDiv.classList.add('hidden');
    }
    else {
        button.classList.remove('hidden');
        containerDiv.classList.remove('hidden');

    }
}

function applyFilter() {
    //initFilter = true;
    let sqlFragments = [];
    if (filterInfo?.filters) {
        sqlFragments = Object.values(filterInfo?.filters)
            .filter(
                (item) =>
                    typeof item === "object" && item !== null && "sqlFragment" in item
            )
            .map((item) => item.sqlFragment);
    }

    // if (cohortFilter.studyIds.length > 0 || cohortFilter.filters.length > 0) {
    agGridFilterParams = generateFilterModel();
    window.agGridFilterParams = agGridFilterParams;

    document.getElementById("serverDataGrid").innerHTML = '';
    initGrid(window.agGridFilterParams);
    // }
}

function generateFilterModel() {
    // Initialize the filterParams object
    let filterParams = {};
    // Helper function to handle single and multiple conditions
    const setFilterCondition = (key, type, values) => {
        if (values.length === 1) {
            filterParams[key] = {
                filterType: "text",
                type: "equals",
                filter: values[0]
            };
        } else if (values.length > 1) {
            filterParams[key] = {
                filterType: "text",
                operator: "OR",
                conditions: values.map(value => ({
                    filterType: "text",
                    type: "equals",
                    filter: value
                }))
            };
        }
    };

    // Add studyIds filter
    if (filterInfo.studyIds?.length) {
        setFilterCondition('study_id', 'text', filterInfo.studyIds);
    }

    // Add filter for gender
    if (filterInfo?.filters?.gender) {
        filterParams.gender = {
            filterType: "text",
            type: "equals",
            filter: filterInfo.filters.gender.values[0]
        };
    }

    // Helper function for range filters like age, tir, etc.
    const setRangeFilter = (key, filterObj) => {
        filterParams[key] = {
            filterType: "text",
            type: filterObj.type,
            filter: filterObj.values[0]
        };
        if (filterObj.type === "between") {
            filterParams[key].secondFilter = filterObj.values[1];
        }
    };

    // List of filters that follow the same "range" pattern
    const rangeFilters = ['age', 'tir', 'tbr', 'tar', 'wear_time', 'gri', 'gmi', 'days_wear', 'hba1c'];
    if (filterInfo?.filters) {
        rangeFilters.forEach(filterKey => {
            if (filterInfo?.filters[filterKey]) {
                setRangeFilter(filterKey, filterInfo?.filters[filterKey]);
            }
        });
    }

    return filterParams;
}


function initGrid(filterModel) {
    let gridFilterParams = filterModel ? filterModel : {};
    let lastResponseLength = 100;
    let hasMoreData = true;
    const agGridInstance = new AGGridAideBuilder()
        .withColumnDefs(gridCols)
        .withServerSideDatasource(
            window.shell.serverSideUrl(
                `/api/ux/tabular/jooq/research-study/population/dashboard.json`
            ),
            ((data, valueCols) => {
                return valueCols.map((col) => ({
                    headerName: col.displayName,
                    field: col.field
                }));
            }),
            {
                beforeRequest: async (reqPayload, dataSourceUrl) => {
                    // Add custom parameters here
                    reqPayload.body = {
                        ...reqPayload.body,
                        "filterModel": {
                            ...reqPayload.body.filterModel, // Preserve AG Grid filters
                            ...gridFilterParams, // Add custom filter
                        },
                    };
                    return reqPayload;

                },
                beforeSuccess: async (serverRespPayload, respMetrics, dataSourceUrl) => {
                    // Update lastResponseLength
                    lastResponseLength = serverRespPayload.data.length;
                    // Clear the grid if there's no more data
                    if (!hasMoreData) {
                        clearGrid();
                    }
                },
            }
        )
        .withEventHandler("onFilterChanged", function (event) {
            console.log(event)
        })
        .withGridDivStyles({ height: "550px", width: "100%" })
        .build();
    agGridInstance.gridOptions.autoSizeStrategy = { type: "fitGridWidth" };

    agGridInstance.init("serverDataGrid");

    window.agGridInstance = agGridInstance;
    window.agGridFilterParams = agGridFilterParams;
}

function createFilterDiv(selectedCategory, selectedTitle, filterData) {
    const filterId = `filter-${selectedCategory}`;
    let filterDiv = document.getElementById(filterId);

    if (!filterDiv) {
        filterDiv = document.createElement("div");
        filterDiv.className = "flex flex-col md:flex-row mb-2 border p-2 border-gray-100 bg-gray-50 rounded";
        filterDiv.dataset.category = selectedCategory;
        filterDiv.id = filterId;

        filterDiv.appendChild(createLabel(selectedTitle));
        const operatorSelect = createOperatorSelect(selectedCategory, filterData);
        filterDiv.appendChild(operatorSelect);
        filterDiv.appendChild(createFilterInputs(selectedCategory, filterData, operatorSelect));
        dynamicFilters.appendChild(filterDiv);
    }
    return filterDiv;
}

function createLabel(selectedTitle) {
    const label = document.createElement("label");
    label.className = "text-gray-700 font-normal h-11 bg-[#f8fafd] px-2 py-1.5 text-xsm border border-[#DDDDDD] rounded-[3px] rounded-br-none rounded-tr-none w-44 flex items-center";
    label.innerHTML = selectedTitle;
    return label;
}

function createFilterInputs(selectedCategory, filterData, operatorSelect) {
    const container = document.createElement("div");
    container.className = "flex space-x-2 w-60";

    let valueInput;
    if (selectedCategory == 'gender') {
        valueInput = createGenderSelect(selectedCategory, filterData);
    }
    else {
        valueInput = createValueInput(selectedCategory, filterData);
    }
    const valueInput2 = createValueInput(`${selectedCategory}-value-2`, filterData, true);

    // const valueInput = createValueInput(selectedCategory, filterData);
    // const valueInput2 = createValueInput(`${selectedCategory}-value-2`, filterData, true);

    const applyBtn = createApplyButton(operatorSelect, valueInput, valueInput2, selectedCategory, filterData);
    const removeBtn = createRemoveButton(selectedCategory);

    operatorSelect.addEventListener("change", () => toggleValueInput(operatorSelect, valueInput, valueInput2, applyBtn));
    [valueInput, valueInput2].forEach(input => input.addEventListener("input", () => toggleApplyButton(valueInput, valueInput2, applyBtn)));

    container.append(valueInput, valueInput2, applyBtn, removeBtn);
    return container;
}

function createOperatorSelect(selectedCategory, filterData) {
    const select = document.createElement("select");
    select.name = `${selectedCategory}-operator`;
    select.className = "box-border mr-2 my-2 md:my-0 w-60 h-11 border rounded-[3px] rounded-tl-none rounded-bl-none ml-[-1px] border-[#DDDDDD] bg-white text-sm";

    let operatorOptions = JSON.parse(JSON.stringify(optionsData));

    if (selectedCategory == 'gender') {
        select.disabled = true;
        operatorOptions = operatorOptions.filter(item => item.value == 'equals')
    }

    operatorOptions.forEach(option => {
        const optionElement = document.createElement("option");
        optionElement.value = option.value;
        optionElement.textContent = option.text;
        select.appendChild(optionElement);
    });

    if (filterData?.operator) {
        select.value = filterData.operatorText;
    }
    return select;
}

function createGenderSelect(selectedCategory, filterData) {
    const select = document.createElement("select");
    select.name = `${selectedCategory}`;
    select.className = "box-border my-2 md:my-0 w-30 h-11 border rounded-[3px] rounded-tl-none rounded-bl-none ml-[-1px] border-[#DDDDDD] bg-white text-sm";

    genderOptionsData.forEach(option => {
        const optionElement = document.createElement("option");
        optionElement.value = option.value;
        optionElement.textContent = option.text;
        select.appendChild(optionElement);
    });

    if (filterData?.values) {
        select.value = filterData.values[0] || '';
    }
    return select;
}

function createValueInput(name, filterData, isSecondInput = false) {
    const input = document.createElement("input");
    input.type = "number";
    input.name = name;
    input.placeholder = isSecondInput ? "Value 2" : "Value";
    input.className = `box-border w-[100px] h-11 border rounded-[3px] border-[#DDDDDD] bg-white text-sm ${isSecondInput ? 'hidden' : ''}`;

    if (filterData?.values) {
        input.value = filterData.values[isSecondInput ? 1 : 0] || '';
        if (isSecondInput && filterData.values.length > 1) {
            input.classList.remove("hidden");
        }
    }
    return input;
}

function createApplyButton(operatorSelect, valueInput, valueInput2, selectedCategory, filterData) {
    const btn = document.createElement("span");
    btn.className = "add-btn ml-2 cursor-pointer text-green-500 hover:text-green-700 hidden";
    btn.innerHTML = applyButtonHtml;
    btn.onclick = () => applyCategoryFilter(operatorSelect, valueInput, valueInput2, selectedCategory);
    return btn;
}

function createRemoveButton(selectedCategory) {
    const btn = document.createElement("span");
    btn.className = "remove-btn ml-2 cursor-pointer text-red-500 hover:text-red-700";
    btn.innerHTML = removeButtonHtml;
    btn.onclick = () => removeFilter(selectedCategory);
    return btn;
}

function toggleValueInput(operatorSelect, valueInput, valueInput2, applyBtn) {
    const isBetween = operatorSelect.value === "between";
    valueInput2.classList.toggle("hidden", !isBetween);
    toggleApplyButton(valueInput, valueInput2, applyBtn);
}

function toggleApplyButton(valueInput, valueInput2, applyBtn) {
    const isValid = valueInput?.value && valueInput?.value !== "" && (valueInput2.classList.contains("hidden") || valueInput2.value !== "");
    applyBtn.classList.toggle("hidden", !isValid);
}

function applyCategoryFilter(operatorSelect, valueInput, valueInput2, selectedCategory) {
    const opText = operatorSelect.value;
    const valueText = valueInput.value;
    const valueText2 = valueInput2.classList.contains("hidden") ? "" : ` ${valueInput2.value}`;
    generateCategoryFilter(selectedCategory, opText, valueText, valueText2);
    document.getElementById(`filter-${selectedCategory}`).style.display = "none";
}


function removeFilter(selectedCategory) {
    if (filterInfo && filterInfo.filters && filterInfo.filters[`${selectedCategory}`]) {
        delete filterInfo?.filters[`${selectedCategory}`];
    }
    let filterDiv = document.getElementById(`filter-${selectedCategory}`)
    filterDiv.remove();
    document.getElementById(`${selectedCategory}-filter-text`)?.remove();
    let itemIndex = gridCols.findIndex(obj => obj.field === selectedCategory);
    if (itemIndex > -1) {
        gridCols.splice(itemIndex, 1);
    }
    console.log(Object.keys(filterInfo.filters));
    if (filterInfo?.filters && Object.keys(filterInfo.filters).length == 0) {
        document.getElementById("clear-all").classList.add("hidden");
    }

    document.getElementById(`${selectedCategory}_check`).checked = false;
    applyFilter();
}

function generateConditionalQueryFragment(field, op = "=", values) {
    switch (op) {
        case "between":
            return (
                field + " > " + values[0] + " AND " + field + " < " + values[1]
            );
        default:
            return field + "  " + op + " '" + values[0] + "'";
    }
};
function generateCategoryFilter(selectedCategory, opText, valueText, valueText2) {
    let catTextArr = filterCategories.find(opt => opt.value === selectedCategory);
    let operatorTextArr = optionsData.find(opt => opt.value === opText);
    const filterText = `${catTextArr.text} ${operatorTextArr.text} ${valueText}${valueText2}`;
    // Create a display text for the applied filter
    const appliedFilterDiv = document.createElement("div");
    appliedFilterDiv.id = `${selectedCategory}-filter-text`;
    appliedFilterDiv.className =
        "applied-filter-div box-border bg-[#edf5ff] border rounded-[3px] cursor-pointer border-solid border-[#A7BFDE]  font-normal text-xs leading-1 text-[#557dae] flex justify-between items-center px-2 py-1 mr-1 mb-1";
    appliedFilterDiv.innerHTML = `<span class="applied-filter-span" data-id="${selectedCategory}">${filterText}</span>`;
    filterInfo.filters = filterInfo.filters ? filterInfo.filters : {};
    filterInfo.filters[selectedCategory] = generateFilterObject(
        selectedCategory,
        opText,
        "number",
        [valueText, valueText2]
    );
    appliedFilters.prepend(appliedFilterDiv);

    let selColArr = customColumns.filter(item => item.field === selectedCategory);
    if (selColArr.length > 0) {
        let newCol = JSON.parse(JSON.stringify(selColArr[0]));
        let selCatArr = filterCategories.find(item => item.value == selectedCategory);
        if (selCatArr && selCatArr.title && selCatArr.title != "") {
            let op = getOperator(operatorTextArr.value);
            let newHeader = selCatArr.title + " " + op + " " + valueText;
            if (valueText2 && valueText2 != '') {
                newHeader += " and " + valueText2;
            }
            newCol.headerName = newHeader;
            newCol.valueFormatter = (params) => {
                return params.value != null ? `${params.value}%` : '';
            }
        }
        let itemIndex = gridCols.findIndex(obj => obj.field === selectedCategory);
        if (itemIndex > -1) {
            gridCols[itemIndex] = newCol;
        }
        else {
            gridCols.splice(3, 0, newCol);
        }

    }

    if (clearFilter == false) {
        applyFilter();
    }
}

function generateFilterObject(selCat, operatorText, filterType, values) {
    let op = getOperator(operatorText);
    let fieldValues = values.filter((val) => val != "");
    let filterObj = {
        filterType: filterType,
        type: operatorText,
        operator: op,
        values: fieldValues,
        sqlFragment: generateConditionalQueryFragment(selCat, op, fieldValues),
    };
    return filterObj;
}

function getOperator(opval) {
    switch (opval) {
        case "equals":
            return "=";
        case "greaterThan":
            return ">";
        case "greatersOrEqual":
            return ">=";
        case "lessThan":
            return "<";
        case "lessOrEqual":
            return "<=";
        case "between":
            return "between";
    }
}