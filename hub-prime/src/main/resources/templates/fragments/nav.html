<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title>Navigation Fragments</title>
</head>

<body>
    <nav th:fragment="main-menu-horiz-and-authn-profile" class="bg-gray-800 sticky top-0 z-10">
        <div class="mx-auto max-w-9xl px-4 sm:px-6 lg:px-8">
            <div class="flex h-16 items-center justify-between">
                <div class="flex items-center">
                    <a href="/home">
                        <div class="flex flex-shrink-0">
                            <img class="h-12 w-12 mr-2 rounded-md" src="/diabetic-research-hub-logo-w.png"
                                alt="Diabetes Research Hub">
                            <span class="text-gray-300 text-base font-medium py-2 w-48 mt-1">Diabetes Research
                                Hub</span>
                            <!-- <img class="h-8 w-8" src="https://tailwindui.com/img/logos/mark.svg?color=indigo&shade=500"
                            alt="Technology by Design"> -->
                        </div>
                    </a>
                    <div id="nav-prime" class="hidden lg:block">
                        <div class="ml-10 flex items-baseline space-x-4 flex-wrap" sec:authorize="isAuthenticated()">
                            <a th:each="navItem: ${navPrime}"
                                th:attr="href=@{${navItem.href.orElse('?')}}, data-privilege=${navItem.text}"
                                th:text="${navItem.text}"
                                class="text-gray-300 hover:bg-gray-700 hover:text-white rounded-md px-3 py-1 text-sm font-medium hidden">Label</a>
                            <!-- <a th:each="navItem: ${navPrime}" th:attr="href=@{${navItem.href.orElse('?')}}"
                                th:text="${navItem.text}" 
                                class="text-gray-300 hover:bg-gray-700 hover:text-white rounded-md px-3 py-1 text-sm font-medium">Label</a> -->

                            <div class="relative group inline-block text-left">
                                <a href="#"
                                    class="text-gray-300 hover:bg-gray-700 hover:text-white rounded-md px-3 py-1 text-sm font-medium flex items-center space-x-1">
                                    <span>Sites</span>
                                    <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor"
                                        aria-hidden="true" data-slot="icon">
                                        <path fill-rule="evenodd"
                                            d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </a>

                                <div
                                    class="absolute left-0 top-full w-48 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 z-10 hidden group-hover:block">
                                    <a href="https://drh.diabetestechnology.org/" target="_blank"
                                        class="hidden block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white"
                                        privilege-action-buttons-links="DRH Home">DRH
                                        Home</a>
                                    <a href="https://www.diabetestechnology.org/" target="_blank"
                                        class="hidden block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white"
                                        privilege-action-buttons-links="DTS Home">DTS
                                        Home</a>
                                    <a th:href="${chatAiBaseUrl}" target="_blank"
                                        class="hidden block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white"
                                        privilege-action-buttons-links="Train English To Sql">Train
                                        English to SQL</a>
                                    <a href="https://drh-ai-research.netspective.com/" target="_blank"
                                        privilege-action-buttons-links="Research Ai"
                                        class="hidden block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">Research
                                        AI</a>
                                </div>

                            </div>

                        </div>
                        <div class="ml-10 flex items-baseline space-x-4 flex-wrap" sec:authorize="!isAuthenticated()">
                            <a th:each="navItem: ${navPrime}" th:attr="href=@{${navItem.href.orElse('?')}}"
                                th:text="${navItem.text}"
                                th:if="${not (navItem.text.contains('Documentation') or navItem.text.contains('Console') or navItem.text.contains('Interaction') or navItem.text.contains('Administration'))}"
                                class="text-gray-300 hover:bg-gray-700 hover:text-white rounded-md px-3 py-1 text-sm font-medium">Label</a>

                            <div class="relative group inline-block text-left">
                                <a href="#"
                                    class="text-gray-300 hover:bg-gray-700 hover:text-white rounded-md px-3 py-1 text-sm font-medium flex items-center space-x-1">
                                    <span>Sites</span>
                                    <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor"
                                        aria-hidden="true" data-slot="icon">
                                        <path fill-rule="evenodd"
                                            d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </a>
                                <div
                                    class="absolute left-0 top-full w-48 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 z-10 hidden group-hover:block">
                                    <a href="https://drh.diabetestechnology.org/" target="_blank"
                                        class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">DRH
                                        Home</a>
                                    <a href="https://www.diabetestechnology.org/" target="_blank"
                                        class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">DTS
                                        Home</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hidden lg:block">
                    <div class="flex items-center w-38">
                        <div class="text-gray-400">v<span th:text="${appVersion}"></span></div>
                        <button type="button" id="nav-prime-notification"
                            class="relative rounded-full bg-gray-800 p-1 text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800">
                            <span class="absolute -inset-1.5"></span>
                            <span class="sr-only">View notifications</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
                            </svg>
                        </button>

                        <!-- Profile dropdown -->
                        <div class="relative ml-3" sec:authorize="isAuthenticated()">
                            <div>
                                <button type="button"
                                    class="relative flex max-w-xs items-center rounded-full bg-gray-800 text-sm text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800"
                                    id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                                    <span class="absolute -inset-1.5"></span>
                                    <span class="sr-only">Open user menu</span>
                                    <div>
                                        <div class="relative group">
                                            <a href="/profile" id="principal_html">
                                                <img th:with="isOAuth2=${#authentication.getPrincipal() instanceof T(org.springframework.security.oauth2.core.user.OAuth2User)}"
                                                    th:src="${isOAuth2 ? #authentication.getPrincipal().attributes['avatar_url'] : '/user.jpg'}"
                                                    class="h-10 rounded-full" alt="User Profile" id="principal_image" />
                                            </a>
                                            <div class="absolute hidden group-hover:block !cursor-default z-10 -mt-1 w-60 origin-top-right right-0 rounded-md bg-white py-2 shadow-lg ring-1 ring-black ring-opacity-5 transition-[opacity] duration-300 divide-y divide-gray-100"
                                                role="menu" aria-orientation="vertical"
                                                aria-labelledby="user-menu-button" tabindex="-1">
                                                <a href="#"
                                                    class="block px-2 py-2 text-sm text-gray-700 text-left leading-[1.75rem] cursor-default"
                                                    role="menuitem" tabindex="-1" id="user-menu-item-0">
                                                    <div>
                                                        <span
                                                            class="inline-block font-medium text-gray-500">Name:</span>
                                                        <span class="userFullName"
                                                            th:with="isOAuth2=${#authentication.getPrincipal() instanceof T(org.springframework.security.oauth2.core.user.OAuth2User)}"></span>
                                                    </div>
                                                    <div>
                                                        <span class="inline-block font-medium text-gray-500">ID:</span>
                                                        <span
                                                            th:with="isOAuth2=${#authentication.getPrincipal() instanceof T(org.springframework.security.oauth2.core.user.OAuth2User)}"
                                                            th:text="${isOAuth2 ? #authentication.getPrincipal().attributes['login'] : #authentication.getPrincipal().username}">
                                                        </span>
                                                    </div>
                                                </a>
                                                <a href="javascript:;" id="logout-button"
                                                    class="block px-2 py-2 text-sm text-gray-700 text-left leading-[1.75rem] cursor-pointer hover:bg-gray-100"
                                                    role="menuitem" tabindex="-1">
                                                    Logout
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div sec:authorize="!isAuthenticated()">
                                        <!-- this should never happen since we're authn-only -->
                                        <div class="relative group">
                                            <a href="/profile" id="principal_html">
                                                <img class="h-8 w-8 rounded-full" src="/user.jpg" alt="" />
                                            </a>
                                        </div>
                                    </div>
                                </button>
                            </div>

                            <div class="hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                                role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button"
                                tabindex="-1">
                                <!-- Active: "bg-gray-100", Not Active: "" -->
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1"
                                    id="user-menu-item-0">Your Profile</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1"
                                    id="user-menu-item-1">Settings</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1"
                                    id="user-menu-item-2">Sign out</a>
                            </div>
                        </div>
                        <div class="relative ml-3" sec:authorize="!isAuthenticated()">
                            <a href="/" class="px-2 py-2 text-sm text-white text-left cursor-pointer" role="menuitem"
                                tabindex="-1" id="user-menu-item-1">
                                <img class="h-8 w-8" src="/anonymous-white.png" alt="" />
                            </a>
                        </div>
                    </div>
                </div>
                <div class="-mr-2 flex lg:hidden">

                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            const mobileMenuButton = document.getElementById('mobile-menu-button');
                            const mobileMenu = document.getElementById('mobile-menu');
                            const hamburgerIcon = document.getElementById('hamburger-icon');
                            const closeIcon = document.getElementById('close-icon');

                            mobileMenuButton.addEventListener('click', () => {
                                // Toggle the hidden class on the mobile menu
                                mobileMenu.classList.toggle('hidden');

                                // Toggle between the hamburger and close icons
                                hamburgerIcon.classList.toggle('hidden');
                                closeIcon.classList.toggle('hidden');
                            });
                        });
                    </script>

                    <div class="-mr-2 flex lg:hidden">
                        <!-- Mobile menu button -->
                        <button type="button" id="mobile-menu-button"
                            class="relative inline-flex items-center justify-center rounded-md bg-gray-800 p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800 mr-3"
                            aria-controls="mobile-menu" aria-expanded="false">
                            <span class="absolute -inset-0.5"></span>
                            <span class="sr-only">Open main menu</span>
                            <!-- Menu open: "hidden", Menu closed: "block" -->
                            <svg id="hamburger-icon" class="block h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                            </svg>
                            <!-- Menu open: "block", Menu closed: "hidden" -->
                            <svg id="close-icon" class="hidden h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                </div>
            </div>
            <div id="mobile-menu" class="hidden lg:hidden pb-2">
                <a th:each="navItem: ${navPrime}" th:attr="href=@{${navItem.href.orElse('?')}}, data-privilege=${navItem.text}"
                    th:text="${navItem.text}"
                    class="hidden block text-gray-300 hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-base font-medium">Label</a>
                <a href="https://drh.diabetestechnology.org/" target="_blank"  privilege-action-buttons-links="DRH Home"
                    class="block text-gray-300 hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-base font-medium">DRH
                    Home</a>
                <a href="https://www.diabetestechnology.org/" target="_blank"  privilege-action-buttons-links="DTS Home"
                    class="block text-gray-300 hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-base font-medium">DTS
                    Home</a>
            </div>
    </nav>

    <!-- 
        This <nav id="nav-breadcrumbs"> element contains breadcrumb navigation 
        that is hidden by default but can be edited as a natural template.
        It is filled in by the LayoutAide.breadcrumbs() method.
        -->
    <nav th:fragment="breadcrumbs" id="nav-breadcrumbs"
        class="hidden flex mx-auto max-w-9xl py-2 px-4 sm:px-6 lg:px-8 mt-2" aria-label="Breadcrumb">

        <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
            <li id="breadcrumb-home" class="inline-flex items-center">
                <a href="/home"
                    class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                    <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor" viewBox="0 0 20 20">
                        <path
                            d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                    </svg>
                    Home
                </a>
            </li>
            <!-- this is the "template" which will be cloned for each of the inner breadcrumb items -->
            <li id="breadcrumb-inner-template" class="breadcrumb-inner">
                <div class="flex items-center">
                    <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <a href="#"
                        class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">Inner</a>
                </div>
            </li>
            <li id="breadcrumb-terminal" aria-current="page">
                <div class="flex items-center">
                    <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">Terminal</span>
                </div>
            </li>
        </ol>
    </nav>


    <nav th:fragment="footer" id="nav-footer" class="flex mx-auto max-w-9xl dark:bg-gray-900" aria-label="Footer">
        <div class="mx-auto w-full max-w-9xl p-4 py-6 lg:py-8 sm:px-6 lg:px-8">


            <!-- -->
            <div class="grid grid-cols-12 gap-4">
                <div class="col-span-12 md:col-span-4 lg:col-span-5">
                    <a href="https://drh.diabetestechnology.org/" target="_blank" class="flex items-center"></a>
                    <!-- <img class="h-8 me-3 pl-6" src="/drh-logo.svg" alt="Technology by Design"> -->
                    <div class="whitespace-nowrap dark:text-white text-base font-semibold py-2 ">Diabetes Research
                        Hub
                    </div>
                    <!-- <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">DRH</span> -->
                    </a>
                </div>
                <div class="col-span-12 md:col-span-4 lg:col-span-4">
                    <div>
                        <h2 class="mb-6 text-base font-semibold text-gray-900 uppercase dark:text-white">
                            Documentation
                        </h2>
                        <ul class="text-gray-500 dark:text-gray-400 font-medium">
                            <li class="mb-4">
                                <a href="https://www.diabetestechnology.org/" class="hover:underline"
                                    target="_blank">DTS
                                    Home</a>
                            </li>
                            <li class="mb-4">
                                <a href="/docs" class="hover:underline">OpenAPI UI</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-span-12 md:col-span-4 lg:col-span-3">
                    <div class="justify-items-center">
                        <h5 class="mb-6 text-base font-normal text-gray-900  dark:text-white">
                            Grant Supported By
                        </h5>
                        <a href="https://www.abbott.com/for-healthcare-professionals.html" target="_blank">
                            <img src="/Abbott-Logo.png" alt="Abbott" class="w-20 me-3">
                        </a>
                    </div>
                </div>
            </div>
            <!-- -->

            <hr class="my-6 border-gray-200 sm:mx-auto dark:border-gray-700 lg:my-8" />
            <div class="sm:flex sm:items-center sm:justify-between">
                <span class="text-sm text-gray-500 sm:text-center dark:text-gray-400">© <span
                        th:text="${#dates.format(#dates.createNow(), 'yyyy')}"></span> <a
                        href="https://drh.diabetestechnology.org/" class="hover:underline" target="_blank">Diabetes
                        Research Hub™</a>. All Rights Reserved.
                </span>
                <div class="flex mt-4 sm:justify-center sm:mt-0">
                    <a href="#" class="text-gray-500 hover:text-gray-900 dark:hover:text-white">
                        <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                            viewBox="0 0 21 16">
                            <path
                                d="M16.942 1.556a16.3 16.3 0 0 0-4.126-1.3 12.04 12.04 0 0 0-.529 1.1 15.175 15.175 0 0 0-4.573 0 11.585 11.585 0 0 0-.535-1.1 16.274 16.274 0 0 0-4.129 1.3A17.392 17.392 0 0 0 .182 13.218a15.785 15.785 0 0 0 4.963 2.521c.41-.564.773-1.16 1.084-1.785a10.63 10.63 0 0 1-1.706-.83c.143-.106.283-.217.418-.33a11.664 11.664 0 0 0 10.118 0c.137.113.277.224.418.33-.544.328-1.116.606-1.71.832a12.52 12.52 0 0 0 1.084 1.785 16.46 16.46 0 0 0 5.064-2.595 17.286 17.286 0 0 0-2.973-11.59ZM6.678 10.813a1.941 1.941 0 0 1-1.8-2.045 1.93 1.93 0 0 1 1.8-2.047 1.919 1.919 0 0 1 1.8 2.047 1.93 1.93 0 0 1-1.8 2.045Zm6.644 0a1.94 1.94 0 0 1-1.8-2.045 1.93 1.93 0 0 1 1.8-2.047 1.918 1.918 0 0 1 1.8 2.047 1.93 1.93 0 0 1-1.8 2.045Z" />
                        </svg>
                        <span class="sr-only">Discord community</span>
                    </a>
                    <a href="https://github.com/tech-by-design/polyglot-prime/" target="_blank"
                        class="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                        <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                            viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M10 .333A9.911 9.911 0 0 0 6.866 19.65c.5.092.678-.215.678-.477 0-.237-.01-1.017-.014-1.845-2.757.6-3.338-1.169-3.338-1.169a2.627 2.627 0 0 0-1.1-1.451c-.9-.615.07-.6.07-.6a2.084 2.084 0 0 1 1.518 1.021 2.11 2.11 0 0 0 2.884.823c.044-.503.268-.973.63-1.325-2.2-.25-4.516-1.1-4.516-4.9A3.832 3.832 0 0 1 4.7 7.068a3.56 3.56 0 0 1 .095-2.623s.832-.266 2.726 1.016a9.409 9.409 0 0 1 4.962 0c1.89-1.282 2.717-1.016 2.717-1.016.366.83.402 1.768.1 2.623a3.827 3.827 0 0 1 1.02 2.659c0 3.807-2.319 4.644-4.525 4.889a2.366 2.366 0 0 1 .673 1.834c0 1.326-.012 2.394-.012 2.72 0 .263.18.572.681.475A9.911 9.911 0 0 0 10 .333Z"
                                clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">GitHub account</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>


</body>

</html>