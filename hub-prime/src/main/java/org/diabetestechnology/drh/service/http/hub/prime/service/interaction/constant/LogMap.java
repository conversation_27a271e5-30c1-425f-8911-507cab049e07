package org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant;

import java.util.HashMap;
import java.util.Map;

public class LogMap {
    Map<String, LogDetails> logMap = new HashMap<>();

    public Map<String, LogDetails> getLogMap() {
        // LEVEL 0
        logMap.put(LogMappingUrl.ERROR, new LogDetails(LogName.ERROR, LogType.ERROR, LogDescription.ERROR,
                LogMappingUrl.ERROR, LogLevel.ERROR));

        // LEVEL 1
        logMap.put(LogMappingUrl.SKIP_LOGIN,
                new LogDetails(LogName.SKIP_LOGIN, LogType.SKIP_LOGIN, LogDescription.SKIP_LOGIN,
                        LogMappingUrl.SKIP_LOGIN, LogLevel.SKIP_LOGIN));
        logMap.put(LogMappingUrl.LOGIN, new LogDetails(LogName.LOGIN, LogType.LOGIN, LogDescription.LOGIN,
                LogMappingUrl.LOGIN, LogLevel.LOGIN));
        logMap.put(LogMappingUrl.ORCID_LOGIN,
                new LogDetails(LogName.ORCID_LOGIN, LogType.ORCID_LOGIN, LogDescription.ORCID_LOGIN,
                        LogMappingUrl.ORCID_LOGIN, LogLevel.ORCID_LOGIN));
        logMap.put(LogMappingUrl.GIT_LOGIN,
                new LogDetails(LogName.GIT_LOGIN, LogType.GIT_LOGIN, LogDescription.GIT_LOGIN,
                        LogMappingUrl.GIT_LOGIN, LogLevel.GIT_LOGIN));
        logMap.put(LogMappingUrl.HOME,
                new LogDetails(LogName.HOME, LogType.HOME, LogDescription.HOME, LogMappingUrl.HOME, LogLevel.HOME));
        logMap.put(LogMappingUrl.STUDIES, new LogDetails(LogName.STUDIES, LogType.STUDIES, LogDescription.STUDIES,
                LogMappingUrl.STUDIES, LogLevel.STUDIES));
        logMap.put(LogMappingUrl.COHORT, new LogDetails(LogName.COHORT, LogType.COHORT, LogDescription.COHORT,
                LogMappingUrl.COHORT, LogLevel.COHORT));
        logMap.put(LogMappingUrl.ASK_DRH, new LogDetails(LogName.ASK_DRH, LogType.ASK_DRH, LogDescription.ASK_DRH,
                LogMappingUrl.ASK_DRH, LogLevel.ASK_DRH));
        logMap.put(LogMappingUrl.CONSOLE, new LogDetails(LogName.CONSOLE, LogType.CONSOLE, LogDescription.CONSOLE,
                LogMappingUrl.CONSOLE, LogLevel.CONSOLE));
        logMap.put(LogMappingUrl.DOCUMENTATION, new LogDetails(LogName.DOCUMENTATION, LogType.DOCUMENTATION,
                LogDescription.DOCUMENTATION, LogMappingUrl.DOCUMENTATION, LogLevel.DOCUMENTATION));
        logMap.put(LogMappingUrl.PROFILE, new LogDetails(LogName.PROFILE, LogType.PROFILE, LogDescription.PROFILE,
                LogMappingUrl.PROFILE, LogLevel.PROFILE));
        logMap.put(LogMappingUrl.PROFILE_EMAIL, new LogDetails(LogName.PROFILE_EMAIL, LogType.PROFILE_EMAIL,
                LogDescription.PROFILE_EMAIL, LogMappingUrl.PROFILE_EMAIL, LogLevel.PROFILE_EMAIL));
        logMap.put(LogMappingUrl.VERIFY_OTP, new LogDetails(LogName.VERIFY_OTP, LogType.VERIFY_OTP,
                LogDescription.VERIFY_OTP, LogMappingUrl.VERIFY_OTP, LogLevel.VERIFY_OTP));
        logMap.put(LogMappingUrl.CREATE_USER_PROFILE,
                new LogDetails(LogName.CREATE_USER_PROFILE, LogType.CREATE_USER_PROFILE,
                        LogDescription.CREATE_USER_PROFILE, LogMappingUrl.CREATE_USER_PROFILE,
                        LogLevel.CREATE_USER_PROFILE));
        logMap.put(LogMappingUrl.LOGOUT, new LogDetails(LogName.LOGOUT, LogType.LOGOUT, LogDescription.LOGOUT,
                LogMappingUrl.LOGOUT, LogLevel.LOGOUT));

        // LEVEL 2
        logMap.put(LogMappingUrl.DASHBOARD, new LogDetails(LogName.DASHBOARD, LogType.DASHBOARD,
                LogDescription.DASHBOARD, LogMappingUrl.DASHBOARD, LogLevel.DASHBOARD));
        logMap.put(LogMappingUrl.ALL_STUDIES, new LogDetails(LogName.ALL_STUDIES, LogType.ALL_STUDIES,
                LogDescription.ALL_STUDIES, LogMappingUrl.ALL_STUDIES, LogLevel.ALL_STUDIES));
        logMap.put(LogMappingUrl.POPULATION_PERCENTAGE,
                new LogDetails(LogName.POPULATION_PERCENTAGE, LogType.POPULATION_PERCENTAGE,
                        LogDescription.POPULATION_PERCENTAGE, LogMappingUrl.POPULATION_PERCENTAGE,
                        LogLevel.POPULATION_PERCENTAGE));
        logMap.put(LogMappingUrl.MY_STUDIES, new LogDetails(LogName.MY_STUDIES, LogType.MY_STUDIES,
                LogDescription.MY_STUDIES, LogMappingUrl.MY_STUDIES, LogLevel.MY_STUDIES));

        logMap.put(LogMappingUrl.COHORT, new LogDetails(LogName.COHORT, LogType.COHORT,
                LogDescription.COHORT, LogMappingUrl.COHORT, LogLevel.COHORT));

        logMap.put(LogMappingUrl.ASK_DRH_DATA, new LogDetails(LogName.ASK_DRH_DATA, LogType.ASK_DRH_DATA,
                LogDescription.ASK_DRH_DATA, LogMappingUrl.ASK_DRH_DATA, LogLevel.ASK_DRH_DATA));
        logMap.put(LogMappingUrl.ASK_DRH_RESEARCH_JOURNAL,
                new LogDetails(LogName.ASK_DRH_RESEARCH_JOURNAL, LogType.ASK_DRH_RESEARCH_JOURNAL,
                        LogDescription.ASK_DRH_RESEARCH_JOURNAL, LogMappingUrl.ASK_DRH_RESEARCH_JOURNAL,
                        LogLevel.ASK_DRH_RESEARCH_JOURNAL));
        logMap.put(LogMappingUrl.ASK_DRH_ICODE, new LogDetails(LogName.ASK_DRH_ICODE, LogType.ASK_DRH_ICODE,
                LogDescription.ASK_DRH_ICODE, LogMappingUrl.ASK_DRH_ICODE, LogLevel.ASK_DRH_ICODE));

        logMap.put(LogMappingUrl.CONSOLE_PROJECT, new LogDetails(LogName.CONSOLE_PROJECT, LogType.CONSOLE_PROJECT,
                LogDescription.CONSOLE_PROJECT, LogMappingUrl.CONSOLE_PROJECT, LogLevel.CONSOLE_PROJECT));
        logMap.put(LogMappingUrl.CONSOLE_HEALTH_INFORMATION,
                new LogDetails(LogName.CONSOLE_HEALTH_INFORMATION, LogType.CONSOLE_HEALTH_INFORMATION,
                        LogDescription.CONSOLE_HEALTH_INFORMATION, LogMappingUrl.CONSOLE_HEALTH_INFORMATION,
                        LogLevel.CONSOLE_HEALTH_INFORMATION));
        logMap.put(LogMappingUrl.CONSOLE_SCHEMA, new LogDetails(LogName.CONSOLE_SCHEMA, LogType.CONSOLE_SCHEMA,
                LogDescription.CONSOLE_SCHEMA, LogMappingUrl.CONSOLE_SCHEMA, LogLevel.CONSOLE_SCHEMA));

        logMap.put(LogMappingUrl.DOCUMENTATION_OPEN_API,
                new LogDetails(LogName.DOCUMENTATION_OPEN_API, LogType.DOCUMENTATION_OPEN_API,
                        LogDescription.DOCUMENTATION_OPEN_API, LogMappingUrl.DOCUMENTATION_OPEN_API,
                        LogLevel.DOCUMENTATION_OPEN_API));
        logMap.put(LogMappingUrl.DOCUMENTATION_ANNOUNCEMENTS,
                new LogDetails(LogName.DOCUMENTATION_ANNOUNCEMENTS, LogType.DOCUMENTATION_OPEN_API,
                        LogDescription.DOCUMENTATION_ANNOUNCEMENTS, LogMappingUrl.DOCUMENTATION_ANNOUNCEMENTS,
                        LogLevel.DOCUMENTATION_ANNOUNCEMENTS));

        // LEVEL 3
        logMap.put(LogMappingUrl.STUDIES_INDIVIDUAL,
                new LogDetails(LogName.STUDIES_INDIVIDUAL, LogType.STUDIES_INDIVIDUAL,
                        LogDescription.STUDIES_INDIVIDUAL, LogMappingUrl.STUDIES_INDIVIDUAL,
                        LogLevel.STUDIES_INDIVIDUAL));
        logMap.put(LogMappingUrl.STUDIES_PARTICIPANT,
                new LogDetails(LogName.STUDIES_PARTICIPANT, LogType.STUDIES_PARTICIPANT,
                        LogDescription.STUDIES_PARTICIPANT, LogMappingUrl.STUDIES_PARTICIPANT,
                        LogLevel.STUDIES_PARTICIPANT));
        logMap.put(LogMappingUrl.ALL_STUDIES_CGM, new LogDetails(LogName.ALL_STUDIES_CGM, LogType.ALL_STUDIES_CGM,
                LogDescription.ALL_STUDIES_CGM, LogMappingUrl.ALL_STUDIES_CGM, LogLevel.ALL_STUDIES_CGM));
        logMap.put(LogMappingUrl.STUDIES_CGM, new LogDetails(LogName.STUDIES_CGM, LogType.STUDIES_CGM,
                LogDescription.STUDIES_CGM, LogMappingUrl.STUDIES_CGM, LogLevel.STUDIES_CGM));

        logMap.put(LogMappingUrl.SUCCESSFUL_STUDY_INTERACTION,
                new LogDetails(LogName.SUCCESSFUL_STUDY_INTERACTION, LogType.SUCCESSFUL_STUDY_INTERACTION,
                        LogDescription.SUCCESSFUL_STUDY_INTERACTION, LogMappingUrl.SUCCESSFUL_STUDY_INTERACTION,
                        LogLevel.SUCCESSFUL_STUDY_INTERACTION));
        logMap.put(LogMappingUrl.FAILED_STUDY_INTERACTION,
                new LogDetails(LogName.FAILED_STUDY_INTERACTION, LogType.FAILED_STUDY_INTERACTION,
                        LogDescription.FAILED_STUDY_INTERACTION, LogMappingUrl.FAILED_STUDY_INTERACTION,
                        LogLevel.FAILED_STUDY_INTERACTION));
        logMap.put(LogMappingUrl.SUCCESSFUL_PARTICIPANT_INTERACTION,
                new LogDetails(LogName.SUCCESSFUL_PARTICIPANT_INTERACTION, LogType.SUCCESSFUL_PARTICIPANT_INTERACTION,
                        LogDescription.SUCCESSFUL_PARTICIPANT_INTERACTION,
                        LogMappingUrl.SUCCESSFUL_PARTICIPANT_INTERACTION, LogLevel.SUCCESSFUL_PARTICIPANT_INTERACTION));
        logMap.put(LogMappingUrl.FAILED_PARTICIPANT_INTERACTION,
                new LogDetails(LogName.FAILED_PARTICIPANT_INTERACTION, LogType.FAILED_PARTICIPANT_INTERACTION,
                        LogDescription.FAILED_PARTICIPANT_INTERACTION, LogMappingUrl.FAILED_PARTICIPANT_INTERACTION,
                        LogLevel.FAILED_PARTICIPANT_INTERACTION));

        logMap.put(LogMappingUrl.DATABASE_INTERACTION,
                new LogDetails(LogName.DATABASE_INTERACTION, LogType.DATABASE_INTERACTION,
                        LogDescription.DATABASE_INTERACTION, LogMappingUrl.DATABASE_INTERACTION,
                        LogLevel.DATABASE_INTERACTION));
        logMap.put(LogMappingUrl.SUCCESSFUL_PARTICIPANT_FILE_INTERACTION,
                new LogDetails(LogName.SUCCESSFUL_PARTICIPANT_FILE_INTERACTION,
                        LogType.SUCCESSFUL_PARTICIPANT_FILE_INTERACTION,
                        LogDescription.SUCCESSFUL_PARTICIPANT_FILE_INTERACTION,
                        LogMappingUrl.SUCCESSFUL_PARTICIPANT_FILE_INTERACTION,
                        LogLevel.SUCCESSFUL_PARTICIPANT_FILE_INTERACTION));
        logMap.put(LogMappingUrl.FAILED_PARTICIPANT_FILE_INTERACTION,
                new LogDetails(LogName.FAILED_PARTICIPANT_FILE_INTERACTION, LogType.FAILED_PARTICIPANT_FILE_INTERACTION,
                        LogDescription.FAILED_PARTICIPANT_FILE_INTERACTION,
                        LogMappingUrl.FAILED_PARTICIPANT_FILE_INTERACTION,
                        LogLevel.FAILED_PARTICIPANT_FILE_INTERACTION));
        logMap.put(LogMappingUrl.SUCCESSFUL_CGM_FILE_INTERACTION,
                new LogDetails(LogName.SUCCESSFUL_CGM_FILE_INTERACTION, LogType.SUCCESSFUL_CGM_FILE_INTERACTION,
                        LogDescription.SUCCESSFUL_CGM_FILE_INTERACTION, LogMappingUrl.SUCCESSFUL_CGM_FILE_INTERACTION,
                        LogLevel.SUCCESSFUL_CGM_FILE_INTERACTION));
        logMap.put(LogMappingUrl.FAILED_CGM_FILE_INTERACTION,
                new LogDetails(LogName.FAILED_CGM_FILE_INTERACTION, LogType.FAILED_CGM_FILE_INTERACTION,
                        LogDescription.FAILED_CGM_FILE_INTERACTION, LogMappingUrl.FAILED_CGM_FILE_INTERACTION,
                        LogLevel.FAILED_CGM_FILE_INTERACTION));
        logMap.put(LogMappingUrl.SUCCESSFUL_MEALS_OR_FITNESS_INTERACTION,
                new LogDetails(LogName.SUCCESSFUL_MEALS_OR_FITNESS_INTERACTION,
                        LogType.SUCCESSFUL_MEALS_OR_FITNESS_INTERACTION,
                        LogDescription.SUCCESSFUL_MEALS_OR_FITNESS_INTERACTION,
                        LogMappingUrl.SUCCESSFUL_MEALS_OR_FITNESS_INTERACTION,
                        LogLevel.SUCCESSFUL_MEALS_OR_FITNESS_INTERACTION));
        logMap.put(LogMappingUrl.FAILED_MEALS_OR_FITNESS_INTERACTION,
                new LogDetails(LogName.FAILED_MEALS_OR_FITNESS_INTERACTION, LogType.FAILED_MEALS_OR_FITNESS_INTERACTION,
                        LogDescription.FAILED_MEALS_OR_FITNESS_INTERACTION,
                        LogMappingUrl.FAILED_MEALS_OR_FITNESS_INTERACTION,
                        LogLevel.FAILED_MEALS_OR_FITNESS_INTERACTION));
        logMap.put(LogMappingUrl.USER_SESSION, new LogDetails(LogName.USER_SESSION, LogType.USER_SESSION,
                LogDescription.USER_SESSION, LogMappingUrl.USER_SESSION, LogLevel.USER_SESSION));

        logMap.put(LogMappingUrl.CREATE_STUDY, new LogDetails(LogName.CREATE_STUDY, LogType.CREATE_STUDY,
                LogDescription.CREATE_STUDY, LogMappingUrl.CREATE_STUDY, LogLevel.CREATE_STUDY));

        // LEVEL 4
        logMap.put(LogMappingUrl.AI_ASK_DRH_DATA, new LogDetails(LogName.AI_ASK_DRH_DATA, LogType.AI_ASK_DRH_DATA,
                LogDescription.AI_ASK_DRH_DATA, LogMappingUrl.AI_ASK_DRH_DATA, LogLevel.AI_ASK_DRH_DATA));
        logMap.put(LogMappingUrl.AI_ASK_DRH_RESEARCH_JOURNAL,
                new LogDetails(LogName.AI_ASK_DRH_RESEARCH_JOURNAL, LogType.AI_ASK_DRH_RESEARCH_JOURNAL,
                        LogDescription.AI_ASK_DRH_RESEARCH_JOURNAL, LogMappingUrl.AI_ASK_DRH_RESEARCH_JOURNAL,
                        LogLevel.AI_ASK_DRH_RESEARCH_JOURNAL));
        logMap.put(LogMappingUrl.AI_ASK_DRH_ICODE, new LogDetails(LogName.AI_ASK_DRH_ICODE, LogType.AI_ASK_DRH_ICODE,
                LogDescription.AI_ASK_DRH_ICODE, LogMappingUrl.AI_ASK_DRH_ICODE, LogLevel.AI_ASK_DRH_ICODE));

        logMap.put(LogMappingUrl.UPLOAD_STUDY_DATABASE,
                new LogDetails(LogName.UPLOAD_STUDY_DATABASE, LogType.UPLOAD_STUDY_DATABASE,
                        LogDescription.UPLOAD_STUDY_DATABASE, LogMappingUrl.UPLOAD_STUDY_DATABASE,
                        LogLevel.UPLOAD_STUDY_DATABASE));
        logMap.put(LogMappingUrl.STUDY_SETTINGS, new LogDetails(LogName.STUDY_SETTINGS, LogType.STUDY_SETTINGS,
                LogDescription.STUDY_SETTINGS, LogMappingUrl.STUDY_SETTINGS, LogLevel.STUDY_SETTINGS));
        logMap.put(LogMappingUrl.COLLABORATION_AND_TEAMS,
                new LogDetails(LogName.COLLABORATION_AND_TEAMS, LogType.COLLABORATION_AND_TEAMS,
                        LogDescription.COLLABORATION_AND_TEAMS, LogMappingUrl.COLLABORATION_AND_TEAMS,
                        LogLevel.COLLABORATION_AND_TEAMS));
        logMap.put(LogMappingUrl.PUBLICATION_SETTINGS,
                new LogDetails(LogName.PUBLICATION_SETTINGS, LogType.PUBLICATION_SETTINGS,
                        LogDescription.PUBLICATION_SETTINGS, LogMappingUrl.PUBLICATION_SETTINGS,
                        LogLevel.PUBLICATION_SETTINGS));
        logMap.put(LogMappingUrl.EDIT_PARTICIPANT,
                new LogDetails(LogName.EDIT_PARTICIPANT, LogType.EDIT_PARTICIPANT,
                        LogDescription.EDIT_PARTICIPANT, LogMappingUrl.EDIT_PARTICIPANT,
                        LogLevel.EDIT_PARTICIPANT));
        logMap.put(LogMappingUrl.ADD_PARTICIPANT,
                new LogDetails(LogName.ADD_PARTICIPANT, LogType.ADD_PARTICIPANT,
                        LogDescription.ADD_PARTICIPANT, LogMappingUrl.ADD_PARTICIPANT,
                        LogLevel.ADD_PARTICIPANT));
        logMap.put(LogMappingUrl.PARTICIPANT_CGM_DATA,
                new LogDetails(LogName.PARTICIPANT_CGM_DATA, LogType.PARTICIPANT_CGM_DATA,
                        LogDescription.PARTICIPANT_CGM_DATA, LogMappingUrl.PARTICIPANT_CGM_DATA,
                        LogLevel.PARTICIPANT_CGM_DATA));
        logMap.put(LogMappingUrl.PARTICIPANT_MEALS_DATA,
                new LogDetails(LogName.PARTICIPANT_MEALS_DATA, LogType.PARTICIPANT_MEALS_DATA,
                        LogDescription.PARTICIPANT_MEALS_DATA, LogMappingUrl.PARTICIPANT_MEALS_DATA,
                        LogLevel.PARTICIPANT_MEALS_DATA));
        logMap.put(LogMappingUrl.PARTICIPANT_FITNESS_DATA,
                new LogDetails(LogName.PARTICIPANT_FITNESS_DATA, LogType.PARTICIPANT_FITNESS_DATA,
                        LogDescription.PARTICIPANT_FITNESS_DATA, LogMappingUrl.PARTICIPANT_FITNESS_DATA,
                        LogLevel.PARTICIPANT_FITNESS_DATA));
        // LEVEL 5
        logMap.put(LogMappingUrl.ACTIVITY_LOG, new LogDetails(LogName.ACTIVITY_LOG, LogType.ACTIVITY_LOG,
                LogDescription.ACTIVITY_LOG, LogMappingUrl.ACTIVITY_LOG, LogLevel.ACTIVITY_LOG));

        // LEVEL 6
        logMap.put(LogMappingUrl.SAVE_ACTIVITY_LOG, new LogDetails(LogName.SAVE_ACTIVITY_LOG, LogType.SAVE_ACTIVITY_LOG,
                LogDescription.SAVE_ACTIVITY_LOG, LogMappingUrl.SAVE_ACTIVITY_LOG, LogLevel.SAVE_ACTIVITY_LOG));

        // LEVEL 7

        // LEVEL 8
        logMap.put(LogMappingUrl.SAVE_STUDY, new LogDetails(LogName.SAVE_STUDY, LogType.SAVE_STUDY,
                LogDescription.SAVE_STUDY, LogMappingUrl.SAVE_STUDY, LogLevel.SAVE_STUDY));
        logMap.put(LogMappingUrl.EDIT_STUDY_SETTINGS,
                new LogDetails(LogName.EDIT_STUDY_SETTINGS, LogType.EDIT_STUDY_SETTINGS,
                        LogDescription.EDIT_STUDY_SETTINGS, LogMappingUrl.EDIT_STUDY_SETTINGS,
                        LogLevel.EDIT_STUDY_SETTINGS));

        logMap.put(LogMappingUrl.STUDY_DATA_INLINE_EDIT,
                new LogDetails(LogName.STUDY_DATA_INLINE_EDIT, LogType.STUDY_DATA_INLINE_EDIT,
                        LogDescription.STUDY_DATA_INLINE_EDIT, LogMappingUrl.STUDY_DATA_INLINE_EDIT,
                        LogLevel.STUDY_DATA_INLINE_EDIT));
        logMap.put(LogMappingUrl.CHANGE_STUDY_ARCHIVE_STATUS,
                new LogDetails(LogName.CHANGE_STUDY_ARCHIVE_STATUS, LogType.CHANGE_STUDY_ARCHIVE_STATUS,
                        LogDescription.CHANGE_STUDY_ARCHIVE_STATUS, LogMappingUrl.CHANGE_STUDY_ARCHIVE_STATUS,
                        LogLevel.CHANGE_STUDY_ARCHIVE_STATUS));
        logMap.put(LogMappingUrl.CHANGE_PUBLICATION_AUTHOR,
                new LogDetails(LogName.CHANGE_PUBLICATION_AUTHOR, LogType.CHANGE_PUBLICATION_AUTHOR,
                        LogDescription.CHANGE_PUBLICATION_AUTHOR, LogMappingUrl.CHANGE_PUBLICATION_AUTHOR,
                        LogLevel.CHANGE_PUBLICATION_AUTHOR));
        logMap.put(LogMappingUrl.CHANGE_STUDY_VISIBILITY,
                new LogDetails(LogName.CHANGE_STUDY_VISIBILITY, LogType.CHANGE_STUDY_VISIBILITY,
                        LogDescription.CHANGE_STUDY_VISIBILITY, LogMappingUrl.CHANGE_STUDY_VISIBILITY,
                        LogLevel.CHANGE_STUDY_VISIBILITY));
        logMap.put(LogMappingUrl.CHANGE_STUDY_CITATIONS,
                new LogDetails(LogName.CHANGE_STUDY_CITATIONS, LogType.CHANGE_STUDY_CITATIONS,
                        LogDescription.CHANGE_STUDY_CITATIONS, LogMappingUrl.CHANGE_STUDY_CITATIONS,
                        LogLevel.CHANGE_STUDY_CITATIONS));
        logMap.put(LogMappingUrl.CHANGE_USER_ROLE, new LogDetails(LogName.CHANGE_USER_ROLE, LogType.CHANGE_USER_ROLE,
                LogDescription.CHANGE_USER_ROLE, LogMappingUrl.CHANGE_USER_ROLE, LogLevel.CHANGE_USER_ROLE));
        logMap.put(LogMappingUrl.SAVE_PARTICIPANT, new LogDetails(LogName.SAVE_PARTICIPANT, LogType.SAVE_PARTICIPANT,
                LogDescription.SAVE_PARTICIPANT, LogMappingUrl.SAVE_PARTICIPANT, LogLevel.SAVE_PARTICIPANT));
        logMap.put(LogMappingUrl.SAVE_PROFILE, new LogDetails(LogName.SAVE_PROFILE, LogType.SAVE_PROFILE,
                LogDescription.SAVE_PROFILE, LogMappingUrl.SAVE_PROFILE, LogLevel.SAVE_PROFILE));
        logMap.put(LogMappingUrl.UPLOAD_PARTICIPANT_FILE,
                new LogDetails(LogName.UPLOAD_PARTICIPANT_FILE, LogType.UPLOAD_PARTICIPANT_FILE,
                        LogDescription.UPLOAD_PARTICIPANT_FILE, LogMappingUrl.UPLOAD_PARTICIPANT_FILE,
                        LogLevel.UPLOAD_PARTICIPANT_FILE));
        logMap.put(LogMappingUrl.SAVE_CGM_DATA,
                new LogDetails(LogName.SAVE_CGM_DATA, LogType.SAVE_CGM_DATA,
                        LogDescription.SAVE_CGM_DATA, LogMappingUrl.SAVE_CGM_DATA,
                        LogLevel.SAVE_CGM_DATA));
        logMap.put(LogMappingUrl.SAVE_MEALS_FITNESS_DATA,
                new LogDetails(LogName.SAVE_MEALS_FITNESS_DATA, LogType.SAVE_MEALS_FITNESS_DATA,
                        LogDescription.SAVE_MEALS_FITNESS_DATA, LogMappingUrl.SAVE_MEALS_FITNESS_DATA,
                        LogLevel.SAVE_MEALS_FITNESS_DATA));
        logMap.put(LogMappingUrl.CHANGE_PARTICIPANT_DETAILS,
                new LogDetails(LogName.CHANGE_PARTICIPANT_DETAILS, LogType.CHANGE_PARTICIPANT_DETAILS,
                        LogDescription.CHANGE_PARTICIPANT_DETAILS, LogMappingUrl.CHANGE_PARTICIPANT_DETAILS,
                        LogLevel.CHANGE_PARTICIPANT_DETAILS));
        logMap.put(LogMappingUrl.UPDATE_OR_CHANGE_PARTICIPANT_DETAILS,
                new LogDetails(LogName.UPDATE_OR_CHANGE_PARTICIPANT_DETAILS,
                        LogType.UPDATE_OR_CHANGE_PARTICIPANT_DETAILS,
                        LogDescription.UPDATE_OR_CHANGE_PARTICIPANT_DETAILS,
                        LogMappingUrl.UPDATE_OR_CHANGE_PARTICIPANT_DETAILS,
                        LogLevel.UPDATE_OR_CHANGE_PARTICIPANT_DETAILS));
        logMap.put(LogMappingUrl.SAVE_USER_PROFILE, new LogDetails(LogName.SAVE_USER_PROFILE, LogType.SAVE_USER_PROFILE,
                LogDescription.SAVE_USER_PROFILE, LogMappingUrl.SAVE_USER_PROFILE, LogLevel.SAVE_USER_PROFILE));
        logMap.put(LogMappingUrl.SAVE_COLLABORATION_TEAM,
                new LogDetails(LogName.SAVE_COLLABORATION_TEAM, LogType.SAVE_COLLABORATION_TEAM,
                        LogDescription.SAVE_COLLABORATION_TEAM, LogMappingUrl.SAVE_COLLABORATION_TEAM,
                        LogLevel.SAVE_COLLABORATION_TEAM));
        logMap.put(LogMappingUrl.DELETE_RESEARCH_STUDY,
                new LogDetails(LogName.DELETE_RESEARCH_STUDY, LogType.DELETE_RESEARCH_STUDY,
                        LogDescription.DELETE_RESEARCH_STUDY, LogMappingUrl.DELETE_RESEARCH_STUDY,
                        LogLevel.DELETE_RESEARCH_STUDY));

        return logMap;
    }
}
