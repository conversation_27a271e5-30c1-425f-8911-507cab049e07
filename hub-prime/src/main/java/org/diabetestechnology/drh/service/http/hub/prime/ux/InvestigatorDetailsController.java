package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.pg.service.InteractionService;
import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.diabetestechnology.drh.service.http.pg.Response;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Hidden
public class InvestigatorDetailsController {

    private final Presentation presentation;
    private final InteractionService interactionService;

    public InvestigatorDetailsController(Presentation presentation, InteractionService interactionService) {
        this.presentation = presentation;
        this.interactionService = interactionService;
    }

    @Autowired
    private ResearchStudyService researchStudyService;

    private static final Logger LOG = LoggerFactory.getLogger(MyProfileController.class.getName());

    @GetMapping("/uploadStudy/{studyId}")
    public String uploadStudy(@PathVariable String studyId, Model model,
            final HttpServletRequest request, @RequestParam(required = false) String tab) {
        if (!researchStudyService.isStudyOwner(studyId)) {
            LOG.warn("Access denied: User is not the owner of the study.");
            return "redirect:/study/info/" + studyId + "?tab=" + tab;
        }
        model.addAttribute("studyId", studyId);
        model.addAttribute("tab", tab);
        model.addAttribute("showDatabaseUploadStatus", interactionService.isDbFileInteractionExistsForStudy(studyId));
        if (researchStudyService.getResearchStudyArchiveStatus(studyId)) {
            LOG.warn("Access denied: Study {} is archived.", studyId);

            Response response = Response.builder()
                    .status("failure")
                    .message("The study is archived; edits and updates are not allowed.")
                    .build();

            model.addAttribute("response", response);

            return presentation.populateModel("page/access-denied", model, request);
        }
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        model.addAttribute("studyDisplayId", studyDisplayId);

        return presentation.populateModel("page/investigator/uploadStudy", model,
                request);
    }

    @GetMapping("/mystudies/uploadParticipant/{studyId}/{participantId}")
    public String uploadParticipant(@PathVariable String studyId, @PathVariable String participantId,
            Model model, final HttpServletRequest request, @RequestParam(required = false) String tab) {
        if (!researchStudyService.isStudyOwner(studyId)) {
            LOG.warn("Access denied: User is not the owner of the study.");
            return "redirect:/participants/cgmdata/" + studyId + "/" + participantId + "?tab=" + tab;
        }
        model.addAttribute("studyId", studyId);
        model.addAttribute("participantId", participantId);
        model.addAttribute("tab", tab);
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        String participantDisplayId = researchStudyService.getParticipantDisplayId(participantId);
        if (researchStudyService.getResearchStudyArchiveStatus(studyId)) {
            LOG.warn("Access denied: Study {} is archived.", studyId);

            Response response = Response.builder()
                    .status("failure")
                    .message("The study is archived; edits and updates are not allowed.")
                    .build();

            model.addAttribute("response", response);

            return presentation.populateModel("page/access-denied", model, request);
        }
        model.addAttribute("studyDisplayId", studyDisplayId);
        model.addAttribute("participantDisplayId", participantDisplayId);
        return presentation.populateModel("page/investigator/uploadCGMParticipant", model,
                request);
    }

    @GetMapping("/render-publication-fragment")
    public String renderPublicationFragment(@RequestParam("count") int count,
            @RequestParam("citation_identifier") String citation_identifier,
            Model model) {
        model.addAttribute("count", count);
        model.addAttribute("citation_identifier", citation_identifier);
        return "page/investigator/study :: publicationFragment";
    }
}
