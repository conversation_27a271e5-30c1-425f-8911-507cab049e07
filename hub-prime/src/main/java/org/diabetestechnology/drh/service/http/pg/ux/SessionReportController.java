package org.diabetestechnology.drh.service.http.pg.ux;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.session.SessionDetails;
import org.diabetestechnology.drh.service.http.hub.prime.session.SessionTracker;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.service.SessionReportService;
import org.jooq.JSONB;
import org.jooq.exception.IOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@RequestMapping("/sessions")
@Tag(name = "DRH Hub Session Management API Endpoints")
public class SessionReportController {

    @Autowired
    private SessionTracker sessionTracker;
    @Autowired
    private SessionRegistry sessionRegistry;
    private UserNameService userNameService;
    private static final Logger LOG = LoggerFactory.getLogger(SessionReportController.class);
    ObjectMapper objectMapper = new ObjectMapper();

    private final SessionReportService sessionReportService;

    public SessionReportController(SessionReportService sessionReportService, UserNameService userNameService) {
        this.sessionReportService = sessionReportService;
        this.userNameService = userNameService;
    }

    @GetMapping("/report")
    @ResponseBody
    @Operation(summary = "Session Report", description = "Get a report of all active sessions in the system.")
    public List<SessionDetails> getSessionReport() {
        return sessionTracker.getAllSessions();
    }

    @GetMapping("/active")
    @ResponseBody
    @Operation(summary = "Active Sessions", description = "Get a list of all active user sessions.")
    public List<Object> listLoggedUsers() {
        return sessionRegistry.getAllPrincipals();
    }

    @GetMapping("/details")
    @ResponseBody
    @Operation(summary = "Session Details", description = "Get detailed information about all active sessions.")
    public Response getSessionDetails() {
        Object result = sessionReportService.getSessionDetails();
        return Response.builder()
                .data(Map.of("data",
                        result))
                .status("success")
                .message("Successfully Fetched Session Details")
                .errors(null)
                .build();
    }

    @GetMapping("/organization/report")
    @ResponseBody
    @Operation(summary = "Organization Session Report", description = "Get a report of all active sessions for a specific organization.")
    public Response getOrganizationSessionReport(
            @RequestParam(required = true) String startDate,
            @RequestParam(required = true) String endDate) {
        String organizationId = userNameService.getCurrentUserOrganizationPartyId();
        if (organizationId == null || organizationId.isEmpty()) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Organization ID is not available for the current user.")
                    .errors(null)
                    .build();
        }
        final Boolean isSuperAdmin = userNameService.isSuperAdmin();
        final Boolean isAdmin = userNameService.isAdmin();
        if (!isSuperAdmin && !isAdmin) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("You do not have permission to access this report.")
                    .errors(null)
                    .build();
        }

        JSONB response = sessionReportService.getOrganizationSessionReport(organizationId, startDate, endDate);
        Map<String, Object> responseMap = Map.of();
        if (response != null) {
            try {
                responseMap = objectMapper.readValue(response.data(), new TypeReference<Map<String, Object>>() {
                });
            } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
                LOG.error("Failed to parse JSON response: {}", response.data(), e);
            } catch (IOException e) {
                LOG.error("IO error while parsing JSON response: {}", response.data(), e);
            }
        }
        if ("Success".equalsIgnoreCase((String) responseMap.get("status"))) {
            return Response.builder()
                    .data(responseMap)
                    .status("success")
                    .message("Session report retrieved successfully.")
                    .errors(null)
                    .build();
        } else {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to fetch session report.")
                    .errors(null)
                    .build();
        }
    }

    @GetMapping("/application/report")
    @ResponseBody
    @Operation(summary = "Application Session Report", description = "Get a report of all active sessions for a specific application.")
    public Response getApplicationSessionReport(@RequestParam(required = true) String startDate,
            @RequestParam(required = true) String endDate) {
        final Boolean isSuperAdmin = userNameService.isSuperAdmin();
        if (!isSuperAdmin) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("You do not have permission to access this report.")
                    .errors(null)
                    .build();
        }
        JSONB response = sessionReportService.getApplicationSessionReport(startDate, endDate);
        if (response == null) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("No application session found.")
                    .errors(null)
                    .build();
        }
        Map<String, Object> responseMap = Map.of();
        if (response != null) {
            try {
                responseMap = objectMapper.readValue(response.data(), new TypeReference<Map<String, Object>>() {
                });
            } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
                LOG.error("Failed to parse JSON response: {}", response.data(), e);
            } catch (IOException e) {
                LOG.error("IO error while parsing JSON response: {}", response.data(), e);
            }
        }
        if ("Success".equalsIgnoreCase((String) responseMap.get("status"))) {
            return Response.builder()
                    .data(responseMap)
                    .status("success")
                    .message("Session report retrieved successfully.")
                    .errors(null)
                    .build();
        } else {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to fetch session report.")
                    .errors(null)
                    .build();
        }
    }

}
