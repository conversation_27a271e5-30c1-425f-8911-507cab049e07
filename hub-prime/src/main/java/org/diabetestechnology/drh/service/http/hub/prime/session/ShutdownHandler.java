package org.diabetestechnology.drh.service.http.hub.prime.session;

import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import jakarta.annotation.PreDestroy;

@Component
public class ShutdownHandler {

    private final DSLContext dsl;
    private static final Logger LOG = LoggerFactory.getLogger(ShutdownHandler.class);

    public ShutdownHandler(@Qualifier("secondaryDsl") DSLContext dsl) {
        this.dsl = dsl;
    }

    @PreDestroy
    public void onShutdown() {
        LOG.info("All Session destroyed.");
        var fnResponse = dsl.select(DSL.field(
                "drh_stateless_activity_audit.end_active_session_audit_log()"))
                .fetchOneInto(org.jooq.JSONB.class);
        LOG.info("Session activity updated on destroy: {}", fnResponse);
    }
}
