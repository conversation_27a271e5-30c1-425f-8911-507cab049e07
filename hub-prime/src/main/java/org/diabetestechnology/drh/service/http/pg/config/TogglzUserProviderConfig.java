package org.diabetestechnology.drh.service.http.pg.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.togglz.core.user.FeatureUser;
import org.togglz.core.user.UserProvider;
import org.diabetestechnology.drh.service.http.pg.service.PractitionerService;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Configuration
public class TogglzUserProviderConfig {

    private final PractitionerService practitionerService;
    private static final Logger LOG = LoggerFactory.getLogger(TogglzUserProviderConfig.class);

    public TogglzUserProviderConfig(@Lazy PractitionerService practitionerService) {
        this.practitionerService = practitionerService;
    }

    @Bean
    public UserProvider userProvider() {
        return new UserProvider() {
            @Override
            public FeatureUser getCurrentUser() {
                String userId = null;

                try {
                    JSONB userJson = practitionerService.getLoggedInUserDetails();

                    if (userJson != null && !userJson.data().isEmpty()) {
                        ObjectMapper objectMapper = new ObjectMapper();
                        JsonNode root = objectMapper.readTree(userJson.data());
                        userId = root.path("provider_user_id").asText();

                        JsonNode rolesNode = root.path("user_roles");
                        boolean isAdmin = false;

                        if (rolesNode.isArray()) {
                            for (JsonNode role : rolesNode) {
                                String roleName = role.path("role_name").asText();
                                if ("Super Admin".equalsIgnoreCase(roleName)) {
                                    isAdmin = true;
                                    break;
                                }
                            }
                        }

                        String finalUserId = userId;
                        boolean finalIsAdmin = isAdmin;
                        return new FeatureUser() {
                            @Override
                            public String getName() {
                                return finalUserId != null ? finalUserId : "anonymous";
                            }

                            @Override
                            public boolean isFeatureAdmin() {
                                return finalIsAdmin;
                            }

                            @Override
                            public String getAttribute(String name) {
                                return null;
                            }
                        };
                    }
                } catch (Exception e) {
                    System.out.println("[TogglzUserProvider] Exception: " + e.getMessage());
                    LOG.error("Error fetching logged in user details: {}", e.getMessage(), e);
                }

                return new FeatureUser() {

                    @Override
                    public String getName() {
                        return "anonymous";
                    }

                    @Override
                    public boolean isFeatureAdmin() {
                        return false;
                    }

                    @Override
                    public String getAttribute(String name) {
                        return null;
                    }

                };
            }
        };
    }

}