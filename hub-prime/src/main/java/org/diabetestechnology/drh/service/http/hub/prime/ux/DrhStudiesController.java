package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.route.RouteMapping;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.aspect.RequireFeature;
import org.diabetestechnology.drh.service.http.pg.aspect.RequiresUserPermission;
import org.diabetestechnology.drh.service.http.pg.constant.UserVerificationStatus;
import org.diabetestechnology.drh.service.http.pg.service.MasterService;
import org.diabetestechnology.drh.service.http.pg.service.PractitionerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.servlet.http.HttpServletRequest;

@Controller
public class DrhStudiesController {

    private static final Logger LOG = LoggerFactory.getLogger(DrhStudiesController.class.getName());

    private final Presentation presentation;
    private final UserNameService userNameService;
    private final MasterService masterService;

    public DrhStudiesController(Presentation presentation, UserNameService userNameService,
            MasterService masterService) {
        this.presentation = presentation;
        this.userNameService = userNameService;
        this.masterService = masterService;
    }

    @Autowired
    PractitionerService practitionerService;

    @RouteMapping(label = "Studies", siblingOrder = 20)
    @RequiresUserPermission(permission = "STUDIES")
    @RequireFeature(feature = "STUDIES")
    @GetMapping("/studies")
    public String studiesHome() {
        return "redirect:/studies/dashboard";
    }

    @Hidden
    @GetMapping("/studies/dashboard")
    @RequiresUserPermission(permission = "DASHBOARD")
    @RouteMapping(label = "Dashboard", siblingOrder = 0)
    public String studiesDashboard(Model model, final HttpServletRequest request) {
        LOG.info("Read All Studies");
        // Descreption to show in UI
        String[] pageDescription = {
                "This page provides an organized, sortable list of all research studies in the Diabetes Research Hub, offering quick access to key metrics and detailed study information."
        };
        String[] pageSubDescriptiontitle = {
                "Displays essential data, such as:",
        };
        String[] pageSubDescription = {
                "Number of participants",
                "Gender distribution",
                "Average participant age"
        };
        String[] pageAttributestitle = {
                "The page is powered by AG Grid, allowing users to:" };
        String[] pageAttributes = {
                "Filter, sort, and adjust column visibility for a personalized view.",
                "Access associated source files and participant metrics by clicking on individual study or participant IDs for further details." };
        String[] notes = {
                "The loading time for the Consolidated Metrics is currently a bit high due to data fetching and calculations. It needs optimization."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("pagesubdescriptiontitle", pageSubDescriptiontitle);
        model.addAttribute("pagesubdescription", pageSubDescription);
        model.addAttribute("pageattributestitle", pageAttributestitle);
        model.addAttribute("pageattributes", pageAttributes);
        model.addAttribute("notes", notes);
        return presentation.populateModel("page/investigator/studyDashboard", model, request);
    }

    @Hidden
    @GetMapping("/studies/all")
    @RouteMapping(label = "All Studies", siblingOrder = 30)
    @RequiresUserPermission(permission = "ALL_STUDIES")
    public String studiesFinal(Model model, final HttpServletRequest request) {
        LOG.info("Read All Studies");
        if (presentation.isAuthenticatedUser()) {
            model.addAttribute("isAuthenticated", true);
            model.addAttribute("isProfileCompleted", practitionerService.isUserExists());
            model.addAttribute("isSuperAdmin", presentation.isSuperAdmin());
            model.addAttribute("Organization", practitionerService.getUserOrganization());
            final var providerId = userNameService.getUserId();
            final var statusId = masterService.getUserVerificationStatusId(UserVerificationStatus.COMPLETED);
            Boolean isUserVerified = practitionerService.isUserVerified(providerId, statusId);
            model.addAttribute("isUserVerified", isUserVerified);

        } else {
            model.addAttribute("isAuthenticated", false);
            model.addAttribute("isProfileCompleted", false);
            model.addAttribute("isSuperAdmin", false);
            model.addAttribute("isUserVerified", false);

        }
        return presentation.populateModel("page/investigator/allStudies", model, request);
    }

    @Hidden
    @GetMapping("/studies/population")
    @RequiresUserPermission(permission = "POPULATION_PERCENTAGE")
    @RouteMapping(label = "Population Percentage", siblingOrder = 20)
    public String studiesPopulation(Model model, final HttpServletRequest request) {
        LOG.info("Read Studies And Details");
        // Descreption to show in UI
        String[] pageDescription = {
                "This page allows users to filter studies based on participant characteristics and glycemic metrics, dynamically generating results."
        };
        String[] pageSubDescriptionTitle = {
                "Users can filter studies based on criteria such as:",
        };
        String[] pageSubDescription = {
                "Age",
                "Time in Range (TIR)",
                "Glycemic Risk Index (GRI)",
                "HbA1c",
                "Gender",
                "Days of Wear for CGM devices",
        };
        String[] pageAttributesTitle = {
                "Results are generated in real-time, displaying population percentages for quick insights.",
                "Users can click on individual study IDs to access in-depth information, and all interactions are powered by AG Grid for smooth, interactive navigation." };
        String[] notes = {
                "The loading time for the Consolidated Metrics is currently a bit high due to data fetching and calculations. It needs optimization."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("pagesubdescription", pageSubDescription);
        model.addAttribute("pagesubdescriptiontitle", pageSubDescriptionTitle);
        model.addAttribute("pageattributestitle", pageAttributesTitle);
        model.addAttribute("notes", notes);
        return presentation.populateModel("page/investigator/populationPercentage", model, request);
    }

    @Hidden
    @GetMapping("/studies/mystudies")
    @RequiresUserPermission(permission = "MY_STUDIES")
    @RouteMapping(label = "My Studies", siblingOrder = 40)
    public String myStudies(Model model, final HttpServletRequest request) {
        if (presentation.isAuthenticatedUser()) {
            model.addAttribute("isAuthenticated", true);
            model.addAttribute("isProfileCompleted", practitionerService.isUserExists());
            model.addAttribute("isSuperAdmin", presentation.isSuperAdmin());
            model.addAttribute("Organization", practitionerService.getUserOrganization());
        } else {
            model.addAttribute("isAuthenticated", false);
            model.addAttribute("isProfileCompleted", false);
            model.addAttribute("isSuperAdmin", false);
        }
        return presentation.populateModel("page/investigator/myStudies", model, request);
    }
}
