package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.route.RouteMapping;
import org.diabetestechnology.drh.service.http.pg.aspect.RequireFeature;
import org.diabetestechnology.drh.service.http.pg.aspect.RequiresUserPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Tag(name = "Administrator", description = "Administrator API")
public class AdministratorController {
    private final Presentation presentation;
    private static final Logger LOG = LoggerFactory.getLogger(AdministratorController.class);

    public AdministratorController(Presentation presentation) {
        this.presentation = presentation;
    }

    @RouteMapping(label = "Administration", siblingOrder = 140)
    @GetMapping("/administration")
    @PreAuthorize("hasRole('DEVELOPER') and hasAuthority('ADD_NEW_USER')")
    public String viewAuditLogs(Model model) {
        return "redirect:/administration/info";
    }

    @GetMapping("/administration/info")
    @RouteMapping(label = "User Settings", title = "User Settings", siblingOrder = 0)
    public String svmFinal(Model model, final HttpServletRequest request) {
        LOG.info("Read User Settings");
        if (presentation.isAdmin()
                || presentation.isSuperAdmin()) {
            return presentation.populateModel("page/investigator/usersettings", model, request);
        } else {
            return presentation.populateModel("page/access-denied", model, request);
        }
    }

}
