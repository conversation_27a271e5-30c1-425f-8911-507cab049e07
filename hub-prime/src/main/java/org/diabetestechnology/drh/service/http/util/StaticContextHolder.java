package org.diabetestechnology.drh.service.http.util;

import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

@Component
public class StaticContextHolder {
    @Autowired
    @Qualifier("secondaryDsl")
    private DSLContext injectedDsl;

    private static DSLContext dsl;

    @PostConstruct
    public void init() {
        dsl = injectedDsl;
    }

    public static DSLContext getDsl() {
        return dsl;
    }
}
