package org.diabetestechnology.drh.service.http.pg.service;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.annotation.PostConstruct;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetUrlRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.request.DatabaseMigrationRequest;
import org.jooq.JSONB;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;

@Service
public class S3FileUploadService {
    private static final Logger LOG = LoggerFactory.getLogger(S3FileUploadService.class);

    private static final List<Character> POSSIBLE_DELIMITERS = List.of(',', '|', '\t', ';', ':', ' ');

    @Value("${aws.s3.bucket}")
    private String bucketName;

    @Value("${aws.region}")
    private String region;

    @Value("${aws.credentials.access-key}")
    private String accessKey;

    @Value("${aws.credentials.secret-key}")
    private String secretKey;

    private S3Client s3Client;

    @Value("${ORG_DRH_SERVICE_HTTP_TEMPORARY_FILE_PATH:}")
    private String tempFileLocation;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("#{T(org.springframework.util.StringUtils).hasText('${spring.profiles.active}') && '${spring.profiles.active}' == 'sandbox'}")
    private boolean isSandbox;

    @Autowired
    MasterService masterService;

    @PostConstruct
    public void initialize() {
        AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKey, secretKey);
        this.s3Client = S3Client.builder()
                .region(Region.of(region))
                .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                .build();
    }

    public Response uploadFile(MultipartFile file) throws IOException {

        LOG.info("Uploading file: {}", file.getOriginalFilename());

        // Check if file is empty
        if (file.isEmpty()) {
            return Response.builder()
                    .data(Map.of())
                    .status("success")
                    .message("File is empty")
                    .errors(null)
                    .build();
        }

        // Check if the file has a CSV extension
        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.toLowerCase().endsWith(".csv")) {
            return Response.builder()
                    .data(Map.of())
                    .status("success")
                    .message(
                            "Invalid file type. Only CSV files are allowed.")
                    .errors(null)
                    .build();
        }

        try {
            // Optionally, check the content type (not always reliable, but helps in some
            // cases)
            String contentType = file.getContentType();
            if (contentType != null && !contentType.equals("text/csv")) {
                return Response.builder()
                        .data(Map.of())
                        .status("success")
                        .message(
                                "Invalid content type. Only CSV files are allowed.")
                        .errors(null)
                        .build();
            }

            // Generate a unique file name for S3
            String s3FileName = "participants/" + generateUniqueFileName(fileName);

            // Upload the CSV file to S3 bucket
            s3Client.putObject(
                    PutObjectRequest.builder()
                            .bucket(bucketName)
                            .key(s3FileName) // Set the unique file name on S3
                            .contentType(file.getContentType()) // Set the content type as text/csv
                            .build(),
                    RequestBody.fromBytes(file.getBytes())); // The actual file content

            // Generate the file's URL after upload
            URL fileUrl = s3Client.utilities().getUrl(GetUrlRequest.builder()
                    .bucket(bucketName)
                    .key(s3FileName)
                    .build());

            return Response.builder()
                    .data(Map.of("fileURL", fileUrl.toString()))
                    .status("success")
                    .message(
                            "File Uploaded")
                    .errors(null)
                    .build();
        } catch (S3Exception e) {
            LOG.error("Error uploading file to S3", e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message(
                            "Error uploading file to S3.")
                    .errors(e.getMessage())
                    .build();
        } catch (Exception e) {
            LOG.error("Unexpected error uploading file to S3", e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message(
                            "Unexpected error uploading file to S3.")
                    .errors(e.getMessage())
                    .build();
        }
    }

    private String generateUniqueFileName(String originalFilename) {
        // String uniqueId = UUID.randomUUID().toString();
        // return uniqueId + "_" + originalFilename;
        int extensionIndex = originalFilename.lastIndexOf('.');
        String baseName = (extensionIndex > 0) ? originalFilename.substring(0, extensionIndex) : originalFilename;
        String extension = (extensionIndex > 0) ? originalFilename.substring(extensionIndex) : "";
        String timestamp = String.valueOf(System.currentTimeMillis());
        return baseName + "_" + timestamp + extension;
    }

    public byte[] convertToByteArray(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IOException("File does not exist");
        }

        return Files.readAllBytes(file.toPath());

    }

    public String saveFileToTempLocation(MultipartFile file) throws IOException {
        LOG.info("Saving file to temp location: {}", file.getOriginalFilename());
        File directory = new File(tempFileLocation);
        if (!directory.exists()) {
            directory.mkdirs(); // Create directories if they don't exist
        }

        // Create a temporary file in the system's temporary directory
        Path tempFile = Path.of(tempFileLocation, "temp-" + file.getOriginalFilename());
        LOG.info("Saving file to temp location: {}", tempFile.toString());
        // Save the file to the temporary location
        file.transferTo(tempFile.toFile());

        // Return the path as a string
        return tempFile.toString();
    }

    public void deleteTempFile(String filePath) {
        File file = new File(filePath);
        if (file.exists() && file.isFile()) {
            boolean deleted = file.delete();
            if (!deleted) {
                // Handle failure to delete (optional)
                System.out.println("Failed to delete temp file: " + filePath);
            }
        }
    }

    public String validateParticipantFileJson(ArrayNode jsonArray) {
        int rowIndex = 0;
        for (JsonNode node : jsonArray) {
            rowIndex++;
            String participantId = node.path("participant_id").asText();
            String age = node.path("age").asText();
            String gender = node.path("gender").asText();

            // Check for null or empty values
            if (participantId == null || participantId.isEmpty()) {
                System.out.println("participant_id is missing or empty for row " + rowIndex + " .");
                return "participant_id is missing or empty for row " + rowIndex + " .";
            }
            if (age == null || age.isEmpty()) {
                System.out.println("age is missing or empty for row " + rowIndex + " .");
                return "age is missing or empty for row " + rowIndex + " .";
            }
            if (age != null) {
                try {
                    Integer.parseInt(age); // or Double.parseDouble(age) if decimals are allowed
                    // If successful, it's a valid number
                } catch (NumberFormatException e) {
                    System.out.println("Age is not a valid number for row " + rowIndex + ".");
                    return "Age is not a valid number for row " + rowIndex + ".";
                }
            }
            if (gender == null || gender.isEmpty()) {
                System.out.println("gender is missing or empty for row " + rowIndex + " .");
                return "gender is missing or empty for row " + rowIndex + " .";
            }
            LOG.info("Validating row: {}", node.toString());
            LOG.info("Gender: {}", gender);

            if (gender != null) {
                String genderResponse = validateGender(rowIndex, gender);
                ((ObjectNode) node).put("gender", genderResponse);
            }
        }
        return "All records are valid.";
    }

    public String uploadCgmFile(MultipartFile file, String orgId, String studyId, String safeFileName) {
        if (isSandbox) {
            LOG.info("Active Profile is Sandbox");
            return "dummyUrl";
        } else {
            LOG.info("Uploading file: {}", file.getOriginalFilename());

            // Check if file is empty
            if (file.isEmpty()) {
                LOG.error("File is empty");
            }

            // Check if the file has a CSV extension
            String fileName = safeFileName;

            try {

                // Generate a unique file name for S3
                String s3FileName = orgId + "/" + studyId + "/cgm-meta-data/" + generateUniqueFileName(fileName);

                // Upload the CSV file to S3 bucket
                s3Client.putObject(
                        PutObjectRequest.builder()
                                .bucket(bucketName)
                                .key(s3FileName) // Set the unique file name on S3

                                .build(),
                        RequestBody.fromInputStream(file.getInputStream(), file.getSize())); // The actual file content

                // Generate the file's URL after upload
                URL fileUrl = s3Client.utilities().getUrl(GetUrlRequest.builder()
                        .bucket(bucketName)
                        .key(s3FileName)
                        .build());

                return fileUrl.toString();
            } catch (S3Exception e) {
                LOG.error("Error uploading file to S3", e);

            } catch (Exception e) {
                LOG.error("Unexpected error uploading file to S3", e);

            }
            return null;
        }
    }

    public String uploadParticipantFileToS3Bucket(MultipartFile file, String orgId, String studyId, String safeFileName)
            throws IOException {
        if (isSandbox) {
            LOG.info("Active Profile is Sandbox");
            return "dummyUrl";
        } else {
            LOG.info("Uploading file: {}", file.getOriginalFilename());

            // String fileName = file.getOriginalFilename();
            String fileName = safeFileName;

            try {

                // Generate a unique file name for S3
                String s3FileName = orgId + "/" + studyId + "/participants/" + generateUniqueFileName(fileName);

                // Upload the CSV file to S3 bucket
                s3Client.putObject(
                        PutObjectRequest.builder()
                                .bucket(bucketName)
                                .key(s3FileName) // Set the unique file name on S3
                                .contentType(file.getContentType()) // Set the content type as text/csv
                                .build(),
                        RequestBody.fromInputStream(file.getInputStream(), file.getSize())); // The actual file content

                // Generate the file's URL after upload
                URL fileUrl = s3Client.utilities().getUrl(GetUrlRequest.builder()
                        .bucket(bucketName)
                        .key(s3FileName)
                        .build());

                return fileUrl.toString();
            } catch (S3Exception e) {
                LOG.error("Error uploading file to S3", e);

            } catch (Exception e) {
                LOG.error("Unexpected error uploading file to S3", e);

            }
            return null;
        }
    }

    public byte[] generateCsvTemplate() {
        String csvContent = "participant_id,diagnosis_icd,med_rxnorm,treatment_modality,gender,race,ethnicity,age,bmi,baseline_hba1c,diabetes_type,study_arm\n";
        try {
            LOG.info("Generating CSV template...");
            return csvContent.getBytes();
        } catch (Exception e) {
            LOG.error("Error occurred while generating CSV template.", e);
            throw new RuntimeException("Failed to generate CSV template", e);
        }
    }

    public String validateGender(int rowIndex, String gender) {
        // Fetch the possible gender values from masterDataService
        JSONB genderData = masterService.readGenderType();

        // Parse the JSONB object to extract valid gender values
        List<String> validGenders = extractValidGenders(genderData);
        Optional<String> unknownGender = validGenders.stream()
                .filter(dbGender -> dbGender.equalsIgnoreCase("unknown"))
                .findFirst();
        if (gender != null) {
            Optional<String> matchedGender = validGenders.stream()
                    .filter(dbGender -> dbGender.equalsIgnoreCase(gender))
                    .findFirst();

            if (matchedGender.isPresent()) {
                System.out.println("Input gender '" + gender + "' matches DB value: " + matchedGender.get());
                return matchedGender.get(); // Gender is valid
            } else {

                System.out.println("Input gender '" + gender + "' matches DB value: " + unknownGender.get());
                System.out.println("Gender is not valid for row " + rowIndex
                        + ". It should be one of: " + String.join(", ", validGenders));
                return unknownGender.get();
            }
        } else {
            System.out.println("Gender is null for row " + rowIndex + ".");
            return unknownGender.get();
        }
    }

    private List<String> extractValidGenders(JSONB genderData) {
        if (genderData == null || genderData.data().isBlank() || genderData.data().equals("null")) {
            throw new IllegalStateException("No valid gender data found in masterDataService.");
        }

        try {
            // Parse the JSONB data into a list of maps
            List<Map<String, Object>> genderList = objectMapper.readValue(
                    genderData.data(), new TypeReference<List<Map<String, Object>>>() {
                    });

            // Extract the "value" field from each map
            return genderList.stream()
                    .map(entry -> entry.get("value").toString())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse gender data from JSONB", e);
        }
    }

    public JSONArray processContent(String filePath) throws Exception {
        final Logger LOG = LoggerFactory.getLogger(this.getClass());

        try {
            Path path = Path.of(filePath);
            String contentType = Files.probeContentType(path);
            byte[] fileBytes = Files.readAllBytes(path);
            String fileName = path.getFileName().toString();

            if ("text/csv".equals(contentType) || fileName.endsWith(".csv") || fileName.endsWith(".txt")) {
                return convertJsonNodeToJSONArray(convertCsvToJson(filePath));
            } else if ("application/vnd.ms-excel".equals(contentType)
                    || "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(contentType)) {
                return parseExcelToJSONArray(fileBytes);
            } else if ("text/plain".equals(contentType)) {
                return parseTextToJSONArray(fileBytes);
            } else if ("application/xml".equals(contentType)) {
                return parseXmlToJSONArray(fileBytes);
            } else if ("application/json".equals(contentType) || fileName.endsWith(".json")) {
                return null;
            } else if ("json".equals(contentType)) {
                return null;
            }
            // Unsupported file type
            // throw new UnsupportedOperationException("Unsupported file type: " +
            // contentType);
            return null;
        } catch (UnsupportedOperationException e) {
            LOG.error("Unsupported file type error: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            LOG.error("Error processing file content: {}", e.getMessage(), e);
            throw new RuntimeException("Error processing file content: " + e.getMessage());
        }
    }

    private JSONArray parseXmlToJSONArray(byte[] fileBytes) {
        try {
            // Convert byte array to string
            String xmlContent = new String(fileBytes, StandardCharsets.UTF_8);

            // Convert XML to JSONObject
            JSONObject jsonObject = XML.toJSONObject(xmlContent);

            // Extract the relevant JSON array directly
            if (jsonObject.has("Records")) {
                Object records = jsonObject.get("Records");
                if (records instanceof JSONObject && ((JSONObject) records).has("Record")) {
                    Object recordObject = ((JSONObject) records).get("Record");
                    return recordObject instanceof JSONArray ? (JSONArray) recordObject
                            : new JSONArray().put(recordObject);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return new JSONArray(); // Return an empty array in case of failure
    }

    private char detectDelimiter(List<String> lines) {
        // Use var and specify generics explicitly for the HashMap
        var delimiterCountMap = new HashMap<Character, Integer>();
        for (char delimiter : POSSIBLE_DELIMITERS) {
            delimiterCountMap.put(delimiter, 0);
        }

        int linesToCheck = Math.min(5, lines.size());
        for (int i = 0; i < linesToCheck; i++) {
            String line = lines.get(i);
            for (char delimiter : POSSIBLE_DELIMITERS) {
                // Count delimiters that are not inside double quotes
                int count = countDelimitersOutsideQuotes(line, delimiter);
                delimiterCountMap.put(delimiter, delimiterCountMap.get(delimiter) + count);
            }
        }
        return Collections.max(delimiterCountMap.entrySet(), Map.Entry.comparingByValue()).getKey();
    }

    private int countDelimitersOutsideQuotes(String line, char delimiter) {
        boolean insideQuotes = false;
        int count = 0;
        for (char c : line.toCharArray()) {
            if (c == '"') {
                insideQuotes = !insideQuotes; // Toggle insideQuotes
            } else if (c == delimiter && !insideQuotes) {
                count++;
            }
        }
        return count;
    }

    private JSONArray parseLinesToJSONArray(List<String> lines, char delimiter) {
        LOG.info("Delimiter: {}", delimiter);
        JSONArray jsonArray = new JSONArray();
        if (lines.isEmpty()) {
            return jsonArray;
        }

        // Split the header line first
        String[] headers = lines.get(0).split("\\" + delimiter); // Escape delimiter for regex
        for (int i = 1; i < lines.size(); i++) {
            String line = lines.get(i);

            // Process the line considering quoted values
            var values = parseLineWithQuotes(line, delimiter);

            // Create a JSON object for this line
            JSONObject jsonObject = new JSONObject();
            for (int j = 0; j < headers.length; j++) {
                jsonObject.put(headers[j].trim(), j < values.size() ? values.get(j).trim() : "");
            }
            jsonArray.put(jsonObject);
        }
        return jsonArray;
    }

    // TO DO
    private JSONArray parseExcelToJSONArray(byte[] fileBytes) throws IOException {
        JSONArray jsonArray = new JSONArray();

        try (InputStream inputStream = new ByteArrayInputStream(fileBytes)) {
            Workbook workbook;
            if (fileBytes[0] == (byte) 0x50 && fileBytes[1] == (byte) 0x4B) {
                workbook = new XSSFWorkbook(inputStream); // .xlsx file
            } else {
                workbook = new HSSFWorkbook(inputStream); // .xls file
            }

            try (workbook) {
                Sheet sheet = workbook.getSheetAt(0); // Read first sheet

                List<String> headers = new ArrayList<>();
                boolean isHeaderRow = true;

                for (Row row : sheet) {
                    if (isHeaderRow) {
                        // Extract column names from the first row
                        for (Cell cell : row) {
                            headers.add(cell.getStringCellValue().trim());
                        }
                        isHeaderRow = false; // Next rows are data rows
                    } else {
                        JSONObject jsonObject = new JSONObject();
                        int cellIndex = 0;

                        for (Cell cell : row) {
                            if (cellIndex < headers.size()) {
                                jsonObject.put(headers.get(cellIndex), getCellValue(cell));
                            }
                            cellIndex++;
                        }

                        jsonArray.put(jsonObject);
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("Invalid Excel format.", e);
        }

        return jsonArray;
    }

    private String getCellValue(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                }
                return String.valueOf((int) cell.getNumericCellValue()); // Convert to integer if possible
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    private JSONArray parseTextToJSONArray(byte[] fileBytes) throws IOException {
        JSONArray jsonArray = new JSONArray();
        var content = new String(fileBytes, StandardCharsets.UTF_8);
        var lines = content.split("\n");

        // Example: Line-based parsing into key-value pairs
        for (String line : lines) {
            JSONObject jsonObject = new JSONObject();
            var parts = line.split(":"); // Assuming "key: value" format
            if (parts.length == 2) {
                jsonObject.put(parts[0].trim(), parts[1].trim());
                jsonArray.put(jsonObject);
            }
        }
        return jsonArray;
    }

    private List<String> parseLineWithQuotes(String line, char delimiter) {
        var values = new ArrayList<String>();
        var value = new StringBuilder();
        boolean insideQuotes = false;

        // Iterate through each character in the line
        for (char c : line.toCharArray()) {
            if (c == '"') {
                // Toggle insideQuotes when encountering a quote
                insideQuotes = !insideQuotes;
            } else if (c == delimiter && !insideQuotes) {
                // If outside quotes and delimiter is found, push current value to values
                values.add(value.toString().trim());
                value = new StringBuilder(); // Reset value
            } else {
                // Add character to current value
                value.append(c);
            }
        }
        // Add the last value (after the last delimiter)
        values.add(value.toString().trim());
        return values;
    }

    public String uploadDBFileToS3(MultipartFile file, DatabaseMigrationRequest request) {
        if (isSandbox) {
            LOG.info("Active Profile is Sandbox");
            return "dummyUrl";
        } else {
            final var fileName = file.getOriginalFilename();
            // Generate a unique file name for S3
            String s3FileName = request.organizationPartyId() + "/" + request.studyId() + "/Database/"
                    + generateUniqueFileName(fileName);
            try {
                if (!isValidSQLiteDB(file)) {
                    throw new IllegalArgumentException("Invalid database file. Please upload a valid SQLite database.");
                }

                // Upload the DB file to S3 bucket

                s3Client.putObject(
                        PutObjectRequest.builder()
                                .bucket(bucketName)
                                .key(s3FileName) // Set the unique file name on S3
                                .build(),
                        RequestBody.fromInputStream(file.getInputStream(), file.getSize())); // The actual file content

                // Generate the file's URL after upload
                URL fileUrl = s3Client.utilities().getUrl(GetUrlRequest.builder()
                        .bucket(bucketName)
                        .key(s3FileName)
                        .build());
                return fileUrl.toString();
            } catch (Exception e) {
                LOG.error("Error uploading file to S3: {}", e.getMessage(), e);
                throw new RuntimeException("Error uploading file to S3: " + e.getMessage());
            }
        }
    }

    private boolean isValidSQLiteDB(MultipartFile file) {
        LOG.info("Validating SQLite database file: {}", file.getOriginalFilename());
        try (InputStream inputStream = file.getInputStream()) {
            byte[] header = new byte[16];
            if (inputStream.read(header) != 16) {
                return false;
            }
            String headerString = new String(header, StandardCharsets.US_ASCII);
            return headerString.startsWith("SQLite format 3");
        } catch (IOException e) {
            LOG.error("Error reading file header: {}", e.getMessage(), e);
            return false;
        }
    }

    public JsonNode convertCsvToJson(String filePath) throws IOException {
        // Read the CSV file
        BufferedReader reader = new BufferedReader(new FileReader(filePath));
        String line;
        List<Map<String, String>> csvData = new ArrayList<>();

        // Possible delimiters for CSV
        String[] possibleDelimiters = { ",", "\t", ";", "|", ":" };

        String detectedDelimiter = null;

        // Check for the delimiter by scanning the first line
        if ((line = reader.readLine()) != null) {
            for (String delimiter : possibleDelimiters) {
                if (line.contains(delimiter)) {
                    detectedDelimiter = delimiter;
                    break;
                }
            }
        }

        if (detectedDelimiter == null) {
            throw new IllegalArgumentException("No valid delimiter found in the CSV file.");
        }

        // Process the first line as header
        String[] headers = line.split(Pattern.quote(detectedDelimiter));

        // Read the remaining lines
        while ((line = reader.readLine()) != null) {
            String[] values = line.split(Pattern.quote(detectedDelimiter));
            Map<String, String> rowData = new HashMap<>();
            for (int i = 0; i < headers.length; i++) {
                if (i < values.length) {
                    rowData.put(headers[i], values[i]);
                } else {
                    rowData.put(headers[i], "");
                }
            }
            csvData.add(rowData);
        }

        reader.close();

        // Convert list of maps to JSON using Jackson
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.valueToTree(csvData);
    }

    public JSONArray convertJsonNodeToJSONArray(JsonNode jsonNode) {
        JSONArray jsonArray = new JSONArray();

        // Iterate through JsonNode and convert to JSONArray
        for (JsonNode node : jsonNode) {
            JSONObject jsonObject = new JSONObject(node.toString());
            jsonArray.put(jsonObject);
        }

        return jsonArray;
    }

    public String uploadDatabaseFileToS3Bucket(String filePath, String fileName, DatabaseMigrationRequest request) {
        if (isSandbox) {
            LOG.info("Active Profile is Sandbox");
            return "dummyUrl";
        } else {
            String s3FileName = request.organizationPartyId() + "/" + request.studyId() + "/Database/"
                    + generateUniqueFileName(fileName);
            // Create S3 Client
            try {

                // Create PutObjectRequest
                PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(s3FileName)
                        .build();

                // Upload File
                s3Client.putObject(putObjectRequest, Paths.get(filePath + "/" + fileName));
                System.out.println("File uploaded successfully: " + s3FileName);
                URL fileUrl = s3Client.utilities().getUrl(GetUrlRequest.builder()
                        .bucket(bucketName)
                        .key(s3FileName)
                        .build());
                return fileUrl.toString();

            } catch (S3Exception e) {
                System.err.println("S3 Error: " + e.awsErrorDetails().errorMessage());
                LOG.error("Error uploading file to S3: {}", e.getMessage(), e);
                throw new RuntimeException("Error uploading file to S3: " + e.getMessage());

            } catch (Exception e) {
                e.printStackTrace();
                LOG.error("Error uploading file to S3: {}", e.getMessage(), e);
                throw new RuntimeException("Error uploading file to S3: " + e.getMessage());
            }
        }
    }

}
