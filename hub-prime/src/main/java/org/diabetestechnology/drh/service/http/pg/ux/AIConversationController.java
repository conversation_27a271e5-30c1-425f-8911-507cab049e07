package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.request.AIConversationRequest;
import org.diabetestechnology.drh.service.http.pg.service.AIConversationService;
import org.jooq.JSONB;
import org.jooq.exception.IOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@Tag(name = "DRH Hub AI Conversation API Endpoints")
public class AIConversationController {
    private static final Logger LOG = LoggerFactory.getLogger(AIConversationController.class);
    private final AIConversationService aiConversationService;
    private final UserNameService userNameService;
    ObjectMapper objectMapper = new ObjectMapper();

    public AIConversationController(AIConversationService aiConversationService, UserNameService userNameService) {
        this.aiConversationService = aiConversationService;
        this.userNameService = userNameService;
    }

    @PostMapping("/ai-conversation")
    @ResponseBody
    @Operation(summary = "Save AI Conversation")
    public Response saveAIConversation(@RequestBody AIConversationRequest request) {

        LOG.info("Received request to save AI conversation: {}", request);
        final var providerId = userNameService.getUserId();
        final var userPartyId = userNameService.getCurrentUserPartyId();
        LOG.info("Provider ID: {}, User Party ID: {}", providerId, userPartyId);
        AIConversationRequest aiConversationRequest = new AIConversationRequest(
                request.messageJson(),
                providerId,
                request.contextSection(),
                userPartyId);
        JSONB response = aiConversationService.saveAIConversation(aiConversationRequest);
        Map<String, Object> responseMap = Map.of();
        if (response != null) {
            try {
                responseMap = objectMapper.readValue(response.data(), new TypeReference<Map<String, Object>>() {
                });
            } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
                LOG.error("Failed to parse JSON response: {}", response.data(), e);
            } catch (IOException e) {
                LOG.error("IO error while parsing JSON response: {}", response.data(), e);
            }
        }
        if ("Success".equalsIgnoreCase((String) responseMap.get("status"))) {
            return Response.builder()
                    .data(responseMap)
                    .status("success")
                    .message("AI conversation saved successfully.")
                    .errors(null)
                    .build();
        }
        return Response.builder()
                .data(Map.of())
                .status("error")
                .message("")
                .errors(null)
                .build();
    }

    @GetMapping("/ai-conversation")
    @ResponseBody
    @Operation(summary = "Get AI Conversation")
    public Response getAIConversation(@RequestParam String contextSection) {
        final var providerId = userNameService.getUserId();
        JSONB response = aiConversationService.getAIConversation(contextSection, providerId);
        if (response == null) {
            LOG.warn("No AI conversation found for contextSection: {}", contextSection);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("No AI conversation found.")
                    .errors(null)
                    .build();
        } else {

            LOG.info("Received request to get AI conversation for contextSection: {}", contextSection);
            return Response.builder()
                    .data(Map.of("conversations",
                            response))
                    .status("success")
                    .message("AI conversation retrieved successfully.")
                    .errors(null)
                    .build();
        }

    }

}
