package org.diabetestechnology.drh.service.http.pg.aspect;

import org.diabetestechnology.drh.service.http.GitHubUserAuthorizationFilter;
import org.diabetestechnology.drh.service.http.pg.service.UserRoleService;
import org.diabetestechnology.drh.service.http.util.DrhFeature;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.diabetestechnology.drh.service.http.hub.prime.exception.AccessDeniedException;
import org.springframework.stereotype.Component;
import org.togglz.core.manager.FeatureManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;

@Aspect
@Component
public class PermissionAspect {

    private static final Logger LOG = LoggerFactory.getLogger(PermissionAspect.class);
    private final FeatureManager featureManager;

    public PermissionAspect(FeatureManager featureManager) {
        this.featureManager = featureManager;
    }

    @Around("@annotation(requiresPermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, RequiresPermission requiresPermission)
            throws Throwable {

        String resource = requiresPermission.resource();
        List<String> userPermissions = GitHubUserAuthorizationFilter
                .getAuthenticatedUser()
                .map(GitHubUserAuthorizationFilter.AuthenticatedUser::userPermissions)
                .orElse(List.of());

        LOG.debug("Checking permissions for resource: {}", resource);
        LOG.debug("User permissions: {}", userPermissions);

        for (Permission p : requiresPermission.permissions()) {
            String requiredPermission = String.format("%s:%s:%s", resource, p.permissionName(), p.action());
            if (userPermissions.contains(requiredPermission)) {
                LOG.debug("Access granted for permission: {}", requiredPermission);
                return joinPoint.proceed();
            }
        }

        LOG.warn("Access denied. None of the required permissions matched for resource: {}", resource);
        throw new AccessDeniedException("You do not have access to perform this operation.");
    }

    @Around("@annotation(requiresUserPermission)")
    public Object checkUserPermission(ProceedingJoinPoint joinPoint,
            RequiresUserPermission requiresUserPermission)
            throws Throwable {

        String permission = requiresUserPermission.permission();
        List<String> permissions = GitHubUserAuthorizationFilter
                .getAuthenticatedUser()
                .map(GitHubUserAuthorizationFilter.AuthenticatedUser::permissions)
                .orElse(List.of());
        if (permissions.isEmpty()) {
            LOG.warn("No permissions found for the user. Access denied.");
            permissions = UserRoleService.getGuestUserPermissions();

        }
        LOG.debug("Checking for permission: {}", permission);
        LOG.debug("User permissions: {}", permissions);

        if (permissions.contains(permission)) {
            LOG.debug("Access granted for permission: {}", permission);
            return joinPoint.proceed();

        }

        LOG.warn("Access denied. None of the required permissions matched for permission: {}", permission);
        throw new AccessDeniedException("You do not have access to perform this operation.");
    }

    @Around("@annotation(requireFeature)")
    public Object checkFeature(ProceedingJoinPoint joinPoint,
            RequireFeature requireFeature)
            throws Throwable {
        if (featureManager.isActive(DrhFeature.valueOf(requireFeature.feature()))) {
            LOG.debug("Access granted for permission: {}", requireFeature);
            return joinPoint.proceed();

        }

        LOG.warn("Access denied. None of the required feature matched for permission: {}", requireFeature.feature());
        throw new AccessDeniedException("You do not have access to perform this operation.");
    }
}