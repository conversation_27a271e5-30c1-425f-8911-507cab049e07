package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.study.AgpStudyParticipant;
import org.diabetestechnology.drh.service.http.hub.prime.study.ComponentData;
import org.diabetestechnology.drh.service.http.hub.prime.study.Params;
import org.diabetestechnology.drh.service.http.hub.prime.study.StudyParticipant;
import org.diabetestechnology.drh.service.http.hub.prime.study.TimeInRange;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.constant.UserVerificationStatus;
import org.diabetestechnology.drh.service.http.pg.service.MasterService;
import org.diabetestechnology.drh.service.http.pg.service.PractitionerService;
import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyService;

import io.swagger.v3.oas.annotations.Hidden;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.text.DecimalFormat;
import java.time.LocalDate;

import java.time.temporal.ChronoUnit;
import jakarta.servlet.http.HttpServletRequest;

@Controller
public class InvestigatorController {

    private final Presentation presentation;

    private final ResearchStudyService researchStudyService;
    private final PractitionerService practitionerService;
    private final UserNameService userNameService;;
    private final MasterService masterService;

    public InvestigatorController(Presentation presentation, ResearchStudyService researchStudyService,
            PractitionerService practitionerService, UserNameService userNameService, MasterService masterService) {
        this.presentation = presentation;
        this.researchStudyService = researchStudyService;
        this.practitionerService = practitionerService;
        this.userNameService = userNameService;
        this.masterService = masterService;
    }

    private static final Logger LOG = LoggerFactory.getLogger(InvestigatorController.class.getName());

    @GetMapping("dashboard/info")
    public String dashboardFinal(Model model, final HttpServletRequest request) {
        LOG.info("Populate Dashboard");
        return presentation.populateModel("page/investigator/dashboard", model, request);
    }

    @GetMapping("/dashboard")
    public String dashboard() {
        return "redirect:/dashboard/info";
    }

    @GetMapping("userprofile/info")
    public String userprofileFinal(Model model, final HttpServletRequest request) {
        LOG.info("Populate Profile");
        final var providerId = userNameService.getUserId();
        final var statusId = masterService.getUserVerificationStatusId(UserVerificationStatus.COMPLETED);
        final var email = practitionerService.getEmailByUserId(providerId, statusId);
        model.addAttribute("verificationEmail", email);
        LOG.info("Verification Email for Profile : {}", email);
        if (presentation.isAuthenticatedUser()) {
            model.addAttribute("isAuthenticated", true);
            Boolean isProfileCompleted = practitionerService.isUserExists();
            model.addAttribute("isProfileCompleted", isProfileCompleted);
            final Boolean isSuperAdmin = presentation.isSuperAdmin();
            model.addAttribute("isSuperAdmin", isSuperAdmin);

            Boolean isUserVerified = practitionerService.isUserVerified(providerId, statusId);
            LOG.info("User is verified: {}", isUserVerified);
            model.addAttribute("isUserVerified", isUserVerified);
            if (!isUserVerified) {
                return "redirect:/profile/email/info";
            }

            else {
                return presentation.populateModel("page/investigator/profile", model, request);
            }

        } else {
            model.addAttribute("isAuthenticated", false);
            model.addAttribute("isProfileCompleted", false);
            model.addAttribute("isSuperAdmin", false);
            model.addAttribute("isUserVerified", false);
            LOG.info("User is not authenticated, redirecting to verify email page.");
            return "redirect:/profile/email/info";
        }
    }

    @GetMapping("/userprofile")
    public String userprofile() {
        return "redirect:/userprofile/info";
    }

    // Add the new method for detail view
    @Hidden
    @GetMapping("/study/info/{studyId}")
    // @RouteMapping(label = "Study Detail", title = "Studies Details")
    public String studyInfoFinal(@PathVariable String studyId, Model model, final HttpServletRequest request,
            @RequestParam(required = false) String tab) {
        LOG.info("Getting details for studyId: {}", studyId);
        model.addAttribute("studyId", studyId);
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        model.addAttribute("studyDisplayId", studyDisplayId);
        String[] pageDescription = {
                "The Study Detail page features the calculated metrics and lists the participants enrolled in the selected study.",
                "We can navigate to each participant's report by clicking on their individual ID in the row."
        };
        String[] notes = {
                "The loading time for the Participant List may be longer due to data loading. The speed needs to be improved."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("notes", notes);
        model.addAttribute("tab", tab);
        return presentation.populateModel("page/investigator/study", model, request);
    }

    // @RouteMapping(label = "Studies Details", siblingOrder = 60)
    @Hidden
    @GetMapping("/studyinfo/{studyId}")
    public String studyInfo(@PathVariable String studyId) {
        return "redirect:/study/info/{studyId}";
    }

    @GetMapping("study/add")
    public String studyNew(Model model, final HttpServletRequest request) {
        LOG.info("Add New Datacollection");
        return presentation.populateModel("page/investigator/newstudy", model, request);
    }

    @GetMapping("/studyadd")
    public String studyAdd() {
        return "redirect:/study/add";
    }

    @Hidden
    @GetMapping("/studies/create-study")
    // @RouteMapping(label = "My Studies", siblingOrder = 40)
    public String createStudies(Model model, final HttpServletRequest request) {
        return presentation.populateModel("page/investigator/create-study", model, request);
    }

    @Hidden
    @GetMapping("/studyInfo")
    // @RouteMapping(label = "Study Info", siblingOrder = 40)
    public String getStudyInfo(Model model, final HttpServletRequest request) {
        return "page/investigator/fragments/studyInfo";
    }

    @Hidden
    @GetMapping("/participants/add/{studyId}")
    // @RouteMapping(label = "Add Participants", siblingOrder = 40)
    public String addParticipant(@PathVariable String studyId, Model model,
            final HttpServletRequest request, @RequestParam(required = false) String tab) {
        if (!researchStudyService.isStudyOwner(studyId)) {
            LOG.warn("Access denied: User is not the owner of the study.");
            return "redirect:/study/info/" + studyId + "?tab=" + tab;
        }
        model.addAttribute("studyId", studyId);
        model.addAttribute("tab", tab);
        if (researchStudyService.getResearchStudyArchiveStatus(studyId)) {
            LOG.warn("Access denied: Study {} is archived.", studyId);

            Response response = Response.builder()
                    .status("failure")
                    .message("The study is archived; edits and updates are not allowed.")
                    .build();

            model.addAttribute("response", response);

            return presentation.populateModel("page/access-denied", model, request);
        }
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        model.addAttribute("studyDisplayId", studyDisplayId);
        return presentation.populateModel("page/investigator/addParticipant", model, request);
    }

    @Hidden
    @GetMapping("/participants/settings/{studyId}/{participantId}")
    public String updateParticipant(@PathVariable String studyId,
            @PathVariable String participantId,
            Model model, final HttpServletRequest request, @RequestParam(required = false) String tab) {
        if (!researchStudyService.isStudyOwner(studyId)) {
            LOG.warn("Access denied: User is not the owner of the study.");
            return "redirect:/participants/info/" + studyId + "/" + participantId + "?tab=" + tab;
        }
        model.addAttribute("studyId", studyId);
        model.addAttribute("participantId", participantId);
        model.addAttribute("tab", tab);
        if (researchStudyService.getResearchStudyArchiveStatus(studyId)) {
            LOG.warn("Access denied: Study {} is archived.", studyId);

            Response response = Response.builder()
                    .status("failure")
                    .message("The study is archived; edits and updates are not allowed.")
                    .build();

            model.addAttribute("response", response);

            return presentation.populateModel("page/access-denied", model, request);
        }
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        String participantDisplayId = researchStudyService.getParticipantDisplayId(participantId);
        model.addAttribute("studyDisplayId", studyDisplayId);
        model.addAttribute("participantDisplayId", participantDisplayId);
        LOG.info("Getting details for studyId: {} , participantId: {}", studyDisplayId, participantDisplayId);
        return presentation.populateModel("page/investigator/participantSettings", model, request);
    }

    @Hidden
    @GetMapping("/participantadd")
    // @RouteMapping(label = "Add Participants", siblingOrder = 40)
    public String participantAdd(Model model, final HttpServletRequest request) {
        return "redirect:/participants/add";
    }

    @Hidden
    @GetMapping("/research-study/settings/{studyId}")
    public String studySettings(@PathVariable String studyId, Model model,
            final HttpServletRequest request, @RequestParam(required = false) String tab) {
        if (!researchStudyService.isStudyOwner(studyId)) {
            LOG.warn("Access denied: User is not the owner of the study.");
            return "redirect:/study/info/" + studyId + "?tab=" + tab;
        }
        model.addAttribute("studyId", studyId);
        model.addAttribute("tab", tab);
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        model.addAttribute("studyDisplayId", studyDisplayId);
        // model.addAttribute("tabs", getTabsForStudySettings(studyId));
        return presentation.populateModel("page/investigator/studySettings", model,
                request);
    }

    @Hidden
    @GetMapping("/research-study/collaboration/{studyId}")
    public String collabSettings(@PathVariable String studyId, Model model,
            final HttpServletRequest request, @RequestParam(required = false) String tab) {
        if (!researchStudyService.isStudyOwner(studyId)) {
            LOG.warn("Access denied: User is not the owner of the study.");
            return "redirect:/study/info/" + studyId + "?tab=" + tab;
        }
        model.addAttribute("studyId", studyId);
        model.addAttribute("tab", tab);
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        model.addAttribute("studyDisplayId", studyDisplayId);
        return presentation.populateModel("page/investigator/collaborations", model,
                request);
    }

    @Hidden
    @GetMapping("/research-study/publications/{studyId}")
    public String publicationSettings(@PathVariable String studyId, Model model,
            final HttpServletRequest request, @RequestParam(required = false) String tab) {
        if (!researchStudyService.isStudyOwner(studyId)) {
            LOG.warn("Access denied: User is not the owner of the study.");
            return "redirect:/study/info/" + studyId + "?tab=" + tab;
        }
        model.addAttribute("studyId", studyId);
        model.addAttribute("tab", tab);
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        model.addAttribute("studyDisplayId", studyDisplayId);
        return presentation.populateModel("page/investigator/publications", model,
                request);
    }

    @Hidden
    @GetMapping("/participants/cgmdata/{studyId}/{participantId}")
    public String cgmparticipantData(@PathVariable String studyId,
            @PathVariable String participantId,
            Model model, final HttpServletRequest request, @RequestParam(required = false) String tab) {
        model.addAttribute("studyId", studyId);
        model.addAttribute("participantId", participantId);
        model.addAttribute("tab", tab);
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        String participantDisplayId = researchStudyService.getParticipantDisplayId(participantId);

        model.addAttribute("studyDisplayId", studyDisplayId);
        model.addAttribute("participantDisplayId", participantDisplayId);
        LOG.info("Getting details for studyId: {} , participantId: {}", studyDisplayId, participantDisplayId);
        String[] pageDescription = {
                "The Combined CGM Tracing refers to a consolidated dataset of continuous glucose monitoring (CGM) data, collected from multiple participants in a research study. CGM devices track glucose levels at regular intervals throughout the day, providing detailed insights into the participants' glycemic control over time."
        };
        String[] pageSubDescriptionTitle = {
                "In a research study, this combined dataset is crucial for analyzing glucose trends across different participants and understanding overall patterns in response to interventions or treatments. The Combined CGM Tracing dataset typically includes:"
        };
        String[] pageSubDescription = {
                "Date_Time: The timestamp for each CGM reading, formatted uniformly to allow accurate time-based analysis.(YYYY-MM-DD HH:MM:SS)",
                "CGM_Value: The recorded glucose level at each time point, often converted to a standard unit (e.g., mg/dL or mmol/L) and stored as a real number for precise calculations.",

        };
        String[] pageAttributesTitle = {
                "This combined view enables researchers to perform comparative analyses, evaluate glycemic variability, and assess overall glycemic control across participants, which is essential for understanding the efficacy of treatments or interventions in the study. By aggregating data from multiple sources, researchers can identify population-level trends while maintaining the integrity of individual data."
        };

        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("pagesubdescription", pageSubDescription);
        model.addAttribute("pagesubdescriptiontitle", pageSubDescriptionTitle);
        model.addAttribute("pageattributestitle", pageAttributesTitle);
        // model.addAttribute("notes", notes);
        return presentation.populateModel("page/investigator/cgmparticipant", model,
                request);
    }

    @Hidden
    @GetMapping("/participants/info/{studyId}/{participantId}")
    public String participantdetails(@PathVariable String studyId,
            @PathVariable String participantId,
            Model model, @RequestParam(required = false) String tab,
            final HttpServletRequest request) {
        LOG.info("Read Participant  Details");

        model.addAttribute("dateRangeFilter", "'Last 14 days'");
        // Generate sample data (replace with actual data retrieval logic)
        StudyParticipant studyParticipants = new StudyParticipant("DCLP1-001-001", 41, 28.5, "Sample Study", 7.2,
                "Type 2");

        // Add data to the model
        model.addAttribute("study_participants", studyParticipants);
        model.addAttribute("studyId", studyId);
        model.addAttribute("participantId", participantId);
        model.addAttribute("tab", tab);
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        String participantDisplayId = researchStudyService.getParticipantDisplayId(participantId);
        model.addAttribute("studyDisplayId", studyDisplayId);
        model.addAttribute("participantDisplayId", participantDisplayId);
        LOG.info("Getting details for studyId: {} , participantId: {}", studyDisplayId, participantDisplayId);
        // Create params object
        Params params = new Params("DCLP1-001-001");
        model.addAttribute("params", params);

        List<Map<String, LocalDate>> dateRangeValues = List.of(
                new HashMap<String, LocalDate>() {
                    {
                        put("start_date", LocalDate.of(2023, 6, 1));
                        put("end_date", LocalDate.of(2023, 6, 10));
                    }
                });
        LocalDate startDate = LocalDate.of(2023, 6, 1);
        LocalDate endDate = LocalDate.of(2023, 6, 10);

        // Calculate the difference in days
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);

        // Format the number of days as an integer
        String formattedDays = String.format("%d", daysBetween);

        model.addAttribute("daysBetween", formattedDays);

        // Sample data for percentage_of_time
        Map<String, String> percentageOfTime = Map.of("percentage_active", "75");

        // Sample data for no_of_days_worn
        Map<String, String> noOfDaysWorn = Map.of("Number_of_Days_CGM_Worn", "28");

        // Sample data for mean_glucose_for_each_patient
        Map<String, String> meanGlucoseForEachPatient = Map.of("MeanGlucose", "150");

        // Sample data for indicator
        Map<String, String> indicator = Map.of("GMI", "7.0");

        // Sample data for glycemic_variability_per_patient
        Map<String, String> glycemicVariabilityPerPatient = Map.of("coefficient_of_variation", "30");

        model.addAttribute("date_range_values", dateRangeValues);
        model.addAttribute("percentage_of_time", percentageOfTime);
        model.addAttribute("no_of_days_worn", noOfDaysWorn);
        model.addAttribute("mean_glucose_for_each_patient", meanGlucoseForEachPatient);
        model.addAttribute("indicator", indicator);
        model.addAttribute("glycemic_variability_per_patient", glycemicVariabilityPerPatient);

        // Create a DecimalFormat object with one decimal place
        DecimalFormat df = new DecimalFormat("#.0");

        // Format the number to one decimal place
        String formattedNumber1 = df.format(1.111);
        String formattedNumber2 = df.format(8.9455);
        String formattedNumber3 = df.format(88.7);
        String formattedNumber4 = df.format(1.2);
        String formattedNumber5 = df.format(0.2);

        // Example data structure for timeInRangesView
        List<TimeInRange> timeInRangesView = List.of(
                new TimeInRange("Participant1", 250, 12.3, "23h 20m", formattedNumber1),
                new TimeInRange("Participant2", 180, 50.0, "195h 40m", formattedNumber2),
                new TimeInRange("Participant3", 70, 50.0, "1947h 10m", formattedNumber3),
                new TimeInRange("Participant4", 54, 50.0, "25h 20m", formattedNumber4),
                new TimeInRange("Participant5", 22, 50.0, "03h 45m", formattedNumber5)
        // Add more entries as needed
        );

        model.addAttribute("timeInRangesView", timeInRangesView);
        model.addAttribute("isCalculationShown", false);

        List<AgpStudyParticipant> studyParticipantsWithRangeView = List.of(
                new AgpStudyParticipant("70.7", "3.71", "25.1", "0.476", "0.0322", "27.3"));

        model.addAttribute("study_participants_with_range_view", studyParticipantsWithRangeView);
        model.addAttribute("time_in_tight_range", 62.1);

        List<ComponentData> componentsPrimary = List.of(
                new ComponentData("Liability Index", 0.113, "Value 1", "mg/dL"),
                new ComponentData("Hypoglycemic Episodes", 349, "hypoglycemic_episodes", ""),
                new ComponentData("Euglycemic Episodes", 23366.0, "euglycemic_episodes", ""),
                new ComponentData("Hyperglycemic Episodes", 2629, "hyperglycemic_episodes", ""),
                new ComponentData("M Value", 0.00224, "m_value", "mg/dL"),
                new ComponentData("Mean Amplitude", 144, "mean_amplitude", ""),
                new ComponentData("Average Daily Risk Range", 144, "average_daily_risk", "mg/dL"),
                new ComponentData("J Index", 645, "j_index", "mg/dL"),
                new ComponentData("Low Blood Glucose Index", 391116186, "lbgi", ""),
                new ComponentData("High Blood Glucose Index", 24485149.1, "hbgi", "")

        );

        model.addAttribute("componentsPrimary", componentsPrimary);
        List<ComponentData> componentsSecondary = List.of(
                new ComponentData("Glycemic Risk Assessment Diabetes Equation (GRADE)", 7.79, "GRADE", ""),
                new ComponentData("Continuous Overall Net Glycemic Action (CONGA)", 5.64, "conga_hourly_mean", ""),
                new ComponentData("Mean of Daily Differences", 0.000835136468891167, "mean_daily_difference", "")

        );

        model.addAttribute("componentsSecondary", componentsSecondary);

        // Page description and notes to show on top of the page
        String[] pageDescription = {
                "The Participants Detail page is a comprehensive report that includes glucose statistics, such as the Ambulatory Glucose Profile (AGP), Glycemia Risk Index (GRI), Daily Glucose Profile, and all other metrics data."

        };
        String[] notes = {
                "The loading time for the AGP and GRI is currently a bit high due to data fetching and calculations. It needs optimization.",
                "Need to optimize the UI of the loader for individual metrics and charts."
        };
        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("notes", notes);

        return presentation.populateModel("page/investigator/participantInfo", model, request);
    }

    @Hidden
    @GetMapping("/participants/mealsdata/{studyId}/{participantId}")
    public String mealsparticipantData(@PathVariable String studyId,
            @PathVariable String participantId,
            Model model, final HttpServletRequest request, @RequestParam(required = false) String tab) {
        model.addAttribute("studyId", studyId);
        model.addAttribute("participantId", participantId);
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        model.addAttribute("tab", tab);
        String participantDisplayId = researchStudyService.getParticipantDisplayId(participantId);

        model.addAttribute("studyDisplayId", studyDisplayId);
        model.addAttribute("participantDisplayId", participantDisplayId);
        LOG.info("Getting details for studyId: {} , participantId: {}", studyDisplayId, participantDisplayId);
        // String[] pageDescription = {
        // "This shows key details about meals reported by participants. It helps
        // researchers understand eating patterns by displaying:"
        // };
        String[] pageSubDescriptionTitle = {
                "This shows key details about meals reported by participants. It helps researchers understand eating patterns by displaying:"
        };
        String[] pageSubDescription = {
                "Meal Quantity : How much food or nutrients were consumed during the meal.",
                "Time of Meal : The exact date and time the meal was eaten.",
                "Meal Type : The category of the meal, such as breakfast, lunch, dinner, or snack."

        };
        // String[] pageAttributesTitle = {
        // "This combined view enables researchers to perform comparative analyses,
        // evaluate glycemic variability, and assess overall glycemic control across
        // participants, which is essential for understanding the efficacy of treatments
        // or interventions in the study. By aggregating data from multiple sources,
        // researchers can identify population-level trends while maintaining the
        // integrity of individual data."
        // };

        // model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("pagesubdescription", pageSubDescription);
        model.addAttribute("pagesubdescriptiontitle", pageSubDescriptionTitle);
        // model.addAttribute("pageattributestitle", pageAttributesTitle);
        // model.addAttribute("notes", notes);
        return presentation.populateModel("page/investigator/mealsData", model,
                request);
    }

    @Hidden
    @GetMapping("participants/fitnessdata/{studyId}/{participantId}")
    public String fitnessParticipantData(@PathVariable String studyId,
            @PathVariable String participantId,
            Model model, final HttpServletRequest request, @RequestParam(required = false) String tab) {
        model.addAttribute("studyId", studyId);
        model.addAttribute("participantId", participantId);
        model.addAttribute("tab", tab);
        String studyDisplayId = researchStudyService.getStudyDisplayId(studyId);
        String participantDisplayId = researchStudyService.getParticipantDisplayId(participantId);

        model.addAttribute("studyDisplayId", studyDisplayId);
        model.addAttribute("participantDisplayId", participantDisplayId);
        LOG.info("Getting details for studyId: {} , participantId: {}", studyDisplayId, participantDisplayId);
        String[] pageDescription = {
                "This view presents a summary of participants’ fitness activity based on data collected from their wearable devices. It consolidates various fitness metrics into a single, easy-to-understand table."
        };
        String[] pageSubDescriptionTitle = {
                "For each fitness record, it displays:"
        };
        String[] pageSubDescription = {
                "Steps : The total number of steps taken.",
                "Exercise Minutes : The amount of time spent being physically active.",
                "Calories Burned : The estimated energy burned during activity.",
                "Distance : The total distance traveled (e.g., while walking or running).",
                "Heart Rate : The recorded heart rate in beats per minute."

        };
        String[] pageAttributesTitle = {
                "This data helps track daily activity levels and overall physical fitness trends for each participant, making it easier to analyze health and lifestyle patterns."
        };

        model.addAttribute("pageDescription", pageDescription);
        model.addAttribute("pagesubdescription", pageSubDescription);
        model.addAttribute("pagesubdescriptiontitle", pageSubDescriptionTitle);
        model.addAttribute("pageattributestitle", pageAttributesTitle);
        // model.addAttribute("notes", notes);
        return presentation.populateModel("page/investigator/fitnessData", model,
                request);
    }

    @GetMapping("studydata/view")
    public String studyDataView(Model model, final HttpServletRequest request) {
        LOG.info("Add New Datacollection");
        return presentation.populateModel("page/investigator/studydataview", model, request);
    }

    @GetMapping("/studydataview")
    public String studyData() {
        return "redirect:/studydata/view";
    }

    @GetMapping("role/settings")
    public String roleSettings(Model model, final HttpServletRequest request) {
        LOG.info("Role Settings");
        return presentation.populateModel("page/investigator/rolesettings", model, request);
    }

    @Hidden
    @GetMapping("/publicationInfo")
    // @RouteMapping(label = "Study Info", siblingOrder = 40)
    public String getPublicationInfo(Model model, final HttpServletRequest request) {
        return "page/investigator/fragments/publicationInfo";
    }

    @GetMapping("/verifyEmail")
    public String verifyEmail(Model model, final HttpServletRequest request) {
        LOG.info("Populate Profile");
        final var provider = userNameService.getUserProvider();
        model.addAttribute("userProvider", provider);
        return "redirect:/profile/email/info";
    }

    @GetMapping("/verifyOtp")
    public String verifyOtp(Model model, final HttpServletRequest request) {
        LOG.info("Verify Otp");
        final var statusId = masterService.getUserVerificationStatusId(UserVerificationStatus.PENDING);
        final var providerId = userNameService.getUserId();
        final var email = practitionerService.getEmailByUserId(providerId, statusId);
        model.addAttribute("verificationEmail", email);
        LOG.info("Verfication Email is {}  for userId {}", email, providerId);
        Boolean existingUser = practitionerService.isUserExists();
        if (existingUser) {
            return "redirect:/home?from=verify-otp";
        } else {
            return presentation.populateModel("page/investigator/verifyOtp", model, request);
        }
    }

    @GetMapping("/linking-account")
    public String matchingConfirmation(Model model, final HttpServletRequest request) {
        LOG.info("matchingConfirmation");
        return presentation.populateModel("page/investigator/accountLinking", model, request);
    }

    @GetMapping("/verificationSuccess")
    public String verificationSuccess(Model model, final HttpServletRequest request) {
        LOG.info("verificationSuccess");
        return presentation.populateModel("page/investigator/verifySuccess", model, request);
    }
}
