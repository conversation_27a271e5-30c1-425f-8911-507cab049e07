package org.diabetestechnology.drh.service.http.hub.prime.ux;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

@Component
public class JwtUtils {

    // TODO: move to config / env var
    private final String jwtSecret = System.getenv().getOrDefault("DRH_JWT_SECRET",
            "replace-with-secure-random-and-store-in-env");
    private final long jwtExpirationMs = 24 * 60 * 60 * 1000L; // 1 day

    @SuppressWarnings("deprecation")
    public String generateToken(String username, String authProvider, List<String> roles, List<String> permissions) {
        Claims claims = Jwts.claims().setSubject(username);

        claims.put("provider", authProvider);
        claims.put("roles", roles == null ? List.of() : roles);
        claims.put("permissions", permissions == null ? List.of() : permissions);

        Date now = new Date();
        Date expiry = new Date(now.getTime() + jwtExpirationMs);

        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(now)
                .setExpiration(expiry)
                .signWith(SignatureAlgorithm.HS256, jwtSecret)
                .compact();
    }

    @SuppressWarnings("deprecation")
    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @SuppressWarnings("deprecation")
    public Claims getAllClaimsFromToken(String token) {
        return Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token).getBody();
    }

    public String getUsernameFromToken(String token) {
        return getAllClaimsFromToken(token).getSubject();
    }

    public List<String> getRolesFromToken(String token) {
        Object roles = getAllClaimsFromToken(token).get("roles");
        if (roles instanceof List<?>) {
            return ((List<?>) roles).stream().map(Object::toString).collect(Collectors.toList());
        }
        return List.of();
    }

    @SuppressWarnings("unchecked")
    public List<String> getPermissionsFromToken(String token) {
        Object perms = getAllClaimsFromToken(token).get("permissions");
        if (perms instanceof List<?>) {
            return ((List<?>) perms).stream().map(Object::toString).collect(Collectors.toList());
        }
        return List.of();
    }
}
