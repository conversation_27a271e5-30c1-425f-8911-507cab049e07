package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.service.MealsAndFitnessService;
import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nonnull;

@Controller
@Tag(name = "DRH Hub Meals And Fitness API Endpoints")
public class MealsAndFitnessController {

    private final MealsAndFitnessService mealsAndFitnessService;
    private static final Logger LOG = LoggerFactory.getLogger(MealsAndFitnessController.class);

    private final UserNameService userNameService;
    private final ResearchStudyService researchStudyService;

    public MealsAndFitnessController(
            MealsAndFitnessService mealsAndFitnessService, UserNameService userNameService,
            ResearchStudyService researchStudyService) {
        this.mealsAndFitnessService = mealsAndFitnessService;
        this.userNameService = userNameService;
        this.researchStudyService = researchStudyService;
    }

    @PostMapping(value = "/study-participant/meals-fitness/file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Save a Meals or Fitness File data")
    @ResponseBody
    public Response saveMealsOrFitneddFile(@RequestPart("file") @Nonnull MultipartFile file,
            @Nonnull @RequestParam("studyId") String studyId,
            @Nonnull @RequestParam("organizationPartyId") String organizationPartyId,
            @Nonnull @RequestParam("participantId") String participantId,
            @Nonnull @RequestParam("fileType") String fileType) throws Exception {

        LOG.info("save meals or fitness file request for study {}: ", studyId);
        try {
            final var currentuserPartyId = userNameService.getCurrentUserPartyId();
            final var studyOwner = researchStudyService.getStudyOwner(studyId);
            if (!currentuserPartyId.equals(studyOwner)) {
                LOG.error("User is not authorized to upload file for this study.");
                return Response.builder()
                        .status("error")
                        .message("Access denied: You are not the owner of this study.")
                        .data(Map.of())
                        .errors(null)
                        .build();
            } else {

                Map<String, Object> response = mealsAndFitnessService.saveMealsAndFitnessFile(file, studyId,
                        organizationPartyId,
                        participantId, fileType);
                LOG.info("save meals or fitness file response: {}", response);
                LOG.info("User is authorized to upload file for this study.");

                String status = "success";
                String message = "Meals or Fitness File data saved successfully";
                String error = null;
                if (!response.isEmpty()) {
                    final var resultStatus = response.get("status");
                    LOG.info("Response Status: {}", resultStatus);
                    if ("failure".equals(resultStatus)) {
                        status = "failure";
                        message = "Failed to Save meals or fitness data";
                    } else if ("error".equals(resultStatus)) {
                        status = "failure";
                        message = "Failed to Save meals or fitness data";
                        error = response.get("result").toString();
                    }
                } else {
                    status = "failure";
                    message = "Failed to Save meals or fitness data";
                }
                return Response.builder()
                        .data(response)
                        .status(status)
                        .message(message)
                        .errors(error)
                        .build();
            }
        } catch (IllegalArgumentException e) {
            LOG.error("Validation error: {}", e.getMessage());
            return Response.builder()
                    .status("error")
                    .message(e.getMessage())
                    .errors(e.getMessage())
                    .build();

        } catch (Exception e) {
            LOG.error("Unexpected error while processing file upload", e.getMessage());
            return Response.builder()
                    .status("error")
                    .message("An unexpected error occurred. Please try again later.")
                    .errors(e.getMessage())
                    .build();

        }
    }
}
