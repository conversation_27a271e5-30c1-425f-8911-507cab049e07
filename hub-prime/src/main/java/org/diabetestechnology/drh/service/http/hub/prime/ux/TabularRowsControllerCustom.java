package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCache;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.diabetestechnology.drh.service.http.hub.prime.ObservabilityRequestFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.AuditService;
import org.diabetestechnology.drh.service.http.pg.constant.FileType;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.diabetestechnology.drh.udi.UdiSecondaryDbConfig;
import org.diabetestechnology.drh.pg.udi.auto.jooq.ingress.Tables;
import org.jetbrains.annotations.NotNull;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Nonnull;
import lib.aide.tabular.JooqRowsSupplier;
import lib.aide.tabular.JooqRowsSupplier.TypableTable;
import lib.aide.tabular.TabularRowsRequest;
import lib.aide.tabular.TabularRowsRequest.FilterModel;
import lib.aide.tabular.TabularRowsResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import static org.jooq.impl.DSL.*;
import org.jooq.*;
import org.jooq.Record;

@Controller
@Tag(name = "DRH Hub Custom Tabular Row API Endpoints for AG Grid")
@Hidden
public class TabularRowsControllerCustom {
    static private final Logger LOG = LoggerFactory.getLogger(TabularRowsControllerCustom.class);

    private final UdiSecondaryDbConfig udiPrimeDbConfig;
    private final UserNameService userNameService;
    private final PartyService partyService;

    private final AuditService auditService;
    private final CacheManager cacheManager;
    private final Presentation presentation;

    public TabularRowsControllerCustom(final UdiSecondaryDbConfig udiPrimeDbConfig, AuditService auditService,
            CacheManager cacheManager,
            UserNameService userNameService, PartyService partyService, Presentation presentation) {
        this.udiPrimeDbConfig = udiPrimeDbConfig;
        this.auditService = auditService;
        this.cacheManager = cacheManager;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.presentation = presentation;
    }

    @Operation(summary = "SQL rows from a master table or view for a specific column value")
    @GetMapping(value = {
            "/api/ux/tabular/jooq/drh_stateless_activity_audit/vw_activity_log/{columnName}/{columnValue}.json" })

    @ResponseBody
    public Object tabularRowsCustom(final @PathVariable String columnName,
            final @PathVariable String columnValue) {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "vw_activity_log";

        // Fetch the result using the dynamically determined table and column; if
        // jOOQ-generated types were found, automatic column value mapping will occur
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        Condition activityLogLevelCondition = DSL.or(
                DSL.field("activity_log_level").eq(0),
                DSL.field("activity_log_level").eq(1),
                DSL.field("activity_log_level").eq(2),
                DSL.field("activity_log_level").eq(3),
                DSL.field("activity_log_level").eq(4),
                DSL.field("activity_log_level").eq(8));
        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where(typableTable.column(columnName).eq(columnValue))
                .and(activityLogLevelCondition);
        // Conditionally add the second WHERE clause if columnName1 and columnValue1 are
        // provided

        return query.fetch().intoMaps();
    }

    @Operation(summary = "Filter rows based on study_display_id")
    @GetMapping(value = {
            "/api/ux/tabular/jooq/{schemaName}/{masterTableNameOrViewName}/study_display_id/{studyDisplayId}.json"
    })
    @ResponseBody
    public Object filterByStudyDisplayId(
            @PathVariable String schemaName,
            @PathVariable String masterTableNameOrViewName,
            @PathVariable String studyDisplayId) {

        // Get table reference dynamically
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(
                Tables.class, schemaName, masterTableNameOrViewName);

        // Define condition for filtering by study_display_id
        Condition studyDisplayCondition = typableTable.column("study_display_id").eq(studyDisplayId);

        // Build and execute query (Removed activity_log_level condition)
        final var query = udiPrimeDbConfig.dsl()
                .selectFrom(typableTable.table())
                .where(studyDisplayCondition);

        return query.fetch().intoMaps();
    }

    @Operation(summary = "SQL distinct activity rows from activity table ")
    @PostMapping(value = {
            "/api/ux/tabular/jooq/distinct/audit/drh_stateless_activity_audit/vw_activity_log.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TabularRowsResponse<?> distinctTabularAuditRows(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            @RequestParam(required = false, defaultValue = "*") String columns,
            final @PathVariable(required = false) String notEqColumnName,
            final @PathVariable(required = false) String notEqColumnValue,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {
        final var schemaName = "drh_stateless_activity_audit";
        final var masterTableNameOrViewName = "vw_activity_log";
        try {
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName.toLowerCase(),
                    masterTableNameOrViewName);
            var bindValues = new ArrayList<Object>();
            Field<String> userNameField = DSL
                    .selectDistinct(DSL.field("name", String.class))
                    .from("drh_stateless_authentication.profile_details_view")
                    .where(DSL.field("party_id").eq(DSL.field("created_by")))
                    .asField("user_name");

            // Parse desired columns if provided (comma-separated)
            List<String> selectedColumns = Arrays.asList(columns.split(","));
            // Prepare the array of selected columns
            var selectedFields = selectedColumns.stream()
                    .map(DSL::field) // Map each column name to field()
                    .toArray(org.jooq.Field[]::new);
            // Create a new array to hold all fields including min created_at
            var allSelectedFields = Arrays.copyOf(selectedFields, selectedFields.length + 2);
            allSelectedFields[selectedFields.length] = userNameField;
            LOG.info("Selected columns: {}", selectedColumns);
            allSelectedFields[selectedFields.length + 1] = min(field("created_at")).as("min_created_at"); // Add
                                                                                                          // minimum
            LOG.info("All selected fields: {}", Arrays.toString(allSelectedFields));
            Condition finalCondition = createCondition(payload.filterModel()); // created_at

            // TO DO: add permission check for admin and non-admin users
            if (userNameService.isSuperAdmin()) {

                final var organizationPartyId = userNameService.getCurrentUserOrganizationPartyId();
                finalCondition = and(finalCondition,
                        DSL.field("organization_party_id").eq(organizationPartyId));
                bindValues.add(organizationPartyId);
                bindValues.add("anonymous");
                finalCondition = or(finalCondition,
                        DSL.field("user_name").isNotNull());
                finalCondition = or(finalCondition,
                        DSL.lower(DSL.field("user_name", String.class)).eq(DSL.value("anonymous")));

                LOG.info("Get Study Details Corresponds to the schema {}:", schemaName.toLowerCase());

            } else if (userNameService.isAdmin()) {
                Condition notSuperAdminCondition = DSL.notExists(
                        DSL.selectOne()
                                .from("drh_stateless_authentication.super_admin_view")
                                .where(DSL.field("party_id").eq(DSL.field("created_by"))));
                final var organizationPartyId = userNameService.getCurrentUserOrganizationPartyId();
                finalCondition = and(finalCondition,
                        DSL.field("organization_party_id").eq(organizationPartyId));
                bindValues.add(organizationPartyId);
                // bindValues.add("anonymous");
                finalCondition = and(finalCondition,
                        DSL.field("created_by").isNotNull());
                finalCondition = finalCondition.and(notSuperAdminCondition);
                // finalCondition = or(finalCondition,
                // DSL.lower(DSL.field("user_name", String.class)).eq(DSL.value("anonymous")));

                LOG.info("Get Study Details Corresponds to the schema {}:", schemaName.toLowerCase());

            } else if (presentation.isAuthenticatedUser()) {
                LOG.info("Applying additional conditions for non-admin user.");
                final var createdBy = userNameService.getCurrentUserPartyId();
                Select<?> sessionUniqueIdSubquery = DSL
                        .selectDistinct(DSL.field("session_unique_id", String.class))
                        .from("drh_stateless_activity_audit.vw_activity_log")
                        .where(DSL.field("created_by").eq(DSL.val(createdBy)));
                // final var userName = userNameService.getUserName();
                if (StringUtils.hasText(createdBy)) {
                    bindValues.add(createdBy);
                    // final var sessionUniqueIds = "(select distinct session_unique_id from
                    // drh_stateless_activity_audit.vw_activity_log where created_by= '"
                    // + createdBy + "')";
                    finalCondition = and(finalCondition,
                            DSL.field("created_by").isNotNull(),
                            DSL.field("session_unique_id").in(sessionUniqueIdSubquery));
                } // else {
                  // finalCondition = and(finalCondition,
                  // DSL.field("user_name").eq(userName));
                  // }
                  // bindValues.add(userName);

                LOG.info("Final condition for non-admin user: {}", finalCondition);

                LOG.info("Get Study Details Corresponds to the schema {}:", schemaName.toLowerCase());

                // bindValues.add(100);

            } else {
                LOG.info("Applying additional conditions for non-admin user.");
                final var userName = userNameService.getUserName();
                final var uniqueSessionId = ObservabilityRequestFilter.uniqueSession;

                LOG.info("Unique Session ID: {}", uniqueSessionId);

                bindValues.add(userName);
                bindValues.add(uniqueSessionId);
                finalCondition = and(finalCondition,
                        // DSL.field("user_name").eq(userName),
                        DSL.field("session_unique_id").eq(uniqueSessionId));
                LOG.info("Final condition for non-admin user: {}", finalCondition);

                LOG.info("Get Study Details Corresponds to the schema {}:", schemaName.toLowerCase());

            }

            // var query = udiPrimeDbConfig.dsl()
            // .select(allSelectedFields)
            // .distinctOn(DSL.field("session_unique_id")) // Use DISTINCT ON for
            // session_unique_id
            // .from(typableTable.table())
            // .where(finalCondition)
            // .groupBy(
            // Stream.concat(
            // selectedColumns.stream().map(typableTable::column), // dynamic fields
            // Stream.of(field("user_name")) // static user_name field
            // ).toArray(Field[]::new))
            // .orderBy(
            // DSL.field("session_unique_id").asc(), // required for DISTINCT ON
            // DSL.field("min_created_at").desc() // additional sorting
            // );

            Table<?> innerQuery = udiPrimeDbConfig.dsl()
                    .select(allSelectedFields)
                    .distinctOn(DSL.field("session_unique_id"))
                    .from(typableTable.table())
                    .where(finalCondition)
                    .groupBy(
                            Stream.concat(
                                    selectedColumns.stream().map(typableTable::column),
                                    Stream.of(field("user_name"))).toArray(Field[]::new))
                    .orderBy(
                            DSL.field("session_unique_id").asc(),
                            DSL.field("min_created_at").desc())
                    .asTable("distinct_session_rows");

            // 2. Outer query: apply final ordering
            var query = udiPrimeDbConfig.dsl()
                    .selectFrom(innerQuery)
                    .orderBy(DSL.field("min_created_at").desc()); // if paginated
            LOG.info("Get Study Details Corresponds to a Query {}:", query.getSQL());
            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName.toLowerCase(), masterTableNameOrViewName,
                            (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (

        DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "SQL Columns from a master table or view for a specific column value")
    @GetMapping(value = {
            "/api/ux/tabular/jooq/column/{schemaName}/{masterTableNameOrViewName}/{columnName}/{columnValue}.json" })

    @ResponseBody
    public Object tabularColumnCustom(final @PathVariable(required = false) String schemaName,
            @RequestParam(required = false, defaultValue = "*") String columns,
            final @PathVariable String masterTableNameOrViewName, final @PathVariable String columnName,
            final @PathVariable String columnValue) {
        // Parse desired columns if provided (comma-separated)
        List<String> selectedColumns = Arrays.asList(columns.split(","));
        // Fetch the result using the dynamically determined table and column; if
        // jOOQ-generated types were found, automatic column value mapping will occur
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName,
                masterTableNameOrViewName);
        final var result = udiPrimeDbConfig.dsl().select(selectedColumns.stream()
                .map(typableTable::column) // dynamically map column names
                .toArray(org.jooq.Field[]::new))
                .from(typableTable.table())
                .where(typableTable.column(columnName).eq(columnValue))
                .fetch()
                .intoMaps();
        if (selectedColumns.contains("activity_data")) {
            auditService.FormatActivityDataResponse(result);
        }
        return result;
    }

    private Condition createCondition(Map<String, FilterModel> filter) {
        List<Condition> conditionsList = new ArrayList<>();

        // Process the filter model
        filter.forEach((field, filterModel) -> {
            // Extract the logical operator if present
            List<Condition> filterConditions = new ArrayList<>();
            if (filterModel.operator() == null) {
                String type = filterModel.type();
                Object filterValue = filterModel.filter();
                Object secondFilter = filterModel.secondFilter();
                // Add individual condition to the list of conditions
                filterConditions.add(individualCondition(field, type, filterValue, secondFilter));
                conditionsList.add(and(filterConditions));

            }

            else if (filterModel.conditions() != null) {
                // Iterate over each condition and create the respective condition
                for (TabularRowsRequest.ConditionsFilterModel conditionModel : filterModel.conditions()) {
                    String type = conditionModel.type();
                    Object filterValue = conditionModel.filter();
                    Object secondFilterValue = conditionModel.secondFilter();
                    // Add individual condition to the list of conditions
                    filterConditions.add(individualCondition(field, type, filterValue, secondFilterValue));
                }
                // Combine conditions using the specified operator (AND/OR)
                Condition combinedCondition;
                if (filterModel.operator().equalsIgnoreCase("AND")) {
                    combinedCondition = and(filterConditions);
                } else if (filterModel.operator().equalsIgnoreCase("OR")) {
                    combinedCondition = or(filterConditions); // Use OR here
                } else {
                    throw new IllegalArgumentException("Unknown operator '" + filterModel.operator() + "'");
                }
                // Add combined condition to conditions list
                conditionsList.add(combinedCondition);
            }

        });

        // Combine all filter conditions into the main query condition
        Condition finalCondition = and(conditionsList); // Use AND for combining all conditions

        return finalCondition;
    }

    private Condition individualCondition(final String field, String type, Object filter, Object secondFilter) {
        // Create individual condition based on filter type
        Condition individualCondition = switch (type) {
            case "like" -> field(field).likeIgnoreCase("%" + filter + "%");
            case "equals" -> field(field).eq(param(field, filter));
            case "notEqual" -> field(field).notEqual(param(field, filter));
            case "number" -> field(field).eq(param(field, filter));
            case "date" -> field(field).eq(param(field, filter));
            case "contains" -> field(field).likeIgnoreCase("%" + filter + "%");
            case "notContains" -> field(field).notLikeIgnoreCase("%" + filter + "%");
            case "startsWith" -> field(field).startsWith(filter);
            case "endsWith" -> field(field).endsWith(filter);
            case "lessOrEqual" -> field(field).lessOrEqual(filter);
            case "greaterOrEqual" -> field(field).greaterOrEqual(filter);
            case "greaterThan" -> field(field).greaterThan(filter);
            case "lessThan" -> field(field).lessThan(filter);
            case "between" -> field(field).cast(SQLDataType.REAL)
                    .between(Float.valueOf(filter.toString()),
                            Float.valueOf(secondFilter.toString()));
            case "blank" -> field(field).cast(SQLDataType.VARCHAR).eq(param(field, ""));
            case "notBlank" -> field(field).cast(SQLDataType.VARCHAR).notEqual(param(field, ""));
            default -> throw new IllegalArgumentException(
                    "Unknown filter type '" + type + "' for field '" + field + "'");
        };
        return individualCondition;

    }

    @Hidden
    @SuppressWarnings("unchecked")
    @Operation(summary = "SQL Custom rows for Study Summary ")
    @PostMapping(value = {
            "/api/ux/tabular/jooq/study/{masterTableNameOrViewName}.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TabularRowsResponse<?> customStudyTabularRows(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable(required = false) String schemaName,
            final @PathVariable String masterTableNameOrViewName,
            final @PathVariable(required = false) String notEqColumnName,
            final @PathVariable(required = false) String notEqColumnValue,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        try {
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    masterTableNameOrViewName);
            List<Field<?>> allFields = Arrays.asList(typableTable.table().fields());
            List<Field<?>> selectedFields = new ArrayList<>(allFields);
            var bindValues = new ArrayList<Object>();

            // Create the participant dashboard table
            var participantDashboardTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    "all_participant_dashboard_cached");

            // Create the final condition based on the filter model
            Condition finalCondition = createFilterWhereCondition(payload.filterModel(),
                    participantDashboardTable);
            if (payload.filterModel() != null && !payload.filterModel().isEmpty()) {
                payload.filterModel().forEach((field, filter) -> {
                    selectedFields.add(createFilterCondition(field, filter.type(), filter.filter(),
                            filter.secondFilter()));

                });
            }
            // Construct the jOOQ query
            var query = udiPrimeDbConfig.dsl()
                    .select(selectedFields) // Selecting the specified fields
                    .from(typableTable.table())
                    .leftJoin(participantDashboardTable.table()) // Join with the participant dashboard table
                    .on(typableTable.column("study_id").eq(participantDashboardTable.column("study_id")))
                    .where(finalCondition)
                    .groupBy(field("all_study_summary_cached.study_id")); // Optionally order by created_at or other
                                                                          // fields
            if (payload.sortModel() != null && !payload.sortModel().isEmpty()) {
                query = (@NotNull SelectHavingStep<Record>) query.orderBy(createSortCondition(payload.sortModel(),
                        participantDashboardTable));
            }

            LOG.info("Get Activity Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Activity Details Corresponds to a Query {}:", query);

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName, masterTableNameOrViewName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (

        DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    private Field<String> createFilterCondition(String field, String type, Object filter,
            Object secondFilter) {
        Field<String> totalParticipantPercentage;
        switch (type) {
            // Create the total participant percentage field
            case "equals":
                @Nonnull
                @NotNull
                Field<String> totalParticipantPercentageEq = DSL
                        .concat(DSL
                                .round(DSL
                                        .countDistinct(
                                                when(field("study_participant_dashboard_metrics_view." + field)
                                                        .eq(filter),
                                                        field("study_participant_dashboard_metrics_view.participant_id")))
                                        .cast(Double.class)
                                        .mul(inline(100.0).cast(Double.class))
                                        .div(countDistinct(
                                                field("study_participant_dashboard_metrics_view.participant_id"))
                                                .cast(Double.class)),
                                        inline(2))
                                .cast(String.class), // cast to String for concatenation
                                inline("%") // append "%" symbol
                        )
                        .as(field);
                // selectedFields.add(totalParticipantPercentageEq);
                totalParticipantPercentage = totalParticipantPercentageEq;

                break;
            case "notEqual":
                @Nonnull
                Field<String> totalParticipantPercentageNotEq = DSL
                        .concat(DSL
                                .round(DSL
                                        .countDistinct(
                                                when(
                                                        field("study_participant_dashboard_metrics_view." + field)
                                                                .notEqual(filter),
                                                        field("study_participant_dashboard_metrics_view.participant_id")))
                                        .cast(Double.class)
                                        .mul(inline(100.0).cast(Double.class))
                                        .div(countDistinct(
                                                field("study_participant_dashboard_metrics_view.participant_id"))
                                                .cast(Double.class)),
                                        inline(2))
                                .cast(String.class), // cast to String for concatenation
                                inline("%") // append "%" symbol
                        )
                        .as(field);
                totalParticipantPercentage = totalParticipantPercentageNotEq;
                break;
            case "lessOrEqual":
                @Nonnull
                Field<String> totalParticipantPercentageLte = DSL
                        .concat(DSL
                                .round(DSL
                                        .countDistinct(
                                                when(field("study_participant_dashboard_metrics_view." + field)
                                                        .le(filter),
                                                        field("study_participant_dashboard_metrics_view.participant_id")))
                                        .cast(Double.class)
                                        .mul(inline(100.0).cast(Double.class))
                                        .div(countDistinct(
                                                field("study_participant_dashboard_metrics_view.participant_id"))
                                                .cast(Double.class)),
                                        inline(2))
                                .cast(String.class), // cast to String for concatenation
                                inline("%") // append "%" symbol
                        )
                        .as(field);
                totalParticipantPercentage = totalParticipantPercentageLte;
                break;
            case "lessThan":
                @Nonnull
                Field<String> totalParticipantPercentageLt = DSL
                        .concat(DSL
                                .round(DSL
                                        .countDistinct(
                                                when(field("study_participant_dashboard_metrics_view." + field)
                                                        .lt(filter),
                                                        field("study_participant_dashboard_metrics_view.participant_id")))
                                        .cast(Double.class)
                                        .mul(inline(100.0).cast(Double.class))
                                        .div(countDistinct(
                                                field("study_participant_dashboard_metrics_view.participant_id"))
                                                .cast(Double.class)),
                                        inline(2))
                                .cast(String.class), // cast to String for concatenation
                                inline("%") // append "%" symbol
                        )
                        .as(field);
                totalParticipantPercentage = totalParticipantPercentageLt;
                break;
            case "greatersOrEqual":
                @Nonnull
                Field<String> totalParticipantPercentageGte = DSL
                        .concat(DSL
                                .round(DSL
                                        .countDistinct(
                                                when(field("study_participant_dashboard_metrics_view." + field)
                                                        .ge(filter),
                                                        field("study_participant_dashboard_metrics_view.participant_id")))
                                        .cast(Double.class)
                                        .mul(inline(100.0).cast(Double.class))
                                        .div(countDistinct(
                                                field("study_participant_dashboard_metrics_view.participant_id"))
                                                .cast(Double.class)),
                                        inline(2))
                                .cast(String.class), // cast to String for concatenation
                                inline("%") // append "%" symbol
                        )
                        .as(field);
                totalParticipantPercentage = totalParticipantPercentageGte;
                break;
            case "greaterThan":
                @Nonnull
                Field<String> totalParticipantPercentageGt = DSL
                        .concat(DSL
                                .round(DSL
                                        .countDistinct(
                                                when(field("study_participant_dashboard_metrics_view." + field)
                                                        .gt(filter),
                                                        field("study_participant_dashboard_metrics_view.participant_id")))
                                        .cast(Double.class)
                                        .mul(inline(100.0).cast(Double.class))
                                        .div(countDistinct(
                                                field("study_participant_dashboard_metrics_view.participant_id"))
                                                .cast(Double.class)),
                                        inline(2))
                                .cast(String.class), // cast to String for concatenation
                                inline("%") // append "%" symbol
                        )
                        .as(field);
                totalParticipantPercentage = totalParticipantPercentageGt;
                break;
            case "between":
                @Nonnull
                Field<String> totalParticipantPercentageBtw = DSL
                        .concat(DSL
                                .round(DSL
                                        .countDistinct(when(
                                                field("study_participant_dashboard_metrics_view." + field).between(
                                                        filter,
                                                        secondFilter),
                                                field("study_participant_dashboard_metrics_view.participant_id")))
                                        .cast(Double.class)
                                        .mul(inline(100.0).cast(Double.class))
                                        .div(countDistinct(
                                                field("study_participant_dashboard_metrics_view.participant_id"))
                                                .cast(Double.class)),
                                        inline(2))
                                .cast(String.class), // cast to String for concatenation
                                inline("%") // append "%" symbol
                        )
                        .as(field);
                totalParticipantPercentage = totalParticipantPercentageBtw;
                break;
            default:
                throw new IllegalArgumentException(
                        "Unknown filter type '" + type + "' in filter for field '" + field
                                + "' see JooqRowsSupplier::createCondition");
        }
        ;
        return totalParticipantPercentage;

    }

    private @NotNull SortField<Object> createSortCondition(
            List<lib.aide.tabular.TabularRowsRequest.SortModel> sortModel,
            TypableTable participantDashboardTable) {
        for (final var sort : sortModel) {
            final var sortField = participantDashboardTable.column(sort.colId());
            if ((sort.sort()).equals("asc")) {
                return field(sortField).asc();
            } else if ((sort.sort()).equals("desc")) {
                return field(sortField).desc();
            } else {
                LOG.info("Not a Valid Sort Field");
            }
        }
        return null;
    }

    private Condition createFilterWhereCondition(Map<String, FilterModel> filterModel,
            TypableTable participantDashboardTable) {

        List<Condition> conditions = new ArrayList<>();

        // Combine all conditions into a single Condition using AND
        return conditions.isEmpty() ? trueCondition() : and(conditions);
    }

    @Hidden
    @Operation(summary = "Fetch participant data from participant_data_view")
    @PostMapping(value = "/api/ux/tabular/jooq/participant/{schemaName}/{viewName}.json", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TabularRowsResponse<?> fetchParticipantData(
            @RequestParam(name = "studyId") String studyId,
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable String schemaName,
            final @PathVariable String viewName,
            @RequestParam(required = false, defaultValue = "*") String columns,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        try {
            var bindValues = new ArrayList<Object>();
            bindValues.add(studyId); // Bind studyId as a parameter

            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName.toLowerCase(),
                    viewName);

            // Parse desired columns if provided (comma-separated)
            List<String> selectedColumns = Arrays.asList(columns.split(","));
            var selectedFields = selectedColumns.stream()
                    .map(DSL::field) // Convert column names to jOOQ fields
                    .toArray(org.jooq.Field[]::new);

            Condition condition = DSL.field("study_id").eq(DSL.val(studyId));

            // Construct the jOOQ query
            var query = udiPrimeDbConfig.dsl()
                    .select(selectedFields)
                    .from(typableTable.table())
                    .where(condition)
                    .orderBy(DSL.field("participant_id"));

            LOG.info("Fetching participant data for schema: {} and studyId: {}", schemaName.toLowerCase(), studyId);
            LOG.info("Generated SQL Query: {}", query.getSQL());

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName.toLowerCase(), viewName, query, bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (DataAccessException e) {
            LOG.error("Error executing SQL query for schema '{}' and studyId '{}': {}", schemaName, studyId,
                    e.getMessage(), e);
            throw new RuntimeException("Error fetching participant data for studyId '" + studyId + "'", e);
        }
    }

    @GetMapping("/cache")
    @Operation(summary = "Get cache details")
    @ResponseBody
    public String getMethodName() {
        for (String cacheName : cacheManager.getCacheNames()) {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache instanceof ConcurrentMapCache concurrentMapCache) {
                ConcurrentMap<Object, Object> nativeCache = (ConcurrentMap<Object, Object>) concurrentMapCache
                        .getNativeCache();
                System.out.println("Cache Name: " + cacheName);
                System.out.println("Stored Keys: " + nativeCache.keySet());
            }
        }
        return "Success";
    }

    @Hidden
    @Operation(summary = "SQL CustomPopulation Dashboard Summary ")
    @PostMapping(value = {
            "/api/ux/tabular/jooq/study/population/dashboard_all_research_study_view.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TabularRowsResponse<?> customPopulationTabularRows(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        final var schemaName = "drh_stateless_research_study";
        try {
            final var masterTableNameOrViewName = "dashboard_population_research_study_view";
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    masterTableNameOrViewName);
            // Define the specific column names you need
            List<String> columnNames = Arrays.asList("study_id", "study_display_id", "title"); // Replace with actual
                                                                                               // column
            // names

            // Convert column names to jOOQ Field objects
            List<Field<?>> selectedFields = columnNames.stream()
                    .map(col -> DSL.field(DSL.name(schemaName, masterTableNameOrViewName, col)))
                    .collect(Collectors.toList());

            LOG.info("Typable Table: {}", typableTable.table());
            LOG.info("Table Name: {}", typableTable.table().getName());
            LOG.info("Schema: {}", typableTable.table().getSchema());
            LOG.info("Fields: {}", Arrays.toString(typableTable.table().fields()));

            var bindValues = new ArrayList<Object>();

            // Create the participant dashboard table
            var participantMetricsTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName,
                    "study_participant_dashboard_metrics_view");

            // Create the final condition based on the filter model
            Condition finalCondition = createFilterWhereCondition(payload.filterModel(),
                    participantMetricsTable);
            if (payload.filterModel() != null && !payload.filterModel().isEmpty()) {
                payload.filterModel().forEach((field, filter) -> {
                    selectedFields.add(createFilterCondition(field, filter.type(),
                            filter.filter(),
                            filter.secondFilter()));

                });
            }

            final var userId = userNameService.getUserId();
            final var userPartyId = partyService.getPartyIdByUserId(userId);
            final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
            LOG.info("Get Study Details Corresponds to the userPartyId: {}, organizationPartyId: {}", userPartyId,
                    organizationPartyId);
            Condition condition = DSL
                    .or(
                            DSL.field("visibility_name").eq("Public"),
                            DSL.field("study_participant_dashboard_metrics_view.organization_party_id")
                                    .eq(organizationPartyId),
                            DSL.and(
                                    DSL.field("visibility_name").eq("Private"),
                                    DSL.field("created_by").eq(userPartyId)))
                    .and(DSL.field("deleted_at").isNull())
                    .and(DSL.field("rec_status_id").eq(1))
                    .and(
                            DSL.or(
                                    DSL.field("visibility").notEqual("Private"),
                                    DSL.field("created_by").eq(userPartyId)));
            finalCondition = finalCondition == null ? condition : finalCondition.and(condition);
            bindValues.add("Public");
            bindValues.add(organizationPartyId);
            bindValues.add("Private");
            bindValues.add(userPartyId);
            bindValues.add(1);
            bindValues.add("Private");
            bindValues.add(userPartyId);

            // Construct the jOOQ query
            var query = udiPrimeDbConfig.dsl()
                    .select(selectedFields) // Selecting the specified fields
                    .from(typableTable.table())
                    .leftJoin(participantMetricsTable.table()) // Join with the participantdashboard table
                    .on(field("dashboard_population_research_study_view.study_id")
                            .eq(field("study_participant_dashboard_metrics_view.study_id")))

                    .where(finalCondition);
            // .groupBy(field("dashboard_population_research_study_view.study_id")); //
            // Optionally order by
            // created_at or
            // other
            // fields
            // if (payload.sortModel() != null && !payload.sortModel().isEmpty()) {
            // query = (@NotNull SelectHavingStep<Record>)
            // query.orderBy(createSortCondition(payload.sortModel(),
            // participantMetricsTable));
            // }

            LOG.info("Get Study Details Corresponds to the schema {}:", schemaName);
            LOG.info("Get Study Details Corresponds to a Query {}:", query.getSQL());

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName, masterTableNameOrViewName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();

        } catch (

        DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "SQL All Study Summary ")
    @PostMapping(value = {
            "/api/ux/tabular/jooq/research-study/dashboard/custom_dashboard_all_research_study_view.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Hidden
    public TabularRowsResponse<?> customAllStudyDashboardTabularRows(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        try {
            final var userId = userNameService.getUserId();
            final var userPartyId = partyService.getPartyIdByUserId(userId);
            final List<Map<String, Object>> result = new ArrayList<>();
            // Correct way to call a table-valued function in PostgreSQL via jOOQ
            udiPrimeDbConfig.dsl().transaction(configuration -> {
                DSLContext ctx = DSL.using(configuration);
                result.addAll(ctx
                        .select()
                        .from(DSL.table("drh_stateless_research_study.get_all_studies_test({0})", DSL.val(userPartyId)))
                        .fetchMaps());

            });

            // Log query execution (if required)
            if (includeGeneratedSqlInResp) {
                LOG.info("Executed query: SELECT * FROM drh_stateless_research_study.get_all_studies_test(?)");
                LOG.info("Query parameters: {}", userPartyId);
            }

            // Construct and return response
            return new TabularRowsResponse<>(
                    includeGeneratedSqlInResp ? "SELECT * FROM drh_stateless_research_study.get_all_studies_test(?)"
                            : null,
                    result,
                    result.size(),
                    null);

        } catch (DataAccessException e) {
            LOG.error("Error executing SQL function: {}", e.getMessage(), e);

            return new TabularRowsResponse<>(
                    includeGeneratedSqlInErrorResp
                            ? "SELECT * FROM drh_stateless_research_study.get_all_studies_test(?)"
                            : null,
                    List.of(),
                    0,
                    "Error fetching data from database");
        }
    }

    @Operation(summary = "SQL distinct file interaction rows from file_interaction_view")
    @PostMapping(value = {
            "/api/ux/tabular/jooq/distinct/file_interaction/{schemaName}/{viewName}.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Hidden
    public TabularRowsResponse<?> distinctFileInteractionRows(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable String schemaName,
            @RequestParam(required = false, defaultValue = "*") String columns,
            final @PathVariable String viewName,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        try {
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName.toLowerCase(), viewName);

            var bindValues = new ArrayList<Object>();

            // Parse columns to select
            List<String> selectedColumns = Arrays.asList(columns.split(","));
            var selectedFields = selectedColumns.stream()
                    .map(DSL::field)
                    .toArray(org.jooq.Field[]::new);

            // Add min(created_at) for ordering
            var allSelectedFields = Arrays.copyOf(selectedFields, selectedFields.length + 1);
            allSelectedFields[selectedFields.length] = min(field("created_at")).as("min_created_at");

            // Apply filters from payload

            Field<String> studyDisplayIdField = field(typableTable.column("study_display_id")).cast(String.class);
            Condition condition = studyDisplayIdField.isNotNull();
            LOG.info("Condition for study_display_id: {}", condition);

            // Apply filters
            Condition finalCondition = condition;

            // Log the final condition before executing the query
            LOG.info("Final WHERE condition: {}", finalCondition);

            // Construct SQL query using jOOQ
            var query = udiPrimeDbConfig.dsl()
                    .select(allSelectedFields)
                    .from(typableTable.table())
                    .where(finalCondition)
                    .groupBy(
                            selectedColumns.stream()
                                    .map(typableTable::column)
                                    .toArray(org.jooq.Field[]::new))
                    .orderBy(field("min_created_at").desc());

            LOG.info("Fetching distinct file interaction rows for schema: {}", schemaName.toLowerCase());
            LOG.info("Generated SQL Query: {}", query.getSQL());

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName.toLowerCase(), viewName, (Query) query, bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();
        } catch (DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "SQL distinct File Interaction rows from activity table ")
    @PostMapping(value = {
            "/api/ux/tabular/jooq/distinct/db/file/{schemaName}/{masterTableNameOrViewName}.json" }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Hidden
    public TabularRowsResponse<?> distinctTabularFileIntractionRowstRows(
            final @RequestBody @Nonnull TabularRowsRequest payload,
            final @PathVariable(required = false) String schemaName,
            @RequestParam(required = false, defaultValue = "*") String columns,
            final @PathVariable String masterTableNameOrViewName,
            final @PathVariable(required = false) String notEqColumnName,
            final @PathVariable(required = false) String notEqColumnValue,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Response", required = false) boolean includeGeneratedSqlInResp,
            @RequestHeader(value = "X-Include-Generated-SQL-In-Error-Response", required = false, defaultValue = "true") boolean includeGeneratedSqlInErrorResp) {

        try {
            final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class,
                    schemaName.toLowerCase(),
                    masterTableNameOrViewName);
            var bindValues = new ArrayList<Object>();
            // Parse desired columns if provided (comma-separated)
            List<String> selectedColumns = Arrays.asList(columns.split(","));
            // Prepare the array of selected columns
            var selectedFields = selectedColumns.stream()
                    .map(DSL::field) // Map each column name to field()
                    .toArray(org.jooq.Field[]::new);
            // Create a new array to hold all fields including min created_at
            var allSelectedFields = Arrays.copyOf(selectedFields, selectedFields.length + 1);
            allSelectedFields[selectedFields.length] = min(field("created_at")).as("min_created_at"); // Add minimum
            Condition finalCondition = createCondition(payload.filterModel()); // created_at
            Field<String> studyDisplayIdField = field("study_display_id", String.class);
            Field<String> fileCategory = field("file_category", String.class);
            Condition condition1 = studyDisplayIdField.isNotNull(); // Ensure it applies
            Condition condition2 = fileCategory.eq(DSL.value(FileType.DATABASE)); // Ensure it applies

            // Apply filters
            finalCondition = DSL.and(condition1);
            finalCondition = DSL.and(condition2);
            bindValues.add(FileType.DATABASE);

            // Log the final condition before executing the query
            LOG.info("Final WHERE condition: {}", finalCondition);
            // Construct the jOOQ query
            var query = udiPrimeDbConfig.dsl()
                    .select(allSelectedFields)
                    .from(typableTable.table())
                    .where(finalCondition)
                    .groupBy(
                            selectedColumns.stream()
                                    .map(typableTable::column) // Dynamically map column names
                                    .toArray(org.jooq.Field[]::new))
                    .orderBy(field("min_created_at").desc());

            LOG.info("Get Study Details Corresponds to the schema {}:", schemaName.toLowerCase());
            LOG.info("Get Study Details Corresponds to a Query {}:", query.getSQL());

            // bindValues.add(100);

            return new JooqRowsSupplier.Builder()
                    .withRequest(payload)
                    .withQuery(Tables.class, schemaName.toLowerCase(), masterTableNameOrViewName, (Query) query,
                            bindValues)
                    .withDSL(udiPrimeDbConfig.dsl())
                    .withLogger(LOG)
                    .includeGeneratedSqlInResp(includeGeneratedSqlInResp)
                    .includeGeneratedSqlInErrorResp(includeGeneratedSqlInErrorResp)
                    .build()
                    .response();
        } catch (

        DataAccessException e) {
            throw new RuntimeException("Error executing SQL query for '" + schemaName + "'", e);
        }
    }

    @Operation(summary = "SQL rows for a sub-grid based on file_category and study_display_id")
    @GetMapping(value = "/api/ux/tabular/jooq/{schemaName}/{viewName}/{file_category}/{fileCategory}/study_id/{studyId}.json")
    @ResponseBody
    @Hidden
    public Object getSubFileGridData(
            final @PathVariable String schemaName,
            final @PathVariable String viewName,
            final @PathVariable String fileCategory,
            final @PathVariable String studyId) {

        // Fetch the result using the dynamically determined table and column
        final var typableTable = JooqRowsSupplier.TypableTable.fromTablesRegistry(Tables.class, schemaName, viewName);

        final var query = udiPrimeDbConfig.dsl().selectFrom(typableTable.table())
                .where(typableTable.column(
                        "file_category").eq(fileCategory))
                .and(typableTable.column("study_id").eq(studyId))
                .orderBy(typableTable.column("created_at").desc());
        LOG.info("Get Study Details Corresponds to the schema {}:", schemaName.toLowerCase());
        LOG.info("Get Study Details Corresponds to the view {}:", viewName);
        LOG.info("Get Study Details Corresponds to the fileCategory {}:", fileCategory);

        LOG.info("Get Study Details Corresponds to a Query {}:", query.getSQL());

        return query.fetch().intoMaps();
    }

}
