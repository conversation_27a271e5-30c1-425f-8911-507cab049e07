package org.diabetestechnology.drh.service.http.hub.prime.service.interaction;

import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.UnknownHostException;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.ObservabilityRequestFilter;
import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogDetails;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogLevel;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.constant.LogMap;
import org.diabetestechnology.drh.service.http.hub.prime.service.request.ActivityLogRequest;
import org.diabetestechnology.drh.service.http.hub.prime.ux.Presentation;
import org.diabetestechnology.drh.service.http.pg.service.MasterService;
import org.diabetestechnology.drh.service.http.pg.service.PartyService;
import org.diabetestechnology.drh.service.http.pg.service.PractitionerService;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

@Service
public class ActivityLogService {

    private static final Logger LOG = LoggerFactory.getLogger(ActivityLogService.class);
    final String alias = "audit";
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final ObservabilityRequestFilter observabilityRequestFilter;

    private final Presentation presentation;

    private final UserNameService userNameService;
    private final PartyService partyService;
    private final DSLContext dsl;
    private final PractitionerService practitionerService;
    private final MasterService masterService;

    public ActivityLogService(ObservabilityRequestFilter observabilityRequestFilter, Presentation presentation,
            UserNameService userNameService,
            PartyService partyService, @Qualifier("secondaryDsl") DSLContext dsl,
            PractitionerService practitionerService, MasterService masterService) {
        this.observabilityRequestFilter = observabilityRequestFilter;
        this.presentation = presentation;
        this.userNameService = userNameService;
        this.partyService = partyService;
        this.dsl = dsl;
        this.practitionerService = practitionerService;
        this.masterService = masterService;
    }

    LogMap logMap = new LogMap();

    public ActivityLog setActivityDataForRequestUrl(ActivityLogRequest activityLogRequest, HttpServletRequest request,
            HttpServletResponse response)
            throws UnsupportedEncodingException, UnknownHostException, InterruptedException {
        ActivityLog activityLog = getAuditDataFromUrlRequestAndResponse(activityLogRequest, request, response);
        LOG.info("Activity Log :{}", activityLog.getRequestUrl());
        return activityLog;
    }

    public HttpServletRequest getCurrentRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
                .getRequest();
    }

    public HttpServletResponse getCurrentResponse() {
        return ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
                .getResponse();
    }

    public void extractSessionContent(HttpServletRequest request) {
        HttpSession session = request.getSession();
        if (session == null) {
            LOG.warn("No session found for the current request.");
            return;
        } else if (session.isNew()) {// Check if the session is new or existing
            System.out.println("A new session was created.");
            return;
        } else {
            System.out.println("Using an existing session.");
        }

        // Print the session ID
        System.out.println("Session ID: " + session.getId());

        // Extract all session attributes
        Enumeration<String> attributeNames = session.getAttributeNames();

        System.out.println("Session Attributes:");
        while (attributeNames.hasMoreElements()) {
            String attributeName = attributeNames.nextElement();
            Object attributeValue = session.getAttribute(attributeName);
            System.out.println(attributeName + ": " + attributeValue);
        }

        // Print session creation and last accessed times
        System.out.println("Session Creation Time: " + session.getCreationTime());
        System.out.println("Last Accessed Time: " + session.getLastAccessedTime());
    }

    public ActivityLog getAuditDataFromRequestAndResponse(String requestUrl)
            throws UnsupportedEncodingException, UnknownHostException, InterruptedException {
        ActivityLog activityLog = new ActivityLog();
        HttpServletRequest request = getCurrentRequest();
        HttpServletResponse response = getCurrentResponse();

        // final var requestUrl = request.getRequestURI();
        final var statusCode = response.getStatus();
        final var httpMethod = request.getMethod();
        final var userAgent = request.getHeader("User-Agent");
        final var localHost = InetAddress.getLocalHost();
        final var ipAddress = localHost.getHostAddress();
        final var provenance = "%s.doFilterInternal".formatted(ActivityLogService.class.getName());
        final var initiator = request.getRemoteUser();
        final var initiatorHost = request.getRemoteHost();
        final var sessionId = request.getSession().getId();
        // extractSessionContent(request);
        final var userId = userNameService.getUserId();
        String userName = userNameService.getUserName();
        final var appVersion = presentation.getVersion().toString();

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("httpMethod", httpMethod);
        dataMap.put("userAgent", userAgent);
        dataMap.put("ipAddress", ipAddress);
        dataMap.put("provenance", provenance);
        dataMap.put("statusCode", statusCode);
        dataMap.put("initiator", initiator);
        dataMap.put("initiatorHost", initiatorHost);
        final var isAuthenticatedUser = userNameService.isAuthenticatedUser();
        if (isAuthenticatedUser) {
            dataMap.put("providerUserId", userId);
            dataMap.put("providerUserName", userName);
        } else {
            dataMap.put("user", "anonymous");
        }

        // Populate auditLog with data from the request
        activityLog.setAppVersion(appVersion);
        activityLog.setCreatedBy(userId);
        activityLog.setSessionId(sessionId);
        activityLog.setIpAddress(ipAddress);
        activityLog.setUserName(userName);
        activityLog.setActivityData(dataMap);
        activityLog.setRequestUrl(requestUrl);
        LOG.info("Request URL:{}", requestUrl);
        Map<String, LogDetails> activityLogMap = logMap.getLogMap();
        LogDetails logDetails = activityLogMap.getOrDefault(requestUrl, null);
        LOG.info("Primary Log Details: {} {}", logDetails, requestUrl);
        if (logDetails == null) {
            LOG.info("Log details are empty for {}.", requestUrl);

            int thirdSlashIndex = -1;
            int slashCount = 0;
            for (int i = 0; i < requestUrl.length(); i++) {
                if (requestUrl.charAt(i) == '/') {
                    slashCount++;
                    if (slashCount == 3) {
                        thirdSlashIndex = i;
                        break;
                    }
                }
            }

            // Extract substring up to and including the 3rd '/'
            String inputUrl = (thirdSlashIndex != -1)
                    ? requestUrl.substring(0, thirdSlashIndex + 1)
                    : requestUrl;
            LOG.info("Fetch Log details for trimmed inputUrl {}.",
                    inputUrl);
            logDetails = activityLogMap.getOrDefault(inputUrl, null);

        }
        if (logDetails != null) {
            LOG.info("Log details are : {} ", logDetails);
            setActivityDataFromLogDetails(logDetails, activityLog);
        } else {
            LOG.info("Log details are empty for {}", requestUrl.substring(0, requestUrl.lastIndexOf("/") + 1));
            // Add View Study Log , View Participant Log
            if (requestUrl.contains("/study/info/")) {
                logDetails = activityLogMap.getOrDefault("/study/info/", null);
            } else if (requestUrl.contains("/participant/info/")) {
                logDetails = activityLogMap.getOrDefault("/participant/info/", null);
            } else if (requestUrl.contains("/uploadStudy/")) {
                logDetails = activityLogMap.getOrDefault("/uploadStudy/", null);
            } else if (requestUrl.contains("/participants/settings/")) {
                logDetails = activityLogMap.getOrDefault("/participants/settings/", null);
            } else if (requestUrl.contains("/research-study/settings/")) {
                logDetails = activityLogMap.getOrDefault("/research-study/settings/", null);
            } else if (requestUrl.contains("/research-study/publications/")) {
                logDetails = activityLogMap.getOrDefault("/publications/", null);
            } else if (requestUrl.contains("/research-study/collaboration/")) {
                logDetails = activityLogMap.getOrDefault("/collaboration/", null);
            } else if (requestUrl.contains("/research-studies/study-data/")) {
                logDetails = activityLogMap.getOrDefault("/research-studies/study-data/", null);
            } else if (requestUrl.contains("/research-studies/archive-status/")) {
                logDetails = activityLogMap.getOrDefault("/research-studies/archive-status/", null);
            } else if ((requestUrl.contains("/research-study/")) &&
                    (requestUrl.contains("/visibility"))) {
                logDetails = activityLogMap.getOrDefault("/research-study/visibility", null);
            } else if (requestUrl.contains("/research-study-participant/")) {
                logDetails = activityLogMap.getOrDefault("/research-study-participant/", null);
            } else if (requestUrl.contains("/participant-settings/")) {
                logDetails = activityLogMap.getOrDefault("/participant-settings/", null);
            } else if (requestUrl.contains("/research-studies/delete-study/")) {
                logDetails = activityLogMap.getOrDefault("/research-studies/delete-study/", null);
            } else {
                activityLog.setActivityLogLevel(LogLevel.DEFAULT);
            }
            if (logDetails != null) {
                setActivityDataFromLogDetails(logDetails, activityLog);
            }
        }
        LOG.info("Log Details after processing: {} {}", logDetails, requestUrl);
        String uniqueSessionId = observabilityRequestFilter.getUniqueSession(requestUrl,
                new ContentCachingRequestWrapper(getCurrentRequest()));
        LOG.info("uniqueSessionId", uniqueSessionId);
        Thread.sleep(500);

        List<Map<String, Object>> results = new ArrayList<>();

        try {
            List<Map<String, Object>> result = dsl.select(
                    DSL.field("hierarchy_path"),
                    DSL.field("activity_hierarchy"))
                    .from("drh_stateful_activity_audit.activity_log")
                    .where(DSL.field("session_id").eq(sessionId))
                    // .and(DSL.field("session_unique_id").eq(uniqueSessionId))
                    .and(DSL.field("activity_level_id").ne(masterService.getMetricActivityLevel(6)))
                    .orderBy(DSL.field("created_at").desc())
                    .limit(1)
                    .fetchMaps();
            results = result;
            // If update is successful, set success flag to true
        } catch (Exception e) {
            // Log the error and retry if the maximum attempts are not reached
            LOG.error("failed to set activity log data: {}", e.getMessage(), e);

        }

        Map<String, Object> heirarchy = results.isEmpty() ? null : results.get(0);
        if (heirarchy != null) {
            if (heirarchy.get("hierarchy_path") != null)
                activityLog.setHierarchyPath((heirarchy.get("hierarchy_path").toString()) + ", "
                        + requestUrl);
            else
                activityLog.setHierarchyPath(requestUrl);
            if (logDetails != null) {
                if (heirarchy.get("activity_hierarchy") != null) {
                    activityLog.setActivityHierarchy(
                            (heirarchy.get("activity_hierarchy").toString()) + ", " +
                                    activityLog.getActivityType());
                } else {
                    activityLog.setActivityHierarchy(activityLog.getActivityType());
                }
            }

        } else {
            if (logDetails != null) {
                activityLog.setHierarchyPath(requestUrl);
                activityLog.setActivityHierarchy(activityLog.getActivityType());
            }

        }
        activityLog.setSessionUniqueId(uniqueSessionId);
        return activityLog;
    }

    private void setActivityDataFromLogDetails(LogDetails logDetails, ActivityLog activityLog) {
        activityLog.setActivityName(logDetails.getActivityName());
        activityLog.setActivityType(logDetails.getActivityType());
        activityLog.setActivityDescription(logDetails.getActivityDescription());
        activityLog.setActivityLogLevel(logDetails.getActivityLogLevel());
    }

    public void saveActivityLog(ActivityLog activityLog)
            throws UnsupportedEncodingException, UnknownHostException, InterruptedException, JsonProcessingException {
        try {
            // LOG.info("Save Activity Log for : {}", activityLog.getHierarchyPath());
            // LOG.info("Save Activity Log for : {}", activityLog.getRequestUrl());
            LOG.info("Save Activity Log for : {}", activityLog);

            String activityJson = prepareActivityJson(activityLog);
            LOG.info("Activity Log JSON: {}", activityJson);

            final var query = dsl.select(DSL.field(
                    "drh_stateless_activity_audit.insert_activity_log(?)", JSONB.class, DSL.cast(DSL.val(
                            activityJson),
                            JSONB.class)));

            // LOG.info("Save Activity Log Query: {}", query);
            JSONB saveActivityResponse = query.fetchOneInto(JSONB.class);
            LOG.info("Save Activity Log Query: {} /n Response: {}", query, saveActivityResponse);
        } catch (JsonProcessingException e) {
            LOG.error("Failed to process JSON for activity log: {}", e.getMessage(), e);
        } catch (DataAccessException e) {
            LOG.error("Failed to save activity log due to data access error: {}", e.getMessage(), e);
        } catch (Exception e) {
            LOG.error("Failed to save activity log due to unexpected error: {}", e.getMessage(), e);
        }

    }

    private ActivityLog getAuditDataFromUrlRequestAndResponse(ActivityLogRequest activityLogRequest,
            HttpServletRequest request,
            HttpServletResponse response)
            throws UnsupportedEncodingException, UnknownHostException, InterruptedException {

        String requestUrl = activityLogRequest.requestUrl();
        LOG.info("Request URL of the activity To be Saved  : {}", requestUrl);
        // final var requestUrl = request.getRequestURI();
        final var statusCode = response.getStatus();
        final var httpMethod = request.getMethod();
        final var userAgent = request.getHeader("User-Agent");
        final var localHost = InetAddress.getLocalHost();
        final var ipAddress = localHost.getHostAddress();
        final var provenance = "%s.doFilterInternal".formatted(ActivityLogService.class.getName());
        final var initiator = request.getRemoteUser();
        final var initiatorHost = request.getRemoteHost();
        final var sessionId = request.getSession().getId();

        final var userId = userNameService.getUserId();
        String userName = userNameService.getUserName();
        final var appVersion = presentation.getVersion().toString();

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("httpMethod", httpMethod);
        dataMap.put("userAgent", userAgent);
        dataMap.put("ipAddress", ipAddress);
        dataMap.put("provenance", provenance);
        dataMap.put("statusCode", statusCode);
        dataMap.put("initiator", initiator);
        dataMap.put("initiatorHost", initiatorHost);
        ActivityLog activityLog = new ActivityLog();
        // Populate auditLog with data from the request
        activityLog.setAppVersion(appVersion);
        activityLog.setCreatedBy(userId);
        activityLog.setSessionId(sessionId);
        activityLog.setIpAddress(ipAddress);
        activityLog.setUserName(userName);
        if (activityLogRequest.requestBody() != null) {
            dataMap.put("requestData", activityLogRequest.requestBody().requestData());
            dataMap.put("responseData", activityLogRequest.requestBody().responseData());
            LOG.info("RequestData {}", activityLogRequest.requestBody().requestData());
            LOG.info("ResponseData {}", activityLogRequest.requestBody().responseData());
        }
        activityLog.setActivityData(dataMap);
        activityLog.setRequestUrl(requestUrl);
        LOG.info("Request URL:{}", requestUrl);
        Map<String, LogDetails> activityLogMap = logMap.getLogMap();
        LOG.info("Request URL to Search in Log Map:{}", requestUrl);

        LogDetails logDetails = activityLogMap.getOrDefault(requestUrl, null);
        LOG.info("Log Details: {} {}", logDetails, requestUrl);

        if (logDetails != null) {
            LOG.info("Log details are : {} ", logDetails);
            setActivityDataFromLogDetails(logDetails, activityLog);
        } else {
            LOG.info("Log details are empty for {}", requestUrl.substring(0, requestUrl.lastIndexOf("/") + 1));
            activityLog.setActivityLogLevel(LogLevel.DEFAULT);
        }
        String uniqueSessionId = observabilityRequestFilter.getUniqueSession(requestUrl,
                new ContentCachingRequestWrapper(getCurrentRequest()));
        LOG.info("uniqueSessionId", uniqueSessionId);
        final var query = dsl.select(
                DSL.field("hierarchy_path"),
                DSL.field("activity_hierarchy"))
                .from("drh_stateful_activity_audit.activity_log")
                .where(DSL.field("session_id").eq(DSL.val(sessionId)))
                .and(DSL.field("session_unique_id").eq(DSL.val(
                        uniqueSessionId)))
                .and(DSL.field("activity_level_id").ne(masterService.getMetricActivityLevel(6)))
                .orderBy(DSL.field("created_at").desc())
                .limit(1);
        LOG.info("Hierarchy Path Query: {}", query);
        List<Map<String, Object>> results = new ArrayList<>();

        try {
            List<Map<String, Object>> result = query.fetchMaps();
            LOG.info("Hierarchy Path Query Result: {}", result);
            results = result;
            // If update is successful, set success flag to true

        } catch (DataAccessException e) {
            LOG.error("Failed to save Activity log due to data access error: {}", e.getMessage(), e);
        } catch (Exception e) {
            LOG.error("Failed to save Activity log due to unexpected error: {}", e.getMessage(), e);
        }

        Map<String, Object> heirarchy = results.isEmpty() ? null : results.get(0);
        if (heirarchy != null) {
            if (heirarchy.get("hierarchy_path") != null)
                activityLog.setHierarchyPath((heirarchy.get("hierarchy_path").toString()) + ", "
                        + requestUrl);
            else
                activityLog.setHierarchyPath(requestUrl);
            if (logDetails != null) {
                if (heirarchy.get("activity_hierarchy") != null) {
                    activityLog.setActivityHierarchy(
                            (heirarchy.get("activity_hierarchy").toString()) + ", " +
                                    activityLog.getActivityType());
                } else {
                    activityLog.setActivityHierarchy(activityLog.getActivityType());
                }
            }

        } else {
            if (logDetails != null) {
                activityLog.setHierarchyPath(requestUrl);
                activityLog.setActivityHierarchy(activityLog.getActivityType());
            }

        }
        activityLog.setSessionUniqueId(uniqueSessionId);
        return activityLog;
    }

    private String prepareActivityJson(ActivityLog activityLog) throws JsonProcessingException {
        ObjectNode jsonNode = objectMapper.createObjectNode();
        final var userId = userNameService.getUserId();
        final var userPartyId = partyService.getPartyIdByUserId(userId);
        final var organizationPartyId = partyService.getOrganizationPartyIdByUser(userId);
        final var uri = activityLog.getRequestUrl();
        // if (activityLog.getActivityType().equalsIgnoreCase("Anonymous Login")) {
        // activityLog.setSessionUniqueId(observabilityRequestFilter.getUniqueSession("/home",
        // new ContentCachingRequestWrapper(getCurrentRequest())));
        // } else {
        final var sessionUniqueId = null == activityLog.getSessionUniqueId()
                || activityLog.getSessionUniqueId().isEmpty()
                || activityLog.getSessionUniqueId().isBlank()
                        ? observabilityRequestFilter.getUniqueSession(uri,
                                new ContentCachingRequestWrapper(getCurrentRequest()))
                        : activityLog.getSessionUniqueId();

        jsonNode.put("session_unique_id", sessionUniqueId);
        // }
        jsonNode.put("activity_name", activityLog.getActivityName());
        jsonNode.put("activity_type_id", masterService.getMetricActivityTypebyTitle(activityLog.getActivityType()));
        jsonNode.put("activity_description", activityLog.getActivityDescription());
        jsonNode.put("activity_hierarchy", activityLog.getActivityHierarchy());
        jsonNode.put("hierarchy_path", activityLog.getHierarchyPath());
        jsonNode.put("request_url", activityLog.getRequestUrl());
        jsonNode.put("organization_party_id", organizationPartyId);
        jsonNode.put("user_name", activityLog.getUserName());
        jsonNode.put("app_version", activityLog.getAppVersion());
        jsonNode.put("session_id", activityLog.getSessionId());
        jsonNode.put("ip_address", activityLog.getIpAddress());
        jsonNode.set("activity_data", objectMapper.valueToTree(activityLog.getActivityData()));
        jsonNode.put("activity_level_id", masterService.getMetricActivityLevel(activityLog.getActivityLogLevel()));
        jsonNode.put("created_by", userPartyId);
        jsonNode.put("updated_by", userPartyId);
        return objectMapper.writeValueAsString(jsonNode);
    }

    public String getCurrentUserOrganizationPartyId() {
        return userNameService.getCurrentUserOrganizationPartyId();
    }

    public void saveLoginActivity(String userId)
            throws UnsupportedEncodingException, UnknownHostException, InterruptedException, JsonProcessingException {
        LOG.info("Saving login activity log for user: {}", userId);
        ActivityLog activityLog = new ActivityLog();
        HttpServletRequest request = getCurrentRequest();
        final var sessionId = request.getSession().getId();
        if (userId.equalsIgnoreCase("Anonymous")) {
            LOG.info("Skipping login activity log for Anonymous user.");
            activityLog.setActivityType("Anonymous Login");
            activityLog.setActivityDescription("Anonymous user login activity");

        } else {
            LOG.info("Saving login activity log for user: {}", userId);
            Boolean existingUser = practitionerService.isUserExists();
            if (existingUser) {
                activityLog.setActivityType("Practitioner Login");
                activityLog.setActivityDescription("Existing user login activity");
            } else {
                activityLog.setActivityType("Guest Login");
                activityLog.setActivityDescription("New user login activity");
            }
        }
        activityLog.setSessionId(sessionId);
        activityLog.setUserName(userId);
        activityLog.setRequestUrl("/home");
        activityLog.setActivityLogLevel(LogLevel.DEFAULT);
        final var localHost = InetAddress.getLocalHost();
        final var ipAddress = localHost.getHostAddress();
        activityLog.setIpAddress(ipAddress);
        final var appVersion = presentation.getVersion().toString();
        activityLog.setAppVersion(appVersion);
        saveActivityLog(activityLog);
    }

    public String getCurrentUserPartyId() {
        return userNameService.getCurrentUserPartyId();
    }

    public String getSessionUniqueIdOfCurrentSession() {
        HttpServletRequest request = getCurrentRequest();
        String requestUrl = request.getRequestURI();
        String uniqueSessionUuid = observabilityRequestFilter.getUniqueSession(requestUrl,
                new ContentCachingRequestWrapper(request));
        LOG.info("Session Unique Id of Current Session: {}", uniqueSessionUuid);
        final var query = dsl.select(DSL.field("id"))
                .from("drh_stateful_activity_audit.session_unique_id_mapping_view")
                .where(DSL.field("session_id").eq(DSL.val(request.getSession().getId())))
                .and(DSL.field("session_unique_id").eq(DSL.val(uniqueSessionUuid)))
                .orderBy(DSL.field("created_at").desc())
                .limit(1);
        LOG.info("Query to fetch session unique id: {}", query);
        final var response = query.fetchOneInto(String.class);
        LOG.info("Session Unique Id of Current Session: {}", response);
        return response;
    }
}
