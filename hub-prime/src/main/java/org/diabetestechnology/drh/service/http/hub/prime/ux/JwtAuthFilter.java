package org.diabetestechnology.drh.service.http.hub.prime.ux;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * Looks for JWT in Authorization header (Bearer ...) or cookie "DRH_JWT".
 * Converts roles -> ROLE_xxx and permissions -> permission strings (no ROLE_ prefix).
 */
public class JwtAuthFilter extends OncePerRequestFilter {

    private final JwtUtils jwtUtils;

    public JwtAuthFilter(JwtUtils jwtUtils) {
        this.jwtUtils = jwtUtils;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain)
            throws ServletException, IOException {

        String token = resolveToken(request);

        if (token != null && jwtUtils.validateToken(token)) {
            String username = jwtUtils.getUsernameFromToken(token);

            // Roles (mapped to ROLE_ prefix)
            List<String> roles = jwtUtils.getRolesFromToken(token);
            var roleAuthorities = roles.stream()
                    .map(r -> {
                        String role = r.startsWith("ROLE_") ? r : "ROLE_" + r;
                        return new SimpleGrantedAuthority(role);
                    })
                    .collect(Collectors.toList());

            // Permissions (use raw permission string as authority)
            List<String> permissions = jwtUtils.getPermissionsFromToken(token);
            var permAuthorities = permissions.stream()
                    // If your permissions are complex strings (e.g., "ADMINISTRATION:Change User
                    // Roles:VIEW"),
                    // extract the canonical permission code (e.g., "READ_USER" or
                    // "CHANGE_USER_ROLES") if you prefer.
                    // Here we assume the permission strings already match the hasAuthority calls
                    // like "READ_USER".
                    .map(SimpleGrantedAuthority::new)
                    .collect(Collectors.toList());

            // combine
            var authorities = roleAuthorities;
            authorities.addAll(permAuthorities);

            var auth = new UsernamePasswordAuthenticationToken(username, null, authorities);
            SecurityContextHolder.getContext().setAuthentication(auth);
        }

        filterChain.doFilter(request, response);
    }

    private String resolveToken(HttpServletRequest request) {
        String header = request.getHeader("Authorization");
        if (header != null && header.startsWith("Bearer ")) {
            return header.substring(7);
        }
        if (request.getCookies() != null) {
            for (Cookie c : request.getCookies()) {
                if ("DRH_JWT".equals(c.getName())) {
                    return c.getValue();
                }
            }
        }
        return null;
    }
}