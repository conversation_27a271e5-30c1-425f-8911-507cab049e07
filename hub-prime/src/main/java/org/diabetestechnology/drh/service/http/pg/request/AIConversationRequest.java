package org.diabetestechnology.drh.service.http.pg.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public record AIConversationRequest(Message messageJson,String authProviderId,String contextSection,String currentUserPartyId){

public record Message(String user,String html,String role,String timestamp,@JsonProperty("userId")String userId){}

// create a constructor that initializes the fields messageJson and
// contextSection
public AIConversationRequest(Message messageJson,String contextSection){this(messageJson,null,contextSection,null);}

}