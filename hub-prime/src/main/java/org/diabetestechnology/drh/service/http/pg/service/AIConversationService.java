package org.diabetestechnology.drh.service.http.pg.service;

import org.diabetestechnology.drh.service.http.pg.request.AIConversationRequest;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;

@Service
public class AIConversationService {
    private static final Logger LOG = LoggerFactory.getLogger(AIConversationService.class);
    private final DSLContext dsl;
    ObjectMapper mapper = new ObjectMapper();

    public AIConversationService(@Qualifier("secondaryDsl") DSLContext dsl) {
        this.dsl = dsl;
    }

    public JSONB saveAIConversation(AIConversationRequest request) {
        try {
            // Implementation for saving the AI conversation
            LOG.info("Saving AI conversation for user: {}", request.currentUserPartyId());
            mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

            String json = mapper.writeValueAsString(request);
            System.out.println(json);
            // This is a placeholder; actual implementation will depend on your database
            // schema and requirements
            final var query = dsl.select(DSL.field(
                    "drh_stateless_ai_insights.save_ai_conversation_log(?)", JSONB.class, DSL.cast(DSL.val(
                            json),
                            JSONB.class)));
            LOG.info("Query to save AI conversation: {}", query);
            final var result = query.fetchOneInto(JSONB.class);
            LOG.info("AI conversation save response: {}", result);
            return result; // Replace with actual JSONB object after saving
        } catch (Exception e) {
            LOG.error("Error saving AI conversation: {}", e.getMessage());
            throw new RuntimeException("Failed to save AI conversation", e);
        }
    }

    public JSONB getAIConversation(String contextSection, String providerId) {
        final var query = dsl.select(DSL.field("jsonb_agg(message_json)", JSONB.class))
                .from("drh_stateless_ai_insights.ai_conversation_log_view")
                .where(DSL.field("context_section").eq(contextSection))
                .and(DSL.field("auth_provider_id").eq(providerId));
        LOG.info("Query to get AI conversation: {}", query);
        final var response = query.fetchOneInto(JSONB.class);
        LOG.info("AI conversation response: {}", response);
        return response;
    }
}
