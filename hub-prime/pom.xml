<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.5</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>org.diabetestechnology.drh</groupId>
	<artifactId>hub-prime</artifactId>
	<version>0.171.0</version>
	<name>DRH Hub (Prime)</name>
	<description>Diabetes Research Hub (Primary)</description>
	<url/>
	<licenses>
		<license>
			<name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0</url>
            <distribution>repo</distribution>
		</license>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>21</java.version>
		<basedir>${project.basedir}</basedir>
		<!-- Replace local SonarQube config with SonarCloud -->
		<!-- <sonar.organization>netspectiveindia</sonar.organization>
		<sonar.host.url>https://sonarcloud.io</sonar.host.url>
		<sonar.projectKey>diabetes-research_polyglot-prime</sonar.projectKey> -->
		<sonar.projectName>${project.name}</sonar.projectName>
		<sonar.projectVersion>${project.version}</sonar.projectVersion>
		<sonar.sources>src/main/java</sonar.sources>
		<sonar.tests>src/test/java</sonar.tests>
		<sonar.java.binaries>${project.build.directory}/classes</sonar.java.binaries>
        <sonar.sourceEncoding>UTF-8</sonar.sourceEncoding>
        <sonar.language>java</sonar.language>
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <sonar.coverage.jacoco.xmlReportPaths>${project.build.directory}/site/jacoco/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>
	</properties>
	<distributionManagement>
		<site>
			<id>github</id>
			<url>scm:git:**************:diabetes-research/polyglot-prime.git</url>
		</site>
	</distributionManagement>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-hateoas</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.thymeleaf.extras</groupId>
			<artifactId>thymeleaf-extras-springsecurity6</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.8.5</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jooq</artifactId>
			<version>3.5.3</version>
		</dependency>
		<dependency>
			<groupId>org.drh.udi.auto</groupId>
			<artifactId>udi-jooq-ingress</artifactId>
			<scope>system</scope>
			<version>1.0</version>
			<systemPath>${basedir}/lib/drh-udi-jooq-ingress.auto.jar</systemPath>
		 </dependency>
		 <dependency>
			<groupId>org.drh.pg.udi.auto</groupId>
			<artifactId>udi-pg-jooq-ingress</artifactId>
			<scope>system</scope>
			<version>1.0</version>
			<systemPath>${basedir}/lib/drh-pg-udi-jooq-ingress.auto.jar</systemPath>
		 </dependency>
		<dependency>
			<groupId>org.xerial</groupId>
 			<artifactId>sqlite-jdbc</artifactId>
			<version>3.49.1.0</version>
		</dependency>
		<!-- PostgreSQL JDBC Driver -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.7.5</version>
		</dependency>
		<!-- <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency> -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.13.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>33.4.8-jre</version>
		</dependency>
		<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
		<dependency>
			<groupId>com.github.bxforce</groupId>
			<artifactId>commons-vfs2-spring-boot-starter</artifactId>
			<version>1.0.1-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>nz.net.ultraq.thymeleaf</groupId>
			<artifactId>thymeleaf-layout-dialect</artifactId>
		</dependency>
		<!-- <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency> -->
		
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-oauth2-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jetbrains</groupId>
			<artifactId>annotations</artifactId>
			<version>26.0.1</version> 
		</dependency>
		<!-- HttpClient -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.14</version>
		</dependency>
		<!-- Jackson -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.18.3</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.18.3</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.18.2</version>
		</dependency>
		 <!-- Apache HttpCore -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.16</version>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20250107</version>
		</dependency>
		<dependency> 
            <groupId>io.micrometer</groupId> 
            <artifactId>micrometer-registry-otlp</artifactId> 
            <version>1.14.6</version> 
        </dependency>
		<dependency> 
            <groupId>io.opentelemetry</groupId> 
            <artifactId>opentelemetry-exporter-otlp</artifactId> 
        </dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-tracing</artifactId>
			<version>1.5.2</version> 
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-tracing-bridge-otel</artifactId>
			<version>1.5.2</version>
		</dependency>
		<dependency> 
			<groupId>com.squareup.okio</groupId> 
			<artifactId>okio</artifactId> 
			<version>3.10.2</version>
		</dependency> 
		
		<dependency>
			<groupId>io.opentelemetry.instrumentation</groupId>
			<artifactId>opentelemetry-logback-appender-1.0</artifactId>
			<version>2.12.0-alpha</version>
		</dependency>
		<dependency>
			<groupId>io.opentelemetry</groupId>
			<artifactId>opentelemetry-exporter-otlp-logs</artifactId>
			<version>1.26.0-alpha</version>
		</dependency>
		<dependency>
			<groupId>io.opentelemetry</groupId>
			<artifactId>opentelemetry-sdk-logs</artifactId>
			<version>1.47.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.4.1</version>
		</dependency>

		<dependency>
			<groupId>io.opentelemetry</groupId>
			<artifactId>opentelemetry-exporter-otlp-logs</artifactId>
			<version>1.26.0-alpha</version>
		</dependency>
		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
			<version>8.0</version>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-core</artifactId>
			<version>1.5.18</version> <!-- Match the logback-classic version -->
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.5.17</version> <!-- Use the latest version -->
		</dependency>
		<dependency>
			<groupId>org.codehaus.jettison</groupId>
			<artifactId>jettison</artifactId>
			<version>1.5.4</version>
		</dependency>
		<dependency>
			<groupId>io.opentelemetry.instrumentation</groupId>
			<artifactId>opentelemetry-logback-appender-1.0</artifactId>
			<version>2.13.3-alpha</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>s3</artifactId>
			<version>2.32.7</version> <!-- or the latest version -->
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>auth</artifactId>
			<version>2.32.7</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
			<version>1.12.787</version>
		</dependency>
		
		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>5.10</version>
		</dependency>
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-core</artifactId>
			<version>3.1.0</version> <!-- Use latest version -->
		</dependency>
 <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>0.11.5</version>
    </dependency>

    <!-- JJWT Implementation -->
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <version>0.11.5</version>
        <scope>runtime</scope>
    </dependency>

    <!-- JJWT Jackson JSON Processor -->
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <version>0.11.5</version>
        <scope>runtime</scope>
    </dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
			<version>1.13.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
       </dependency>
		<dependency>
			<groupId>org.duckdb</groupId>
			<artifactId>duckdb_jdbc</artifactId>
			<version>1.2.0</version>
		</dependency>
		<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
       </dependency>
	   <dependency>
 		   <groupId>org.springframework.boot</groupId>
 		   <artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>1.9.24</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.9.24</version>
		</dependency>

		<dependency>
 		   <groupId>org.springframework.boot</groupId>
		   <artifactId>spring-boot-starter-mail</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>jakarta.mail</artifactId>
			<version>2.0.1</version>
		</dependency>
		<dependency> 
			<groupId>org.togglz</groupId> 
			<artifactId>togglz-console-spring-boot-starter</artifactId> 
			<version>4.0.1</version> 
		</dependency> 
		<dependency> 
			<groupId>org.togglz</groupId> 
			<artifactId>togglz-spring-web</artifactId> 
			<version>4.0.1</version> 
		</dependency>
 </dependencies>
	
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.squareup.okhttp3</groupId>
				<artifactId>okhttp-bom</artifactId>
				<version>5.0.0-alpha.14</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<build>
		<plugins>
			<plugin>
				<groupId>net.revelc.code.formatter</groupId>
				<artifactId>formatter-maven-plugin</artifactId>
				<version>2.25.0</version> <!-- Check for the latest version -->
				<executions>
					<execution>
						<goals>
							<goal>format</goal>
						</goals>
						<phase>process-sources</phase>
					</execution>
				</executions>
				<configuration>
					<configFile>eclipse-formatter.xml</configFile>
					<compilerCompliance>21</compilerCompliance>
					<compilerSource>21</compilerSource>
					<compilerTargetPlatform>21</compilerTargetPlatform>
					<encoding>UTF-8</encoding>
					<removeTrailingWhitespace>false</removeTrailingWhitespace>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-report-plugin</artifactId>
				<version>3.5.1</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-site-plugin</artifactId>
				<version>4.0.0-M16</version>
				<dependencies>
					<dependency>
						<groupId>org.apache.maven.doxia</groupId>
						<artifactId>doxia-module-markdown</artifactId>
						<version>2.0.0</version>
					</dependency>
					<dependency>
						<groupId>org.apache.maven.doxia</groupId>
						<artifactId>doxia-site-renderer</artifactId>
						<version>2.0.0</version>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<id>generate-scm-page</id>
						<phase>site</phase>
						<goals>
							<goal>site</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/site</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- Maven Resources Plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.3.1</version>
				<executions>
					<execution>
						<id>copy-md-files</id>
						<phase>generate-resources</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/site/markdown</outputDirectory>
							<resources>
								<resource>
									<directory>src/site/markdown</directory>
									<includes>
										<include>**/*.md</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.10.0.2594</version>
            </plugin>
			<!-- Add JaCoCo plugin for code coverage -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.11</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
		</plugins>
	</build>
	<reporting>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-report-plugin</artifactId>
				<version>3.5.2</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<version>3.6.0</version>
				<configuration>
					<configLocation>checkstyle.xml</configLocation>
					<outputFile>${project.build.directory}/output-results/checkstyle-result.xml</outputFile>
					<outputFileFormat>xml</outputFileFormat>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-scm-publish-plugin</artifactId>
				<version>3.3.0</version>
				<configuration>
					<scmBranch>gh-pages</scmBranch>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-pmd-plugin</artifactId>
				<version>3.26.0</version>
				<configuration>
					<targetJdk>21</targetJdk>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<!-- Formatter Report Plugin -->
			<plugin>
				<groupId>com.spotify.fmt</groupId>
				<artifactId>fmt-maven-plugin</artifactId>
				<version>2.27</version>
				<configuration>
					<configLocation>formater.xml</configLocation>
					<outputFile>${project.build.directory}/output-results/formater.xml</outputFile>
					<outputFileFormat>xml</outputFileFormat>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<!-- Spotbugs Maven Plugin -->
			<plugin>
				<groupId>com.github.spotbugs</groupId>
				<artifactId>spotbugs-maven-plugin</artifactId>
				<version>4.9.3.2</version>
				<configuration>
					<configLocation>spotbugs.xml</configLocation>
					<excludeFilterFile>spotbug-exclude-filter.xml</excludeFilterFile>
					<outputFile>${project.build.directory}/output-results/spotbugs.xml</outputFile>
					<outputFileFormat>xml</outputFileFormat>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<!-- git-commit-id Maven Plugin -->
			<plugin>
				<groupId>io.github.git-commit-id</groupId>
				<artifactId>git-commit-id-maven-plugin</artifactId>
				<version>9.0.1</version>
				<configuration>
					<configLocation>git-commit-id.xml</configLocation>
					<outputFile>${project.build.directory}/output-results/git-commit-id.xml</outputFile>
					<outputFileFormat>xml</outputFileFormat>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<!-- Modernizer Report Plugin -->
			<plugin>
				<groupId>org.gaul</groupId>
				<artifactId>modernizer-maven-plugin</artifactId>
				<version>3.1.0</version>
				<configuration>
					<configLocation>modernizer.xml</configLocation>
					<outputFile>${project.build.directory}/output-results/modernizer.xml</outputFile>
					<outputFileFormat>xml</outputFileFormat>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<!-- Javadoc Report Plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.11.2</version>
				<configuration>
					<failOnError>false</failOnError>
					<stylesheetfile>${project.build.directory}/site/apidocs/stylesheet.css</stylesheetfile>
					<show>public</show>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-project-info-reports-plugin</artifactId>
				<version>3.9.0</version> <!-- Specify a version -->
			</plugin>
		</plugins>
	</reporting>
	<profiles>
        <profile>
            <id>sonar</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonarsource.scanner.maven</groupId>
                        <artifactId>sonar-maven-plugin</artifactId>
                        <version>3.10.0.2594</version>
                        <executions>
                            <execution>
                                <id>sonar-scan</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sonar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
